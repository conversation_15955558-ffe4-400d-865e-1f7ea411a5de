# Performance Optimization Guide

*Comprehensive performance optimization strategies and best practices for the Valura AI platform.*

## 🎯 Performance Overview

Performance is critical for financial applications where users expect real-time data updates, fast calculations, and responsive interfaces. This guide covers optimization strategies across all layers of our platform.

## 📊 Performance Targets

### Response Time Targets

**Web Application:**
- **Page Load Time:** < 2 seconds (First Contentful Paint)
- **Time to Interactive:** < 3 seconds
- **API Response Time:** < 500ms (95th percentile)
- **Database Queries:** < 100ms (average)

**Mobile Application:**
- **App Launch Time:** < 3 seconds
- **Screen Transitions:** < 200ms
- **API Response Time:** < 1 second
- **Offline Capability:** Core features available

**API Server:**
- **Throughput:** > 1000 requests/second
- **Latency:** < 200ms (median)
- **Error Rate:** < 0.1%
- **Uptime:** 99.9%

## 🚀 Frontend Performance Optimization

### Next.js Optimization

**Code Splitting & Lazy Loading:**
```typescript
// ✅ Good - Dynamic imports for large components
import dynamic from 'next/dynamic';

const PortfolioChart = dynamic(
  () => import('../components/PortfolioChart'),
  {
    loading: () => <ChartSkeleton />,
    ssr: false // Disable SSR for client-only components
  }
);

// ✅ Good - Route-based code splitting
const DashboardPage = dynamic(() => import('../pages/dashboard'));

// ✅ Good - Conditional loading
const AdminPanel = dynamic(
  () => import('../components/AdminPanel'),
  {
    loading: () => <div>Loading admin panel...</div>
  }
);

export function Dashboard({ user }: DashboardProps) {
  return (
    <div>
      <PortfolioChart userId={user.id} />
      {user.role === 'admin' && <AdminPanel />}
    </div>
  );
}
```

**Image Optimization:**
```typescript
// ✅ Good - Next.js Image component with optimization
import Image from 'next/image';

export function UserAvatar({ user }: UserAvatarProps) {
  return (
    <Image
      src={user.avatarUrl}
      alt={`${user.firstName} ${user.lastName}`}
      width={40}
      height={40}
      priority={false} // Only true for above-the-fold images
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
      sizes="(max-width: 768px) 40px, 40px"
    />
  );
}

// ✅ Good - Responsive images
export function HeroImage() {
  return (
    <Image
      src="/hero-image.jpg"
      alt="Valura AI Platform"
      width={1200}
      height={600}
      priority={true} // Above-the-fold image
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  );
}
```

**Bundle Optimization:**
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features for better performance
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@repo/ui', 'lodash'],
  },
  
  // Webpack optimizations
  webpack: (config, { dev, isServer }) => {
    // Production optimizations
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true,
          },
        },
      };
    }
    
    return config;
  },
  
  // Compression
  compress: true,
  
  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
};

module.exports = nextConfig;
```

### React Performance Optimization

**Memoization Strategies:**
```typescript
// ✅ Good - Memoize expensive calculations
import { useMemo, useCallback } from 'react';

export function PortfolioAnalytics({ portfolio }: PortfolioAnalyticsProps) {
  // Memoize expensive calculations
  const portfolioMetrics = useMemo(() => {
    return calculatePortfolioMetrics(portfolio);
  }, [portfolio]);

  // Memoize event handlers
  const handleRefresh = useCallback(async () => {
    await refreshPortfolioData(portfolio.id);
  }, [portfolio.id]);

  // Memoize filtered data
  const highRiskPositions = useMemo(() => {
    return portfolio.positions.filter(position => position.riskLevel === 'high');
  }, [portfolio.positions]);

  return (
    <div>
      <MetricsDisplay metrics={portfolioMetrics} />
      <PositionsList positions={highRiskPositions} />
      <RefreshButton onClick={handleRefresh} />
    </div>
  );
}

// ✅ Good - Memoize components with React.memo
export const PositionRow = React.memo(({ position }: PositionRowProps) => {
  return (
    <tr>
      <td>{position.symbol}</td>
      <td>{position.quantity}</td>
      <td>{formatCurrency(position.marketValue)}</td>
    </tr>
  );
});

// ✅ Good - Custom comparison function for complex objects
export const PortfolioChart = React.memo(
  ({ data }: PortfolioChartProps) => {
    // Chart rendering logic
    return <Chart data={data} />;
  },
  (prevProps, nextProps) => {
    // Custom comparison for deep equality
    return JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data);
  }
);
```

**Virtual Scrolling for Large Lists:**
```typescript
// ✅ Good - Virtual scrolling for large datasets
import { FixedSizeList as List } from 'react-window';

interface VirtualizedPortfolioListProps {
  positions: Position[];
}

export function VirtualizedPortfolioList({ positions }: VirtualizedPortfolioListProps) {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <PositionRow position={positions[index]} />
    </div>
  );

  return (
    <List
      height={400} // Container height
      itemCount={positions.length}
      itemSize={50} // Row height
      width="100%"
    >
      {Row}
    </List>
  );
}
```

## 🗄️ Backend Performance Optimization

### Database Optimization

**Query Optimization:**
```typescript
// ✅ Good - Optimized Prisma queries
export class PortfolioService {
  async getPortfolioWithPositions(userId: string): Promise<Portfolio> {
    return await db.portfolio.findFirst({
      where: { userId },
      include: {
        positions: {
          include: {
            security: {
              select: {
                symbol: true,
                name: true,
                currentPrice: true,
                sector: true
              }
            }
          }
        }
      }
    });
  }

  // ✅ Good - Batch queries to avoid N+1 problem
  async getMultiplePortfolios(userIds: string[]): Promise<Portfolio[]> {
    return await db.portfolio.findMany({
      where: {
        userId: { in: userIds }
      },
      include: {
        positions: {
          include: {
            security: true
          }
        }
      }
    });
  }

  // ✅ Good - Pagination for large datasets
  async getPortfolioHistory(
    portfolioId: string,
    page: number = 1,
    limit: number = 50
  ): Promise<{ data: PortfolioSnapshot[]; total: number }> {
    const [data, total] = await Promise.all([
      db.portfolioSnapshot.findMany({
        where: { portfolioId },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      db.portfolioSnapshot.count({
        where: { portfolioId }
      })
    ]);

    return { data, total };
  }
}
```

**Database Indexing:**
```sql
-- ✅ Good - Strategic indexes for common queries
CREATE INDEX CONCURRENTLY idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX CONCURRENTLY idx_positions_portfolio_id ON positions(portfolio_id);
CREATE INDEX CONCURRENTLY idx_positions_symbol ON positions(symbol);
CREATE INDEX CONCURRENTLY idx_orders_user_id_status ON orders(user_id, status);
CREATE INDEX CONCURRENTLY idx_transactions_created_at ON transactions(created_at DESC);

-- ✅ Good - Composite indexes for complex queries
CREATE INDEX CONCURRENTLY idx_positions_portfolio_symbol ON positions(portfolio_id, symbol);
CREATE INDEX CONCURRENTLY idx_orders_user_created ON orders(user_id, created_at DESC);

-- ✅ Good - Partial indexes for filtered queries
CREATE INDEX CONCURRENTLY idx_orders_active ON orders(user_id, created_at) 
WHERE status IN ('pending', 'processing');
```

### Caching Strategies

**Redis Caching:**
```typescript
// ✅ Good - Multi-layer caching strategy
export class CacheService {
  private redis: Redis;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
  }

  // Portfolio data caching
  async getPortfolioValue(portfolioId: string): Promise<number | null> {
    const cacheKey = `portfolio:value:${portfolioId}`;
    const cached = await this.redis.get(cacheKey);
    
    if (cached) {
      return parseFloat(cached);
    }

    // Calculate and cache for 5 minutes
    const value = await this.calculatePortfolioValue(portfolioId);
    await this.redis.setex(cacheKey, 300, value.toString());
    
    return value;
  }

  // Market data caching
  async getMarketData(symbol: string): Promise<MarketData | null> {
    const cacheKey = `market:${symbol}`;
    const cached = await this.redis.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }

    const marketData = await this.fetchMarketData(symbol);
    // Cache for 1 minute (market data changes frequently)
    await this.redis.setex(cacheKey, 60, JSON.stringify(marketData));
    
    return marketData;
  }

  // User session caching
  async cacheUserSession(userId: string, sessionData: UserSession): Promise<void> {
    const cacheKey = `session:${userId}`;
    await this.redis.setex(cacheKey, 3600, JSON.stringify(sessionData)); // 1 hour
  }

  // Cache invalidation
  async invalidatePortfolioCache(portfolioId: string): Promise<void> {
    const pattern = `portfolio:*:${portfolioId}`;
    const keys = await this.redis.keys(pattern);
    
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
```

**Application-Level Caching:**
```typescript
// ✅ Good - In-memory caching for frequently accessed data
export class MarketDataService {
  private cache = new Map<string, { data: MarketData; timestamp: number }>();
  private readonly CACHE_TTL = 60000; // 1 minute

  async getMarketData(symbol: string): Promise<MarketData> {
    const cached = this.cache.get(symbol);
    const now = Date.now();

    // Return cached data if still valid
    if (cached && (now - cached.timestamp) < this.CACHE_TTL) {
      return cached.data;
    }

    // Fetch fresh data
    const data = await this.fetchFromExternalAPI(symbol);
    
    // Update cache
    this.cache.set(symbol, { data, timestamp: now });
    
    // Clean up old entries
    this.cleanupCache();
    
    return data;
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if ((now - value.timestamp) > this.CACHE_TTL) {
        this.cache.delete(key);
      }
    }
  }
}
```

### API Performance

**Response Optimization:**
```typescript
// ✅ Good - Efficient API responses
export class PortfolioController {
  async getPortfolio(req: Request, res: Response): Promise<void> {
    const { portfolioId } = req.params;
    const { includeHistory = false } = req.query;

    try {
      // Base portfolio data
      const portfolio = await portfolioService.getPortfolio(portfolioId);
      
      // Conditional data loading
      const response: any = {
        ...portfolio,
        positions: portfolio.positions.map(position => ({
          id: position.id,
          symbol: position.symbol,
          quantity: position.quantity,
          marketValue: position.marketValue,
          // Only include detailed data if needed
          ...(includeHistory && { history: position.history })
        }))
      };

      // Set appropriate cache headers
      res.set({
        'Cache-Control': 'private, max-age=300', // 5 minutes
        'ETag': generateETag(response),
        'Last-Modified': portfolio.updatedAt.toUTCString()
      });

      res.json({
        success: true,
        data: response,
        meta: {
          timestamp: new Date().toISOString(),
          cached: false
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: { message: 'Failed to fetch portfolio' }
      });
    }
  }

  // ✅ Good - Streaming for large datasets
  async exportPortfolioData(req: Request, res: Response): Promise<void> {
    const { portfolioId } = req.params;
    
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Transfer-Encoding', 'chunked');
    
    const stream = portfolioService.getPortfolioDataStream(portfolioId);
    
    res.write('{"data":[');
    
    let first = true;
    for await (const chunk of stream) {
      if (!first) res.write(',');
      res.write(JSON.stringify(chunk));
      first = false;
    }
    
    res.write(']}');
    res.end();
  }
}
```

## 📱 Mobile Performance Optimization

### React Native Optimization

**List Performance:**
```typescript
// ✅ Good - Optimized FlatList for large datasets
export function PortfolioPositionsList({ positions }: PositionsListProps) {
  const renderPosition = useCallback(({ item }: { item: Position }) => (
    <PositionItem position={item} />
  ), []);

  const keyExtractor = useCallback((item: Position) => item.id, []);

  const getItemLayout = useCallback(
    (data: Position[] | null | undefined, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    []
  );

  return (
    <FlatList
      data={positions}
      renderItem={renderPosition}
      keyExtractor={keyExtractor}
      getItemLayout={getItemLayout}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      updateCellsBatchingPeriod={50}
      initialNumToRender={10}
      windowSize={10}
    />
  );
}

// ✅ Good - Memoized list items
export const PositionItem = React.memo(({ position }: PositionItemProps) => {
  return (
    <View style={styles.container}>
      <Text style={styles.symbol}>{position.symbol}</Text>
      <Text style={styles.value}>{formatCurrency(position.marketValue)}</Text>
    </View>
  );
});
```

**Image Optimization:**
```typescript
// ✅ Good - Optimized image loading
import FastImage from 'react-native-fast-image';

export function UserAvatar({ user }: UserAvatarProps) {
  return (
    <FastImage
      style={styles.avatar}
      source={{
        uri: user.avatarUrl,
        priority: FastImage.priority.normal,
        cache: FastImage.cacheControl.immutable,
      }}
      resizeMode={FastImage.resizeMode.cover}
      fallback={true}
    />
  );
}
```

## 🔍 Performance Monitoring

### Metrics Collection

**Frontend Monitoring:**
```typescript
// ✅ Good - Performance metrics collection
export class PerformanceMonitor {
  static measurePageLoad(pageName: string): void {
    // Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navigationEntry = entry as PerformanceNavigationTiming;
          
          const metrics = {
            page: pageName,
            loadTime: navigationEntry.loadEventEnd - navigationEntry.loadEventStart,
            domContentLoaded: navigationEntry.domContentLoadedEventEnd - navigationEntry.domContentLoadedEventStart,
            firstContentfulPaint: this.getFCP(),
            largestContentfulPaint: this.getLCP(),
            cumulativeLayoutShift: this.getCLS(),
            firstInputDelay: this.getFID(),
          };
          
          // Send to analytics
          this.sendMetrics(metrics);
        }
      }
    });
    
    observer.observe({ entryTypes: ['navigation'] });
  }

  static measureAPICall(endpoint: string, duration: number, success: boolean): void {
    const metrics = {
      endpoint,
      duration,
      success,
      timestamp: Date.now(),
    };
    
    // Send to monitoring service
    this.sendAPIMetrics(metrics);
  }

  private static getFCP(): number {
    const entries = performance.getEntriesByName('first-contentful-paint');
    return entries.length > 0 ? entries[0].startTime : 0;
  }

  private static getLCP(): number {
    return new Promise((resolve) => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        resolve(lastEntry.startTime);
      });
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    });
  }
}
```

**Backend Monitoring:**
```typescript
// ✅ Good - API performance monitoring
export const performanceMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const metrics = {
      method: req.method,
      route: req.route?.path || req.path,
      statusCode: res.statusCode,
      duration,
      timestamp: new Date().toISOString(),
      userAgent: req.get('User-Agent'),
      ip: req.ip,
    };
    
    // Log slow requests
    if (duration > 1000) {
      logger.warn('Slow request detected', metrics);
    }
    
    // Send metrics to monitoring service
    metricsService.recordAPICall(metrics);
  });
  
  next();
};
```

### Performance Alerts

**Automated Alerting:**
```typescript
// ✅ Good - Performance threshold monitoring
export class PerformanceAlerting {
  private static readonly THRESHOLDS = {
    API_RESPONSE_TIME: 1000, // 1 second
    ERROR_RATE: 0.05, // 5%
    DATABASE_QUERY_TIME: 500, // 500ms
    MEMORY_USAGE: 0.85, // 85%
  };

  static checkAPIPerformance(metrics: APIMetrics[]): void {
    const avgResponseTime = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
    const errorRate = metrics.filter(m => m.statusCode >= 400).length / metrics.length;
    
    if (avgResponseTime > this.THRESHOLDS.API_RESPONSE_TIME) {
      this.sendAlert('HIGH_API_RESPONSE_TIME', {
        current: avgResponseTime,
        threshold: this.THRESHOLDS.API_RESPONSE_TIME,
        timeWindow: '5 minutes'
      });
    }
    
    if (errorRate > this.THRESHOLDS.ERROR_RATE) {
      this.sendAlert('HIGH_ERROR_RATE', {
        current: errorRate,
        threshold: this.THRESHOLDS.ERROR_RATE,
        timeWindow: '5 minutes'
      });
    }
  }

  private static sendAlert(type: string, data: any): void {
    // Send to PagerDuty, Slack, etc.
    alertingService.send({
      severity: 'warning',
      title: `Performance Alert: ${type}`,
      description: `Performance threshold exceeded`,
      data,
      timestamp: new Date().toISOString()
    });
  }
}
```

## 🎯 Performance Best Practices

### Development Guidelines

**Code Review Checklist:**
- [ ] Database queries are optimized and indexed
- [ ] API responses include only necessary data
- [ ] Large lists use virtualization or pagination
- [ ] Images are optimized and properly sized
- [ ] Expensive calculations are memoized
- [ ] Cache invalidation strategies are implemented
- [ ] Performance metrics are collected

**Testing Performance:**
```typescript
// ✅ Good - Performance testing
describe('Portfolio Performance Tests', () => {
  it('should calculate portfolio value within acceptable time', async () => {
    const largePortfolio = createLargePortfolio(1000); // 1000 positions
    
    const startTime = Date.now();
    const value = await portfolioService.calculateValue(largePortfolio);
    const endTime = Date.now();
    
    expect(endTime - startTime).toBeLessThan(500); // Should complete within 500ms
    expect(value).toBeGreaterThan(0);
  });

  it('should handle concurrent requests efficiently', async () => {
    const requests = Array.from({ length: 100 }, () => 
      portfolioService.getPortfolio('test-portfolio-id')
    );
    
    const startTime = Date.now();
    const results = await Promise.all(requests);
    const endTime = Date.now();
    
    expect(endTime - startTime).toBeLessThan(2000); // 100 requests in under 2 seconds
    expect(results).toHaveLength(100);
  });
});
```

### Deployment Optimization

**CDN Configuration:**
```javascript
// CloudFront configuration for optimal caching
const cloudFrontConfig = {
  behaviors: [
    {
      pathPattern: '/static/*',
      cachePolicyId: 'managed-caching-optimized',
      compress: true,
      viewerProtocolPolicy: 'redirect-to-https',
    },
    {
      pathPattern: '/api/*',
      cachePolicyId: 'managed-caching-disabled',
      originRequestPolicyId: 'managed-cors-s3-origin',
    },
    {
      pathPattern: '/_next/static/*',
      cachePolicyId: 'managed-caching-optimized-for-uncompressed-objects',
      compress: true,
    }
  ]
};
```

---

*This performance guide ensures our financial platform delivers exceptional user experience through optimized performance across all layers. Regular performance reviews and optimizations are essential for maintaining these standards.*
