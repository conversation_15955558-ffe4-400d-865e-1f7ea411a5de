# GitHub OIDC Setup for IAM Identity Center

Since you're using IAM Identity Center, this is the recommended secure approach instead of creating IAM users with access keys.

## Benefits of OIDC over Access Keys
- ✅ No long-term credentials to manage
- ✅ Automatic credential rotation
- ✅ Better security (temporary tokens)
- ✅ Works perfectly with IAM Identity Center
- ✅ No risk of accidentally committing access keys

## Step-by-Step OIDC Setup

### 1. Set Up OIDC Identity Provider in ValuraDev Account

1. **Log into your ValuraDev account** via IAM Identity Center
2. **Go to IAM Console** in `ap-south-1` region
3. **Navigate to Identity Providers** → **Add provider**
4. **Configure OIDC Provider:**
   - Provider type: `OpenID Connect`
   - Provider URL: `https://token.actions.githubusercontent.com`
   - Audience: `sts.amazonaws.com`
   - Click **Get thumbprint** (should auto-populate)
   - Click **Add provider**

### 2. Create IAM Role for GitHub Actions

1. **Go to IAM Roles** → **Create role**
2. **Select trusted entity:** `Web identity`
3. **Identity provider:** Select the OIDC provider you just created
4. **Audience:** `sts.amazonaws.com`
5. **GitHub organization:** Your GitHub username
6. **GitHub repository:** `valura_ai`
7. **GitHub branch:** `dev` (for now)

### 3. Configure Role Trust Policy

Replace the auto-generated trust policy with this (update YOUR_GITHUB_USERNAME):

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Federated": "arn:aws:iam::YOUR_ACCOUNT_ID:oidc-provider/token.actions.githubusercontent.com"
      },
      "Action": "sts:AssumeRole",
      "Condition": {
        "StringEquals": {
          "token.actions.githubusercontent.com:aud": "sts.amazonaws.com"
        },
        "StringLike": {
          "token.actions.githubusercontent.com:sub": [
            "repo:YOUR_GITHUB_USERNAME/valura_ai:ref:refs/heads/dev",
            "repo:YOUR_GITHUB_USERNAME/valura_ai:ref:refs/heads/feat/*"
          ]
        }
      }
    }
  ]
}
```

### 4. Attach Permissions to Role

Attach these AWS managed policies:
- `AWSAmplifyFullAccess`
- `AmazonRDSFullAccess`
- `AmazonEC2ContainerRegistryFullAccess`
- `AWSAppRunnerFullAccess`
- `AmazonVPCFullAccess`
- `CloudWatchFullAccess`

**Role name:** `GitHubActionsRole-ValuraDev`

### 5. Update GitHub Actions Workflow

Update your GitHub secrets to use the role ARN instead of access keys:

```bash
# Instead of access keys, set the role ARN
gh secret set AWS_ROLE_ARN_INDIA --body "arn:aws:iam::YOUR_ACCOUNT_ID:role/GitHubActionsRole-ValuraDev"
gh secret set AWS_REGION_INDIA --body "ap-south-1"
```

### 6. Updated GitHub Actions Configuration

The workflow will use this configuration instead of access keys:

```yaml
- name: Configure AWS credentials
  uses: aws-actions/configure-aws-credentials@v4
  with:
    role-to-assume: ${{ secrets.AWS_ROLE_ARN_INDIA }}
    aws-region: ${{ secrets.AWS_REGION_INDIA }}
    role-session-name: GitHubActions-ValuraDev
```

## Alternative: Temporary Access Keys (Quick Start)

If you want to get started quickly and set up OIDC later, you can:

1. **Use your IAM Identity Center user** to create temporary access keys
2. **Go to AWS Console** → **Command line or programmatic access**
3. **Copy the temporary credentials** (valid for 1-12 hours)
4. **Set them as GitHub secrets** (you'll need to refresh them regularly)

```bash
# Set temporary credentials (will expire)
gh secret set AWS_ACCESS_KEY_ID_INDIA --body "ASIA..."
gh secret set AWS_SECRET_ACCESS_KEY_INDIA --body "..."
gh secret set AWS_SESSION_TOKEN_INDIA --body "..."  # Required for temporary creds
```

## Recommended Approach

**For Development/Testing:** Use temporary access keys to get started quickly

**For Production:** Set up OIDC for security and automation

## Troubleshooting OIDC

### Common Issues:

1. **"No identity-based policy allows the sts:AssumeRole action"**
   - Check that the role has the correct trust policy
   - Verify the GitHub repository name matches exactly

2. **"Invalid identity token"**
   - Ensure the OIDC provider URL is exactly: `https://token.actions.githubusercontent.com`
   - Check that the audience is `sts.amazonaws.com`

3. **"Access denied"**
   - Verify the role has the necessary permissions attached
   - Check that the branch name in the trust policy matches your branch

### Testing OIDC Setup

You can test the OIDC setup with a simple GitHub Action:

```yaml
name: Test OIDC
on:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN_INDIA }}
          aws-region: ap-south-1
          role-session-name: GitHubActions-Test
      
      - name: Test AWS access
        run: |
          aws sts get-caller-identity
          aws amplify list-apps --region ap-south-1
```

## Next Steps

1. **Choose your approach:** OIDC (recommended) or temporary access keys (quick start)
2. **Set up the authentication method** in your ValuraDev account
3. **Configure GitHub secrets** accordingly
4. **Test the setup** with a simple workflow
5. **Proceed with infrastructure deployment**

The rest of the setup guide remains the same regardless of which authentication method you choose!
