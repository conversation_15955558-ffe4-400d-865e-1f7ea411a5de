# Valura AI - CI/CD Pipeline Implementation

## 🚀 Quick Start

This repository now includes a comprehensive CI/CD pipeline with multi-region deployment strategy optimized for UAE production and India development.

### Branch Strategy
- **`dev`** → Development (India `ap-south-1`) via AWS Amplify
- **`stg`** → Staging (UAE `me-central-1`) via S3 + CloudFront
- **`prod`** → Production (UAE `me-central-1`) via S3 + CloudFront

## 📁 Repository Structure

```
valura_ai/
├── .github/workflows/
│   ├── ci-cd-uae.yml           # Main CI/CD pipeline
│   └── main.yml                # Legacy mobile workflow
├── infrastructure/
│   ├── terraform/              # Infrastructure as Code
│   │   ├── main.tf            # Main Terraform configuration
│   │   ├── variables.tf       # Variable definitions
│   │   ├── api.tf            # API infrastructure
│   │   ├── outputs.tf        # Output values
│   │   └── terraform.tfvars.example
│   └── scripts/
│       ├── deploy.sh          # Infrastructure deployment script
│       └── setup-github-secrets.sh  # GitHub secrets setup
├── apps/
│   ├── web/                   # Next.js 14 Web App
│   ├── admin/                 # Next.js 14 Admin Panel
│   ├── landing/               # Next.js 15 Landing Page
│   ├── api/                   # Express.js API Server
│   ├── mobile/                # React Native + Expo App
│   └── microservice/          # Python FastAPI Services
└── docs/
    └── DEPLOYMENT_GUIDE.md    # Detailed deployment guide
```

## 🏗️ Infrastructure Overview

### AWS Services Used

#### UAE Region (`me-central-1`) - Production & Staging
- **S3 + CloudFront**: Static hosting for Next.js apps
- **App Runner**: Containerized API deployment
- **RDS PostgreSQL**: Primary database
- **ECR**: Container registry
- **Route53**: DNS management
- **ACM**: SSL certificates

#### India Region (`ap-south-1`) - Development
- **AWS Amplify**: Simplified hosting for development
- **App Runner**: Development API server
- **RDS PostgreSQL**: Development database

### Cost Estimates
- **Development**: ~$15-25/month
- **Staging**: ~$35-70/month  
- **Production**: ~$135-325/month
- **Total**: ~$185-420/month

## 🚀 Quick Deployment

### 1. Prerequisites
```bash
# Install required tools
brew install terraform awscli
npm install -g @aws-amplify/cli eas-cli gh
```

### 2. Infrastructure Setup
```bash
# Configure Terraform
cp infrastructure/terraform/terraform.tfvars.example infrastructure/terraform/terraform.tfvars
# Edit terraform.tfvars with your domain and settings

# Deploy infrastructure
chmod +x infrastructure/scripts/deploy.sh
./infrastructure/scripts/deploy.sh apply production
```

### 3. GitHub Secrets Setup
```bash
# Authenticate with GitHub
gh auth login

# Run automated secrets setup
chmod +x infrastructure/scripts/setup-github-secrets.sh
./infrastructure/scripts/setup-github-secrets.sh
```

### 4. Test Deployment
```bash
# Test development deployment
git checkout dev
git push origin dev

# Test staging deployment  
git checkout stg
git push origin stg

# Test production deployment
git checkout prod
git push origin prod
```

## 🔧 Configuration Details

### Environment Variables by Branch

#### Development (`dev` branch)
- Deploys to AWS Amplify in India
- Uses development database and services
- Automatic deployments on push

#### Staging (`stg` branch)  
- Deploys to S3 + CloudFront in UAE
- Uses staging database and services
- Manual promotion from development

#### Production (`prod` branch)
- Deploys to S3 + CloudFront in UAE
- Uses production database and services
- Manual promotion from staging

### Application-Specific Configurations

#### Next.js Apps (Web, Admin, Landing)
- Static export for S3 hosting
- Environment-specific API endpoints
- CloudFront CDN optimization

#### API Server
- Containerized deployment via App Runner
- Auto-scaling based on traffic
- VPC connectivity to RDS

#### Mobile App
- EAS (Expo Application Services) integration
- Environment-specific updates
- App store deployment for production

#### Python Microservices
- Lambda deployment for cost optimization
- API Gateway integration
- Environment-specific configurations

## 📋 Required GitHub Secrets

### AWS Credentials
```
AWS_ACCESS_KEY_ID_UAE
AWS_SECRET_ACCESS_KEY_UAE
AWS_ACCESS_KEY_ID_INDIA  
AWS_SECRET_ACCESS_KEY_INDIA
```

### Infrastructure Secrets
```
S3_BUCKET_WEB_PROD
S3_BUCKET_ADMIN_PROD
S3_BUCKET_LANDING_PROD
CLOUDFRONT_DISTRIBUTION_ID_WEB
CLOUDFRONT_DISTRIBUTION_ID_ADMIN
CLOUDFRONT_DISTRIBUTION_ID_LANDING
ECR_REGISTRY_UAE
ECR_REGISTRY_INDIA
```

### Application Secrets
```
NEXT_PUBLIC_API_URL
NEXT_PUBLIC_USER_POOL_ID
NEXT_PUBLIC_USER_POOL_CLIENT_ID
DATABASE_URL_PROD
DATABASE_URL_STAGING
DATABASE_URL_DEV
EXPO_TOKEN
RESEND_API_KEY
```

## 🔍 Monitoring & Maintenance

### Automated Monitoring
- CloudWatch metrics for all services
- App Runner auto-scaling
- RDS performance insights
- CloudFront analytics

### Security Features
- VPC isolation for databases
- IAM least-privilege access
- Encrypted storage (S3, RDS)
- SSL/TLS everywhere

### Backup Strategy
- RDS automated backups
- S3 versioning enabled
- Infrastructure state in Terraform

## 🆘 Troubleshooting

### Common Issues
1. **DNS not resolving**: Check Route53 nameservers
2. **SSL certificate issues**: Ensure cert is in us-east-1
3. **App Runner failures**: Check ECR image and VPC config
4. **Mobile updates failing**: Verify EAS token and config

### Support Commands
```bash
# Check infrastructure status
./infrastructure/scripts/deploy.sh validate

# View Terraform outputs
./infrastructure/scripts/deploy.sh output

# Check GitHub secrets
gh secret list
```

## 📚 Documentation

- **[Complete Deployment Guide](docs/DEPLOYMENT_GUIDE.md)**: Detailed step-by-step instructions
- **[Terraform Documentation](infrastructure/terraform/)**: Infrastructure code documentation
- **[GitHub Actions Workflows](.github/workflows/)**: CI/CD pipeline details

## 🎯 Next Steps

1. **Deploy Infrastructure**: Follow the deployment guide
2. **Configure Secrets**: Use the automated setup script
3. **Test Pipeline**: Push to each branch to verify deployments
4. **Monitor Costs**: Set up AWS billing alerts
5. **Security Review**: Implement additional security measures
6. **Performance Optimization**: Fine-tune based on usage patterns

## 🤝 Contributing

1. Create feature branches from `dev`
2. Test changes in development environment
3. Create PR to `dev` for review
4. Promote to `stg` for staging tests
5. Promote to `prod` for production deployment

---

**Need Help?** Check the [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) or create an issue for support.
