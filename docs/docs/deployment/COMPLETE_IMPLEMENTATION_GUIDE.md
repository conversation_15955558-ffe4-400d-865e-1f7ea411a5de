# Valura AI - Complete CI/CD Implementation Guide

## Overview

This guide implements a production-ready CI/CD pipeline for your multi-app monorepo with:
- **3-branch strategy**: `dev` (India) → `stg` (UAE) → `prod` (UAE)
- **Multi-region deployment**: India (`ap-south-1`) for development, UAE (`me-central-1`) for staging/production
- **IAM Identity Center integration**: Secure authentication without long-term access keys
- **Cost optimization**: Environment-specific resource sizing

## Phase 1: AWS Account Setup (ValuraDev Account)

### 1.1 Enable Required AWS Services
In your **ValuraDev account** (`ap-south-1` region):

```bash
# Services to enable/verify:
- AWS Amplify (for Next.js hosting)
- Amazon RDS (PostgreSQL database)
- Amazon ECR (container registry)
- AWS App Runner (API deployment)
- Amazon VPC (networking)
- AWS IAM (roles and policies)
- Amazon CloudWatch (monitoring)
```

### 1.2 Set Up IAM Role for GitHub Actions

**Option A: OIDC (Recommended)**
1. Go to **IAM Console** → **Identity providers** → **Add provider**
2. Configure OIDC:
   - Provider URL: `https://token.actions.githubusercontent.com`
   - Audience: `sts.amazonaws.com`
3. Create IAM Role: `GitHubActionsRole-ValuraDev`
4. Trust policy (replace `YOUR_GITHUB_USERNAME`):

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Federated": "arn:aws:iam::ACCOUNT_ID:oidc-provider/token.actions.githubusercontent.com"
      },
      "Action": "sts:AssumeRole",
      "Condition": {
        "StringEquals": {
          "token.actions.githubusercontent.com:aud": "sts.amazonaws.com"
        },
        "StringLike": {
          "token.actions.githubusercontent.com:sub": [
            "repo:YOUR_GITHUB_USERNAME/valura_ai:ref:refs/heads/dev",
            "repo:YOUR_GITHUB_USERNAME/valura_ai:ref:refs/heads/feat/*"
          ]
        }
      }
    }
  ]
}
```

5. Attach policies:
   - `AWSAmplifyFullAccess`
   - `AmazonRDSFullAccess`
   - `AmazonEC2ContainerRegistryFullAccess`
   - `AWSAppRunnerFullAccess`
   - `AmazonVPCFullAccess`
   - `CloudWatchFullAccess`

**Option B: Temporary Access Keys (Quick Start)**
1. Use IAM Identity Center → "Command line or programmatic access"
2. Copy temporary credentials (valid 1-12 hours)
3. Set as GitHub secrets (refresh periodically)

### 1.3 Set Up Cost Protection
```bash
# Create billing alerts
aws cloudwatch put-metric-alarm \
  --alarm-name "valura-dev-billing-50" \
  --alarm-description "Billing alert for $50" \
  --metric-name EstimatedCharges \
  --namespace AWS/Billing \
  --statistic Maximum \
  --period 86400 \
  --threshold 50 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=Currency,Value=USD \
  --evaluation-periods 1 \
  --region us-east-1
```

## Phase 2: Infrastructure Deployment

### 2.1 Configure Terraform for Dev Environment
```bash
# Copy and customize terraform variables
cp infrastructure/terraform/terraform.tfvars.example infrastructure/terraform/terraform.tfvars.dev

# Edit terraform.tfvars.dev:
environment = "development"
domain_name = "dev.yourdomain.com"  # Use subdomain for testing
uae_region = "me-central-1"
india_region = "ap-south-1"
database_password = "your-secure-dev-password"
database_instance_class = "db.t3.micro"  # Free tier eligible
enable_multi_az = false
backup_retention_period = 1
enable_deletion_protection = false
```

### 2.2 Deploy Development Infrastructure
```bash
# Set AWS credentials (if using access keys)
export AWS_PROFILE=valura-dev  # or set temporary credentials

# Deploy infrastructure
cd infrastructure/terraform
terraform init
terraform plan -var-file="terraform.tfvars.dev"
terraform apply -var-file="terraform.tfvars.dev"

# Note the outputs for GitHub secrets
terraform output
```

### 2.3 Create AWS Amplify Applications
```bash
# Create 3 Amplify apps in ap-south-1
aws amplify create-app --name "valura-web-dev" --platform WEB --region ap-south-1
aws amplify create-app --name "valura-admin-dev" --platform WEB --region ap-south-1
aws amplify create-app --name "valura-landing-dev" --platform WEB --region ap-south-1

# Note the App IDs for GitHub secrets
```

## Phase 3: GitHub Configuration

### 3.1 Set Up GitHub Secrets

**For OIDC Authentication:**
```bash
gh secret set AWS_ROLE_ARN_INDIA --body "arn:aws:iam::ACCOUNT_ID:role/GitHubActionsRole-ValuraDev"
gh secret set AWS_REGION_INDIA --body "ap-south-1"
```

**For Access Key Authentication:**
```bash
gh secret set AWS_ACCESS_KEY_ID_INDIA --body "ASIA..."
gh secret set AWS_SECRET_ACCESS_KEY_INDIA --body "..."
gh secret set AWS_SESSION_TOKEN_INDIA --body "..."  # For temporary credentials
gh secret set AWS_REGION_INDIA --body "ap-south-1"
```

**Infrastructure Secrets (from Terraform output):**
```bash
gh secret set ECR_REGISTRY_INDIA --body "ACCOUNT_ID.dkr.ecr.ap-south-1.amazonaws.com"
gh secret set DATABASE_URL_DEV --body "************************************/db"
gh secret set VPC_CONNECTOR_ARN --body "arn:aws:apprunner:ap-south-1:ACCOUNT_ID:vpcconnector/..."
```

**Amplify App IDs:**
```bash
gh secret set AMPLIFY_APP_ID_WEB_DEV --body "d1234567890"
gh secret set AMPLIFY_APP_ID_ADMIN_DEV --body "d0987654321"
gh secret set AMPLIFY_APP_ID_LANDING_DEV --body "d1122334455"
```

**Application Environment Variables:**
```bash
gh secret set NEXT_PUBLIC_API_URL_DEV --body "https://api-dev.yourdomain.com"
gh secret set NEXT_PUBLIC_USER_POOL_ID --body "ap-south-1_xxxxxxxxx"
gh secret set NEXT_PUBLIC_USER_POOL_CLIENT_ID --body "xxxxxxxxxxxxxxxxxxxxxxxxxx"
gh secret set NEXT_PUBLIC_POSTHOG_KEY --body "phc_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
gh secret set NEXT_PUBLIC_POSTHOG_HOST --body "https://eu.i.posthog.com"
gh secret set RESEND_API_KEY --body "re_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
gh secret set NEXT_PUBLIC_SUPABASE_URL --body "https://xxxxxxxxxxxxxxxx.supabase.co"
gh secret set NEXT_PUBLIC_SUPABASE_ANON_KEY --body "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 3.2 Create GitHub Environment
1. Go to **GitHub repo** → **Settings** → **Environments**
2. Create **"development"** environment
3. Add environment protection rules if needed
4. Add environment-specific secrets

## Phase 4: Application Configuration

### 4.1 Update Next.js Applications for Static Export

**apps/web/next.config.js:**
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  }
}

module.exports = nextConfig
```

**Apply same config to:**
- `apps/admin/next.config.js`
- `apps/landing/next.config.js`

### 4.2 Configure API Application

**apps/api/Dockerfile (if not exists):**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 4000
CMD ["npm", "start"]
```

### 4.3 Update Package Scripts
Ensure your `package.json` has proper build scripts:
```json
{
  "scripts": {
    "build": "turbo run build",
    "test": "turbo run test",
    "lint": "turbo run lint"
  }
}
```

## Phase 5: Testing the Pipeline

### 5.1 Test Development Deployment
```bash
# Create and switch to dev branch
git checkout -b dev

# Make a small test change
echo "# Dev Environment Test" >> README.md
git add README.md
git commit -m "feat: test dev environment deployment"

# Push to trigger CI/CD
git push origin dev

# Monitor GitHub Actions
# Check AWS Amplify deployments
# Verify applications are accessible
```

### 5.2 Verify Deployments
1. **GitHub Actions**: Check workflow runs in Actions tab
2. **AWS Amplify**: Verify apps deployed in AWS console
3. **Application URLs**: Test each deployed application
4. **AWS Costs**: Monitor billing dashboard

### 5.3 Troubleshooting Common Issues

**GitHub Actions fails with "Access Denied":**
- Check IAM role permissions
- Verify trust policy includes correct repository
- Ensure OIDC provider is configured correctly

**Amplify deployment fails:**
- Check App IDs are correct
- Verify build settings in Amplify console
- Check application build scripts

**High AWS costs:**
- Stop unused RDS instances
- Delete unused ECR images
- Check NAT Gateway usage

## Phase 6: Monitoring and Maintenance

### 6.1 Set Up Monitoring
```bash
# CloudWatch alarms for key metrics
aws cloudwatch put-metric-alarm \
  --alarm-name "valura-dev-rds-cpu" \
  --alarm-description "RDS CPU utilization" \
  --metric-name CPUUtilization \
  --namespace AWS/RDS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=DBInstanceIdentifier,Value=valura-dev-db \
  --evaluation-periods 2
```

### 6.2 Cost Optimization
- Use `db.t3.micro` for development
- Enable RDS auto-pause for dev databases
- Set ECR lifecycle policies
- Monitor data transfer costs

### 6.3 Security Best Practices
- Rotate temporary credentials regularly
- Use least-privilege IAM policies
- Enable VPC Flow Logs
- Regular security audits

## Next Steps: Staging and Production

Once dev environment is stable:
1. **Set up Prod AWS account** with similar configuration
2. **Deploy staging environment** in UAE region
3. **Configure production environment** with high availability
4. **Set up proper domain management** with Route53
5. **Implement monitoring and alerting**

## Estimated Costs

**Development Environment:**
- RDS db.t3.micro: ~$13/month
- AWS Amplify (3 apps): ~$3-15/month
- App Runner: ~$10-25/month
- ECR: ~$1/month
- **Total: ~$27-54/month**

**Production Environment (later):**
- RDS db.t3.medium: ~$50/month
- S3 + CloudFront: ~$10-30/month
- App Runner: ~$30-100/month
- NAT Gateway: ~$45/month
- **Total: ~$135-225/month**

## Support and Troubleshooting

- **Documentation**: All guides in `docs/` directory
- **Scripts**: Automation scripts in `infrastructure/scripts/`
- **Monitoring**: CloudWatch dashboards and alarms
- **Cost tracking**: AWS Cost Explorer and billing alerts
