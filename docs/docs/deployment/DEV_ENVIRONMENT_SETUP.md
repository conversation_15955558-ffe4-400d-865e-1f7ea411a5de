# Dev Environment Setup Guide - ValuraDev Account

## Prerequisites Checklist for ValuraDev Account (ap-south-1)

### ✅ AWS Services to Enable/Configure

#### 1. Core Services (Required)
- [ ] **AWS Amplify** - For hosting Next.js applications
- [ ] **Amazon RDS** - PostgreSQL database
- [ ] **Amazon ECR** - Container registry for API
- [ ] **AWS App Runner** - API server deployment
- [ ] **Amazon VPC** - Network isolation
- [ ] **AWS IAM** - User and role management
- [ ] **Amazon CloudWatch** - Monitoring and logs

#### 2. Optional Services (Can be added later)
- [ ] **AWS Lambda** - For microservices
- [ ] **Amazon S3** - File storage
- [ ] **AWS Secrets Manager** - Secure secrets storage

### 🔧 Step-by-Step AWS Account Setup

#### Step 1: Set Up GitHub OIDC with IAM Role (Recommended for IAM Identity Center)
```bash
# 1. Go to IAM Console in ValuraDev account (ap-south-1)
# 2. Create OIDC Identity Provider:
#    - Provider Type: OpenID Connect
#    - Provider URL: https://token.actions.githubusercontent.com
#    - Audience: sts.amazonaws.com

# 3. Create IAM Role: GitHubActionsRole-ValuraDev
# 4. Trust Policy (replace YOUR_GITHUB_USERNAME/YOUR_REPO):
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Federated": "arn:aws:iam::ACCOUNT_ID:oidc-provider/token.actions.githubusercontent.com"
      },
      "Action": "sts:AssumeRole",
      "Condition": {
        "StringEquals": {
          "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
          "token.actions.githubusercontent.com:sub": "repo:YOUR_GITHUB_USERNAME/valura_ai:ref:refs/heads/dev"
        }
      }
    }
  ]
}

# 5. Attach policies to the role:
#    - AWSAmplifyFullAccess
#    - AmazonRDSFullAccess
#    - AmazonEC2ContainerRegistryFullAccess
#    - AWSAppRunnerFullAccess
#    - AmazonVPCFullAccess
#    - CloudWatchFullAccess
```

#### Alternative Step 1: Create IAM User (If you prefer access keys)
```bash
# Only use this if you don't want to set up OIDC
# 1. Go to IAM Console in ap-south-1
# 2. Create new user: valura-cicd-dev
# 3. Attach the same policies as above
# 4. Create access keys and save securely
```

#### Step 2: Set Up VPC (Optional but Recommended)
```bash
# Create VPC for better security isolation
# CIDR: 10.0.0.0/16
# Public subnets: 10.0.1.0/24, 10.0.2.0/24
# Private subnets: 10.0.3.0/24, 10.0.4.0/24
```

#### Step 3: Create RDS PostgreSQL Database
```bash
# Database Configuration:
# - Engine: PostgreSQL 15.4
# - Instance: db.t3.micro (free tier eligible)
# - Storage: 20GB GP2
# - Multi-AZ: No (for dev)
# - Public access: No
# - Database name: valura_dev_db
# - Username: valura_admin
# - Password: [Generate strong password]
```

#### Step 4: Set Up ECR Repository
```bash
# Create ECR repository for API container
aws ecr create-repository \
    --repository-name valura-api-dev \
    --region ap-south-1
```

#### Step 5: Configure AWS Amplify Apps
```bash
# You'll create 3 Amplify apps:
# 1. valura-web-dev (for apps/web)
# 2. valura-admin-dev (for apps/admin)  
# 3. valura-landing-dev (for apps/landing)
```

### 💰 Cost Estimation for Dev Environment
- **RDS db.t3.micro**: ~$13/month
- **AWS Amplify**: ~$1-5/month per app (3 apps = $3-15/month)
- **App Runner**: ~$10-25/month
- **ECR**: ~$1/month
- **VPC NAT Gateway**: ~$45/month (if using private subnets)
- **Total**: ~$72-99/month (or ~$27-54/month without NAT Gateway)

### 🛡️ Cost Protection Measures
1. Set up billing alerts at $50, $75, $100
2. Use AWS Free Tier where possible
3. Enable detailed billing reports
4. Set up AWS Budgets with alerts

## Domain Configuration Strategy

### Recommended Domain Structure
```
Production:    yourdomain.com
               app.yourdomain.com  
               admin.yourdomain.com

Staging:       stg.yourdomain.com
               app-stg.yourdomain.com
               admin-stg.yourdomain.com

Development:   dev.yourdomain.com
               app-dev.yourdomain.com
               admin-dev.yourdomain.com
```

### DNS Setup Options

#### Option 1: Separate Hosted Zones (Recommended)
- Create separate Route53 hosted zones for each environment
- More isolated and secure
- Easier to manage permissions

#### Option 2: Single Hosted Zone with Subdomains
- Use one hosted zone with environment-specific subdomains
- More cost-effective
- Simpler initial setup

### For Dev Environment Only Setup
1. **Don't change main domain nameservers yet**
2. **Create a subdomain**: `dev.yourdomain.com`
3. **Use CNAME records** pointing to Amplify domains
4. **Test everything** before touching production DNS

## Step-by-Step Dev Environment Deployment

### Phase 1: Infrastructure Setup (ValuraDev Account)

#### 1. Configure AWS CLI
```bash
# Install AWS CLI v2
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /

# Configure for ValuraDev account
aws configure --profile valura-dev
# Enter your ValuraDev account access keys
# Region: ap-south-1
```

#### 2. Create Terraform Configuration for Dev Only
```bash
# Create dev-specific terraform.tfvars
cp infrastructure/terraform/terraform.tfvars.example infrastructure/terraform/terraform.tfvars.dev

# Edit terraform.tfvars.dev:
environment = "development"
domain_name = "dev.yourdomain.com"  # Use subdomain for testing
uae_region = "ap-south-1"  # Override to use India for dev
india_region = "ap-south-1"
database_password = "your-secure-dev-password"
```

#### 3. Deploy Dev Infrastructure
```bash
# Set AWS profile
export AWS_PROFILE=valura-dev

# Deploy infrastructure
cd infrastructure/terraform
terraform init
terraform plan -var-file="terraform.tfvars.dev"
terraform apply -var-file="terraform.tfvars.dev"
```

### Phase 2: GitHub Configuration

#### 1. Set Up GitHub Secrets (Dev Only)
```bash
# Required secrets for dev environment:
gh secret set AWS_ACCESS_KEY_ID_INDIA --body "your-valura-dev-access-key"
gh secret set AWS_SECRET_ACCESS_KEY_INDIA --body "your-valura-dev-secret-key"
gh secret set DATABASE_URL_DEV --body "*******************************************************/valura_dev_db"

# Application secrets:
gh secret set NEXT_PUBLIC_API_URL --body "https://api-dev.yourdomain.com"
gh secret set NEXT_PUBLIC_USER_POOL_ID --body "your-dev-cognito-pool-id"
gh secret set NEXT_PUBLIC_USER_POOL_CLIENT_ID --body "your-dev-cognito-client-id"

# Amplify App IDs (get these after creating Amplify apps):
gh secret set AMPLIFY_APP_ID_WEB_DEV --body "your-web-app-id"
gh secret set AMPLIFY_APP_ID_ADMIN_DEV --body "your-admin-app-id"
gh secret set AMPLIFY_APP_ID_LANDING_DEV --body "your-landing-app-id"
```

#### 2. Create GitHub Environment
```bash
# Go to GitHub repo → Settings → Environments
# Create "development" environment
# Add environment-specific secrets if needed
```

### Phase 3: Application Configuration

#### 1. Update Next.js Apps for Static Export
```javascript
// apps/web/next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  }
}

module.exports = nextConfig
```

#### 2. Configure API for Development
```javascript
// apps/api/src/config/database.js
const config = {
  development: {
    url: process.env.DATABASE_URL_DEV,
    dialect: 'postgres',
    logging: console.log
  }
}
```

### Phase 4: Testing the Pipeline

#### 1. Test Local Build First
```bash
# Test each app builds correctly
cd apps/web && npm run build
cd apps/admin && npm run build  
cd apps/landing && npm run build
cd apps/api && npm run build
```

#### 2. Deploy to Dev Branch
```bash
# Create and push to dev branch
git checkout -b dev
git add .
git commit -m "feat: initial dev environment setup"
git push origin dev

# Monitor GitHub Actions
# Check AWS Amplify deployments
# Verify API deployment to App Runner
```

## Risk Mitigation Checklist

### 🛡️ Before You Start
- [ ] Set up AWS billing alerts ($25, $50, $75, $100)
- [ ] Enable AWS Cost Explorer
- [ ] Create IAM user with minimal required permissions
- [ ] Use separate AWS account (ValuraDev) - ✅ You already have this
- [ ] Start with smallest instance sizes
- [ ] Enable detailed monitoring

### 🔍 During Deployment
- [ ] Monitor AWS costs daily during initial setup
- [ ] Test with minimal traffic first
- [ ] Use development/staging instance sizes
- [ ] Keep backups minimal (1-day retention)
- [ ] Monitor CloudWatch logs for errors

### ⚠️ Red Flags to Watch For
- Unexpected data transfer charges
- RDS instance running when not needed
- Multiple NAT Gateways created
- Large CloudWatch log retention
- Unused Elastic IPs

### 🚨 Emergency Procedures
```bash
# If costs spike unexpectedly:
# 1. Stop all App Runner services
aws apprunner pause-service --service-arn <service-arn>

# 2. Stop RDS instance (can restart later)
aws rds stop-db-instance --db-instance-identifier valura-dev-db

# 3. Delete unused resources
terraform destroy -target=resource.name
```

## Next Steps After Dev Environment Works

1. **Verify all applications work** in dev environment
2. **Test CI/CD pipeline** with small changes
3. **Monitor costs** for 1 week
4. **Document any issues** and solutions
5. **Plan staging environment** setup
6. **Gradually add production** environment

## Quick Commands Reference

```bash
# Check AWS costs
aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31 --granularity MONTHLY --metrics BlendedCost

# Monitor Amplify deployments
aws amplify list-apps --region ap-south-1

# Check App Runner services
aws apprunner list-services --region ap-south-1

# View RDS instances
aws rds describe-db-instances --region ap-south-1
```
