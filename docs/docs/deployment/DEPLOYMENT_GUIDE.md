# Valura AI - Complete CI/CD Deployment Guide

## Overview

This guide provides step-by-step instructions to set up a comprehensive CI/CD pipeline for the Valura AI multi-app monorepo with regional deployment strategy.

## Architecture Overview

### Branch Strategy
- **`dev`** → Development environment in India (`ap-south-1`) using AWS Amplify
- **`stg`** → Staging environment in UAE (`me-central-1`) using S3 + CloudFront
- **`prod`** → Production environment in UAE (`me-central-1`) using S3 + CloudFront

### Regional Deployment
- **India Region (`ap-south-1`)**: Development environment with AWS Amplify
- **UAE Region (`me-central-1`)**: Production and Staging with S3 + CloudFront

### Applications
1. **Web App** (`apps/web`) - Next.js 14 investor dashboard
2. **Admin Panel** (`apps/admin`) - Next.js 14 administrative interface
3. **Landing Page** (`apps/landing`) - Next.js 15 marketing website
4. **API Server** (`apps/api`) - Express.js + TypeScript backend
5. **Mobile App** (`apps/mobile`) - React Native + Expo
6. **Analysis Microservice** (`apps/microservice/analysis`) - Python FastAPI

## Prerequisites

### Required Tools
```bash
# Install required tools
brew install terraform
brew install awscli
npm install -g @aws-amplify/cli
npm install -g eas-cli
```

### AWS Account Setup
1. **Two AWS Accounts Recommended**:
   - India account for development
   - UAE account for staging/production

2. **Required AWS Services**:
   - S3, CloudFront, Route53, ACM
   - App Runner, ECR, RDS
   - Cognito, Lambda, API Gateway

## Step 1: Infrastructure Setup

### 1.1 Configure Terraform Variables
```bash
# Copy and customize terraform variables
cp infrastructure/terraform/terraform.tfvars.example infrastructure/terraform/terraform.tfvars

# Edit with your values
nano infrastructure/terraform/terraform.tfvars
```

### 1.2 Deploy Infrastructure
```bash
# Make deployment script executable
chmod +x infrastructure/scripts/deploy.sh

# Initialize and validate
./infrastructure/scripts/deploy.sh init

# Deploy production infrastructure
./infrastructure/scripts/deploy.sh apply production

# Deploy staging infrastructure
./infrastructure/scripts/deploy.sh apply staging
```

### 1.3 Configure DNS
After infrastructure deployment:
1. Update your domain's nameservers to point to Route53
2. Wait for DNS propagation (up to 48 hours)

## Step 2: GitHub Secrets Configuration

### 2.1 Required GitHub Secrets

#### AWS Credentials
```bash
# UAE Region (Production & Staging)
AWS_ACCESS_KEY_ID_UAE=your-uae-access-key
AWS_SECRET_ACCESS_KEY_UAE=your-uae-secret-key

# India Region (Development)
AWS_ACCESS_KEY_ID_INDIA=your-india-access-key
AWS_SECRET_ACCESS_KEY_INDIA=your-india-secret-key
```

#### Application Environment Variables
```bash
# API URLs
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
API_URL_DEV_INDIA=https://dev-api.yourdomain.com
API_URL_STAGING_UAE=https://stg-api.yourdomain.com
API_URL_PROD_UAE=https://api.yourdomain.com

# AWS Cognito
NEXT_PUBLIC_USER_POOL_ID=your-user-pool-id
NEXT_PUBLIC_USER_POOL_CLIENT_ID=your-client-id

# Database
DATABASE_URL_DEV=**********************************/valura_db
DATABASE_URL_STAGING=**********************************/valura_db
DATABASE_URL_PROD=***********************************/valura_db

# Expo/EAS
EXPO_TOKEN=your-expo-token

# Other services
NEXT_PUBLIC_POSTHOG_KEY=your-posthog-key
NEXT_PUBLIC_POSTHOG_HOST=https://eu.i.posthog.com
RESEND_API_KEY=your-resend-key
```

#### Infrastructure Secrets (from Terraform output)
```bash
# S3 Buckets
S3_BUCKET_WEB_PROD=valura-web-production-123456789
S3_BUCKET_ADMIN_PROD=valura-admin-production-123456789
S3_BUCKET_LANDING_PROD=valura-landing-production-123456789
S3_BUCKET_WEB_STAGING=valura-web-staging-123456789
S3_BUCKET_ADMIN_STAGING=valura-admin-staging-123456789
S3_BUCKET_LANDING_STAGING=valura-landing-staging-123456789

# CloudFront Distribution IDs
CLOUDFRONT_DISTRIBUTION_ID_WEB=E1234567890ABC
CLOUDFRONT_DISTRIBUTION_ID_ADMIN=E1234567890DEF
CLOUDFRONT_DISTRIBUTION_ID_LANDING=E1234567890GHI
CLOUDFRONT_DISTRIBUTION_ID_WEB_STAGING=E1234567890JKL
CLOUDFRONT_DISTRIBUTION_ID_ADMIN_STAGING=E1234567890MNO
CLOUDFRONT_DISTRIBUTION_ID_LANDING_STAGING=E1234567890PQR

# ECR Repositories
ECR_REGISTRY_UAE=123456789.dkr.ecr.me-central-1.amazonaws.com
ECR_REGISTRY_INDIA=123456789.dkr.ecr.ap-south-1.amazonaws.com

# AWS Amplify App IDs (for development)
AMPLIFY_APP_ID_WEB_DEV=d1234567890abc
AMPLIFY_APP_ID_ADMIN_DEV=d1234567890def
AMPLIFY_APP_ID_LANDING_DEV=d1234567890ghi

# App Runner Service ARNs
APP_RUNNER_SERVICE_ARN_PROD=arn:aws:apprunner:me-central-1:123456789:service/valura-api-prod
APP_RUNNER_SERVICE_ARN_STAGING=arn:aws:apprunner:me-central-1:123456789:service/valura-api-stg
APP_RUNNER_SERVICE_ARN_DEV=arn:aws:apprunner:ap-south-1:123456789:service/valura-api-dev
```

### 2.2 Add Secrets to GitHub
1. Go to your repository → Settings → Secrets and variables → Actions
2. Add all the secrets listed above
3. Use GitHub Environments for environment-specific secrets

## Step 3: Application Configuration

### 3.1 Update Next.js Applications
Ensure your Next.js apps have proper `next.config.js` for static export:

```javascript
// apps/web/next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}

module.exports = nextConfig
```

### 3.2 Configure Mobile App
Update `apps/mobile/app.json` with proper EAS configuration:

```json
{
  "expo": {
    "name": "Valura AI",
    "slug": "valura-ai",
    "updates": {
      "url": "https://u.expo.dev/your-project-id"
    },
    "runtimeVersion": {
      "policy": "sdkVersion"
    }
  }
}
```

## Step 4: Testing the Pipeline

### 4.1 Test Development Deployment
```bash
# Create a feature branch from dev
git checkout dev
git checkout -b feature/test-deployment

# Make a small change
echo "# Test" >> README.md
git add README.md
git commit -m "test: deployment pipeline"

# Push to trigger CI/CD
git push origin feature/test-deployment

# Create PR to dev branch
```

### 4.2 Test Staging Deployment
```bash
# Merge to staging
git checkout stg
git merge dev
git push origin stg
```

### 4.3 Test Production Deployment
```bash
# Merge to production
git checkout prod
git merge stg
git push origin prod
```

## Step 5: Monitoring and Maintenance

### 5.1 CloudWatch Monitoring
- Set up CloudWatch alarms for App Runner services
- Monitor S3 and CloudFront metrics
- Configure log retention policies

### 5.2 Cost Optimization
- Review AWS Cost Explorer monthly
- Optimize CloudFront cache policies
- Consider Reserved Instances for production RDS

### 5.3 Security Best Practices
- Rotate AWS access keys quarterly
- Review IAM policies regularly
- Enable AWS Config for compliance monitoring
- Use AWS Secrets Manager for sensitive data

## Estimated Costs

### Development Environment (India)
- AWS Amplify: $1-5/month per app
- Total: ~$15-25/month

### Staging Environment (UAE)
- S3 + CloudFront: $5-15/month
- App Runner: $15-30/month
- RDS: $15-25/month
- Total: ~$35-70/month

### Production Environment (UAE)
- S3 + CloudFront: $10-30/month
- App Runner: $30-100/month
- RDS: $50-150/month
- NAT Gateway: $45/month
- Total: ~$135-325/month

**Overall Estimated Cost: $185-420/month**

## Troubleshooting

### Common Issues
1. **DNS not resolving**: Check nameserver configuration
2. **SSL certificate issues**: Ensure certificate is in us-east-1
3. **App Runner deployment fails**: Check ECR image and VPC configuration
4. **Mobile app updates not working**: Verify EAS configuration

### Support Resources
- AWS Documentation
- Terraform Documentation
- Expo/EAS Documentation
- GitHub Actions Documentation

## Next Steps
1. Set up monitoring and alerting
2. Configure backup strategies
3. Implement security scanning
4. Set up performance monitoring
5. Plan disaster recovery procedures
