# New Joinee Onboarding Guide

*Welcome to Valura AI! This comprehensive guide will help you get up to speed quickly and effectively.*

## 🎯 Overview

Welcome to the Valura AI team! This guide will walk you through everything you need to know to become a productive member of our fintech development team. We'll cover business context, technical setup, team processes, and your first tasks.

## 📋 Pre-Onboarding Checklist

Before your first day, ensure you have:

- [ ] **Hardware Setup** - Laptop/workstation with adequate specs (16GB+ RAM, SSD)
- [ ] **Account Access** - GitHub, AWS, Slack, and other tool access
- [ ] **Development Tools** - VS Code, Node.js, Docker, and required extensions
- [ ] **Security Setup** - VPN access, 2FA enabled on all accounts

## 🏢 Business Context & Domain Knowledge

### What is Valura AI?

Valura AI is a **financial technology platform** that democratizes investment management through AI-powered tools. We serve:

- **Individual Investors** - Retail investors seeking intelligent portfolio management
- **Financial Advisors** - Professionals managing client portfolios
- **Institutional Clients** - Organizations requiring compliance and oversight tools

### Our Mission

To make sophisticated investment management accessible to everyone through AI-driven insights and user-friendly interfaces.

### Key Business Concepts

**Portfolio Management**
- Asset allocation and diversification
- Risk assessment and management
- Performance tracking and reporting

**Regulatory Compliance**
- KYC (Know Your Customer) processes
- AML (Anti-Money Laundering) requirements
- Financial regulatory reporting

**Trading Operations**
- Order management and execution
- Market data integration
- Settlement and clearing processes

## 🏗️ Technical Architecture Overview

### Platform Components

```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Web App       │  │   Mobile App    │  │   Admin Panel   │
│   (Next.js)     │  │ (React Native)  │  │   (Next.js)     │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
                    ┌─────────────────┐
                    │   API Server    │
                    │  (Express.js)   │
                    └─────────────────┘
                               │
         ┌─────────────────────┼─────────────────────┐
         │                     │                     │
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   PostgreSQL    │  │   Redis Cache   │  │   AWS Services  │
│   Database      │  │                 │  │ (Cognito, S3)   │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### Technology Stack

**Frontend**
- **Next.js 14/15** - React framework with App Router
- **React Native + Expo** - Cross-platform mobile development
- **Tailwind CSS** - Utility-first CSS framework
- **TypeScript** - Type-safe JavaScript

**Backend**
- **Express.js** - Node.js web framework
- **Prisma** - Database ORM and query builder
- **PostgreSQL** - Primary database
- **Redis** - Caching and session storage

**Infrastructure**
- **AWS** - Cloud infrastructure (Cognito, S3, CloudFront)
- **Docker** - Containerization
- **Turborepo** - Monorepo management
- **GitHub Actions** - CI/CD pipeline

## 🛠️ Development Environment Setup

### Step 1: Clone and Install

```bash
# Clone the repository
git clone <repository-url>
cd valura_ai

# Install dependencies
pnpm install

# Copy environment template
cp .env.example .env.local
```

### Step 2: Environment Configuration

Edit `.env.local` with the development values (ask your team lead for the actual values):

```bash
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/valura_dev"

# AWS Cognito (Development)
NEXT_PUBLIC_USER_POOL_ID="ap-south-1_xxxxxxxxx"
NEXT_PUBLIC_USER_POOL_CLIENT_ID="xxxxxxxxxxxxxxxxxxxxxxxxxx"

# API Configuration
NEXT_PUBLIC_API_URL="http://localhost:8000"
API_PORT="8000"

# Application Ports
WEB_PORT="3000"
ADMIN_PORT="3001"
LANDING_PORT="3002"
```

### Step 3: Database Setup

```bash
# Start PostgreSQL with Docker
docker-compose up -d postgres

# Run database migrations
cd apps/api
pnpm prisma migrate dev
pnpm prisma generate

# Seed development data
pnpm prisma db seed
```

### Step 4: Start Development Servers

```bash
# Return to project root
cd ../..

# Start all applications
pnpm dev
```

**Verify everything is running:**
- Web App: http://localhost:3000
- Admin Panel: http://localhost:3001
- Landing Page: http://localhost:3002
- API Server: http://localhost:8000

## 👥 Team Structure & Roles

### Development Team

**Frontend Team**
- Web application development (Next.js)
- Mobile application development (React Native)
- UI/UX implementation

**Backend Team**
- API development and maintenance
- Database design and optimization
- Third-party integrations

**DevOps Team**
- Infrastructure management
- CI/CD pipeline maintenance
- Security and compliance

**Product Team**
- Requirements gathering
- Feature specification
- User experience design

### Key Contacts

- **Tech Lead** - Technical guidance and architecture decisions
- **Product Manager** - Feature requirements and priorities
- **DevOps Lead** - Infrastructure and deployment support
- **Compliance Officer** - Regulatory and security guidance

## 📝 Development Workflow

### Git Workflow

We use a **three-branch strategy**:

```
main (production) ← stg (staging) ← dev (development)
```

**Branch Naming Convention:**
- `feature/TICKET-123-feature-description`
- `bugfix/TICKET-456-bug-description`
- `hotfix/critical-issue-description`

### Development Process

1. **Pick a Task** - Assign yourself a ticket from our project board
2. **Create Branch** - Create feature branch from `dev`
3. **Develop** - Write code following our coding standards
4. **Test** - Write and run tests for your changes
5. **Code Review** - Create PR and request review
6. **Deploy** - Merge to `dev` for testing, then `stg` for staging

### Code Review Guidelines

**Before Requesting Review:**
- [ ] All tests pass locally
- [ ] Code follows our style guidelines
- [ ] Documentation is updated
- [ ] No console.log statements in production code

**Review Checklist:**
- [ ] Code quality and readability
- [ ] Security considerations
- [ ] Performance implications
- [ ] Test coverage

## 🔒 Security Guidelines

### Authentication & Authorization

**User Authentication:**
- AWS Cognito handles user authentication
- JWT tokens for API authorization
- MFA enabled for admin accounts

**Role-Based Access Control:**
- `investor` - Regular platform users
- `admin` - Administrative access
- `superadmin` - Full system access
- `compliance_staff` - Compliance tools access

### Data Security

**Sensitive Data Handling:**
- Never log sensitive information (passwords, tokens, PII)
- Use environment variables for secrets
- Encrypt sensitive data at rest

**API Security:**
- All endpoints require authentication
- Input validation on all user data
- Rate limiting on public endpoints

### Development Security

**Local Development:**
- Use development databases only
- Never commit secrets to git
- Keep dependencies updated

## 📊 Testing Strategy

### Testing Pyramid

**Unit Tests** (70%)
- Individual function testing
- Component testing
- Business logic validation

**Integration Tests** (20%)
- API endpoint testing
- Database integration
- Service integration

**E2E Tests** (10%)
- Critical user journeys
- Cross-application workflows
- Compliance processes

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests for specific app
cd apps/web && pnpm test
cd apps/api && pnpm test

# Run tests in watch mode
pnpm test --watch

# Run tests with coverage
pnpm test --coverage
```

## 🎯 Your First Week

### Day 1-2: Setup & Exploration
- [ ] Complete environment setup
- [ ] Explore the codebase structure
- [ ] Run all applications locally
- [ ] Join team Slack channels
- [ ] Meet with your team lead

### Day 3-4: First Contribution
- [ ] Pick a "good first issue" ticket
- [ ] Implement the feature/fix
- [ ] Write tests for your changes
- [ ] Create your first pull request

### Day 5: Integration
- [ ] Participate in team standup
- [ ] Review code from other team members
- [ ] Deploy your first change to development
- [ ] Schedule regular 1:1 with your manager

## 📚 Learning Resources

### Internal Documentation
- [System Architecture](/docs/overview/system-architecture)
- [API Documentation](/docs/api)
- [Coding Standards](/docs/getting-started/coding-standards)
- [Testing Guide](/docs/getting-started/testing-guide)

### External Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Prisma Documentation](https://www.prisma.io/docs)
- [AWS Cognito Documentation](https://docs.aws.amazon.com/cognito/)

### Fintech Domain Knowledge
- [Financial Regulations Overview](https://www.sec.gov/investment)
- [Portfolio Management Basics](https://www.investopedia.com/terms/p/portfoliomanagement.asp)
- [KYC/AML Compliance](https://www.investopedia.com/terms/k/knowyourclient.asp)

## 🤝 Getting Help

### When You're Stuck
1. **Check Documentation** - Search this documentation first
2. **Ask in Slack** - Use appropriate channels (#dev-help, #general)
3. **Schedule Pairing** - Pair program with a senior developer
4. **Team Lead** - Escalate to your team lead for complex issues

### Communication Channels
- **#general** - General team communication
- **#dev-help** - Technical questions and support
- **#deployments** - Deployment notifications
- **#compliance** - Compliance and security discussions

## ✅ Onboarding Completion Checklist

By the end of your first week, you should have:

- [ ] Successfully set up your development environment
- [ ] Understood the business domain and platform architecture
- [ ] Made your first code contribution
- [ ] Participated in team processes (standup, code review)
- [ ] Established communication with key team members
- [ ] Identified your learning goals for the next month

---

**Welcome to the team! We're excited to have you contribute to the future of fintech.** 🚀

*For questions about this guide, contact your team lead or post in #dev-help.*
