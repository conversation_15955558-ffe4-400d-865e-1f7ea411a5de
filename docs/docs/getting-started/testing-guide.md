# Testing Guide & Best Practices

*Comprehensive testing strategies and best practices for the Valura AI platform.*

## 🎯 Testing Philosophy

Our testing strategy follows the **Testing Pyramid** principle, emphasizing fast, reliable, and maintainable tests that provide confidence in our financial platform's correctness and security.

### Testing Pyramid

```
        /\
       /  \
      / E2E \     ← 10% - Critical user journeys
     /______\
    /        \
   /Integration\ ← 20% - API and service integration
  /____________\
 /              \
/   Unit Tests   \ ← 70% - Individual functions and components
/________________\
```

## 🧪 Testing Types

### Unit Tests (70%)

**Purpose:** Test individual functions, components, and modules in isolation.

**Characteristics:**
- Fast execution (< 1ms per test)
- No external dependencies
- High code coverage
- Easy to debug

**Example - Business Logic Testing:**
```typescript
// src/services/portfolio.service.test.ts
import { PortfolioService } from './portfolio.service';
import { mockPortfolioData } from '../__mocks__/portfolio.mock';

describe('PortfolioService', () => {
  let portfolioService: PortfolioService;

  beforeEach(() => {
    portfolioService = new PortfolioService();
  });

  describe('calculatePortfolioValue', () => {
    it('should calculate total portfolio value correctly', () => {
      // Arrange
      const portfolio = mockPortfolioData.basicPortfolio;
      const expectedValue = 150000;

      // Act
      const result = portfolioService.calculatePortfolioValue(portfolio);

      // Assert
      expect(result).toBe(expectedValue);
    });

    it('should handle empty portfolio', () => {
      // Arrange
      const emptyPortfolio = { positions: [] };

      // Act
      const result = portfolioService.calculatePortfolioValue(emptyPortfolio);

      // Assert
      expect(result).toBe(0);
    });

    it('should handle portfolio with zero-value positions', () => {
      // Arrange
      const portfolioWithZeroValues = {
        positions: [
          { symbol: 'AAPL', quantity: 100, currentPrice: 0 },
          { symbol: 'GOOGL', quantity: 50, currentPrice: 0 }
        ]
      };

      // Act
      const result = portfolioService.calculatePortfolioValue(portfolioWithZeroValues);

      // Assert
      expect(result).toBe(0);
    });
  });

  describe('calculateRiskMetrics', () => {
    it('should calculate Sharpe ratio correctly', () => {
      // Arrange
      const returns = [0.1, 0.15, 0.08, 0.12, 0.09];
      const riskFreeRate = 0.02;
      const expectedSharpeRatio = 2.45; // Pre-calculated expected value

      // Act
      const result = portfolioService.calculateSharpeRatio(returns, riskFreeRate);

      // Assert
      expect(result).toBeCloseTo(expectedSharpeRatio, 2);
    });

    it('should return 0 for zero volatility portfolio', () => {
      // Arrange
      const constantReturns = [0.05, 0.05, 0.05, 0.05];
      const riskFreeRate = 0.02;

      // Act
      const result = portfolioService.calculateSharpeRatio(constantReturns, riskFreeRate);

      // Assert
      expect(result).toBe(0);
    });
  });
});
```

**Example - React Component Testing:**
```typescript
// src/components/PortfolioSummary.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { PortfolioSummary } from './PortfolioSummary';
import { portfolioService } from '../services/portfolio.service';

// Mock the service
jest.mock('../services/portfolio.service');
const mockPortfolioService = portfolioService as jest.Mocked<typeof portfolioService>;

describe('PortfolioSummary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should display loading state initially', () => {
    // Arrange
    mockPortfolioService.getPortfolio.mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    // Act
    render(<PortfolioSummary userId="user123" />);

    // Assert
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('should display portfolio data when loaded', async () => {
    // Arrange
    const mockPortfolio = {
      totalValue: 150000,
      positions: [
        { symbol: 'AAPL', quantity: 100, currentPrice: 150 },
        { symbol: 'GOOGL', quantity: 50, currentPrice: 2800 }
      ]
    };
    mockPortfolioService.getPortfolio.mockResolvedValue(mockPortfolio);

    // Act
    render(<PortfolioSummary userId="user123" />);

    // Assert
    await waitFor(() => {
      expect(screen.getByText('$150,000.00')).toBeInTheDocument();
    });
  });

  it('should display error message when loading fails', async () => {
    // Arrange
    const errorMessage = 'Failed to load portfolio';
    mockPortfolioService.getPortfolio.mockRejectedValue(new Error(errorMessage));

    // Act
    render(<PortfolioSummary userId="user123" />);

    // Assert
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });
});
```

### Integration Tests (20%)

**Purpose:** Test interactions between different modules, services, and external systems.

**Example - API Integration Testing:**
```typescript
// src/api/portfolio.integration.test.ts
import request from 'supertest';
import { app } from '../app';
import { db } from '../lib/db';
import { createTestUser, createTestPortfolio } from '../__helpers__/test-data';

describe('Portfolio API Integration', () => {
  let testUser: any;
  let authToken: string;

  beforeAll(async () => {
    // Set up test database
    await db.$connect();
  });

  beforeEach(async () => {
    // Create test user and get auth token
    testUser = await createTestUser();
    authToken = await getAuthToken(testUser.email, 'testpassword');
  });

  afterEach(async () => {
    // Clean up test data
    await db.portfolio.deleteMany({ where: { userId: testUser.id } });
    await db.user.delete({ where: { id: testUser.id } });
  });

  afterAll(async () => {
    await db.$disconnect();
  });

  describe('GET /api/portfolio', () => {
    it('should return user portfolio with correct structure', async () => {
      // Arrange
      const testPortfolio = await createTestPortfolio(testUser.id);

      // Act
      const response = await request(app)
        .get('/api/portfolio')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Assert
      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: testPortfolio.id,
          totalValue: expect.any(Number),
          positions: expect.any(Array),
          riskLevel: expect.stringMatching(/^(low|medium|high)$/),
          createdAt: expect.any(String),
          updatedAt: expect.any(String)
        }
      });
    });

    it('should return 401 for unauthenticated requests', async () => {
      // Act & Assert
      await request(app)
        .get('/api/portfolio')
        .expect(401);
    });
  });

  describe('POST /api/portfolio', () => {
    it('should create new portfolio with valid data', async () => {
      // Arrange
      const portfolioData = {
        name: 'Test Portfolio',
        riskLevel: 'medium',
        initialInvestment: 10000
      };

      // Act
      const response = await request(app)
        .post('/api/portfolio')
        .set('Authorization', `Bearer ${authToken}`)
        .send(portfolioData)
        .expect(201);

      // Assert
      expect(response.body.data).toMatchObject({
        name: portfolioData.name,
        riskLevel: portfolioData.riskLevel,
        userId: testUser.id
      });

      // Verify in database
      const createdPortfolio = await db.portfolio.findUnique({
        where: { id: response.body.data.id }
      });
      expect(createdPortfolio).toBeTruthy();
    });

    it('should validate input data and return 400 for invalid data', async () => {
      // Arrange
      const invalidData = {
        name: '', // Invalid: empty name
        riskLevel: 'invalid', // Invalid: not in enum
        initialInvestment: -1000 // Invalid: negative amount
      };

      // Act & Assert
      const response = await request(app)
        .post('/api/portfolio')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });
});
```

### End-to-End Tests (10%)

**Purpose:** Test complete user workflows across the entire application.

**Example - Critical User Journey:**
```typescript
// e2e/portfolio-management.e2e.test.ts
import { test, expect } from '@playwright/test';

test.describe('Portfolio Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as test user
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'testpassword');
    await page.click('[data-testid="login-button"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should complete full portfolio creation and management flow', async ({ page }) => {
    // Step 1: Navigate to portfolio creation
    await page.click('[data-testid="create-portfolio-button"]');
    await expect(page).toHaveURL('/portfolio/create');

    // Step 2: Fill portfolio creation form
    await page.fill('[data-testid="portfolio-name"]', 'My Test Portfolio');
    await page.selectOption('[data-testid="risk-level"]', 'medium');
    await page.fill('[data-testid="initial-investment"]', '10000');
    await page.click('[data-testid="create-button"]');

    // Step 3: Verify portfolio creation
    await expect(page).toHaveURL(/\/portfolio\/[a-zA-Z0-9]+/);
    await expect(page.locator('[data-testid="portfolio-name"]')).toContainText('My Test Portfolio');
    await expect(page.locator('[data-testid="portfolio-value"]')).toContainText('$10,000.00');

    // Step 4: Add a position to the portfolio
    await page.click('[data-testid="add-position-button"]');
    await page.fill('[data-testid="symbol-input"]', 'AAPL');
    await page.fill('[data-testid="quantity-input"]', '10');
    await page.click('[data-testid="add-position-submit"]');

    // Step 5: Verify position was added
    await expect(page.locator('[data-testid="position-AAPL"]')).toBeVisible();
    await expect(page.locator('[data-testid="position-AAPL"] [data-testid="quantity"]')).toContainText('10');

    // Step 6: View portfolio analytics
    await page.click('[data-testid="analytics-tab"]');
    await expect(page.locator('[data-testid="sharpe-ratio"]')).toBeVisible();
    await expect(page.locator('[data-testid="volatility"]')).toBeVisible();

    // Step 7: Generate portfolio report
    await page.click('[data-testid="generate-report-button"]');
    await expect(page.locator('[data-testid="report-download-link"]')).toBeVisible();
  });

  test('should handle portfolio risk limit validation', async ({ page }) => {
    // Navigate to high-risk trade
    await page.goto('/portfolio/test-portfolio-id');
    await page.click('[data-testid="add-position-button"]');
    
    // Try to add position that exceeds risk limits
    await page.fill('[data-testid="symbol-input"]', 'TSLA');
    await page.fill('[data-testid="quantity-input"]', '1000'); // Large position
    await page.click('[data-testid="add-position-submit"]');

    // Verify risk warning appears
    await expect(page.locator('[data-testid="risk-warning"]')).toBeVisible();
    await expect(page.locator('[data-testid="risk-warning"]')).toContainText('exceeds risk limits');

    // Confirm override (if user has permission)
    await page.click('[data-testid="confirm-risk-override"]');
    
    // Verify position was added with risk flag
    await expect(page.locator('[data-testid="position-TSLA"]')).toBeVisible();
    await expect(page.locator('[data-testid="position-TSLA"] [data-testid="risk-flag"]')).toBeVisible();
  });
});
```

## 🔧 Testing Tools & Setup

### Jest Configuration

**jest.config.js:**
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: [
    '**/__tests__/**/*.+(ts|tsx|js)',
    '**/*.(test|spec).+(ts|tsx|js)'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/**/__mocks__/**',
    '!src/**/__tests__/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.ts']
};
```

### Test Setup Files

**src/__tests__/setup.ts:**
```typescript
import '@testing-library/jest-dom';
import { server } from '../__mocks__/server';

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/valura_test';
process.env.JWT_SECRET = 'test-secret';

// Setup MSW (Mock Service Worker)
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock console methods in tests
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock window.matchMedia for React components
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});
```

### Mock Data & Helpers

**src/__mocks__/portfolio.mock.ts:**
```typescript
export const mockPortfolioData = {
  basicPortfolio: {
    id: 'portfolio-123',
    userId: 'user-123',
    name: 'Test Portfolio',
    totalValue: 150000,
    riskLevel: 'medium' as const,
    positions: [
      {
        id: 'position-1',
        symbol: 'AAPL',
        quantity: 100,
        currentPrice: 150,
        averageCost: 140,
        marketValue: 15000
      },
      {
        id: 'position-2',
        symbol: 'GOOGL',
        quantity: 50,
        currentPrice: 2700,
        averageCost: 2600,
        marketValue: 135000
      }
    ],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  },

  emptyPortfolio: {
    id: 'portfolio-empty',
    userId: 'user-123',
    name: 'Empty Portfolio',
    totalValue: 0,
    riskLevel: 'low' as const,
    positions: [],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  }
};

export const createMockPortfolio = (overrides: Partial<Portfolio> = {}): Portfolio => {
  return {
    ...mockPortfolioData.basicPortfolio,
    ...overrides
  };
};
```

## 📊 Test Coverage & Quality

### Coverage Requirements

**Minimum Coverage Thresholds:**
- **Statements:** 80%
- **Branches:** 80%
- **Functions:** 80%
- **Lines:** 80%

**Critical Code Coverage:**
- **Financial calculations:** 95%
- **Security functions:** 95%
- **API endpoints:** 90%
- **Business logic:** 85%

### Running Tests

**Local Development:**
```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test --watch

# Run tests with coverage
pnpm test --coverage

# Run specific test file
pnpm test portfolio.service.test.ts

# Run tests matching pattern
pnpm test --testNamePattern="calculate"
```

**CI/CD Pipeline:**
```bash
# Run tests with coverage and generate reports
pnpm test --coverage --ci --watchAll=false --passWithNoTests

# Run E2E tests
pnpm test:e2e

# Run integration tests only
pnpm test --testPathPattern=integration
```

## 🚀 Testing Best Practices

### Test Structure (AAA Pattern)

```typescript
describe('Feature', () => {
  it('should behave correctly under specific conditions', () => {
    // Arrange - Set up test data and conditions
    const input = createTestData();
    const expectedOutput = calculateExpectedResult(input);

    // Act - Execute the code under test
    const result = functionUnderTest(input);

    // Assert - Verify the results
    expect(result).toEqual(expectedOutput);
  });
});
```

### Test Naming Conventions

**Descriptive Test Names:**
```typescript
// ✅ Good - Describes behavior and conditions
describe('PortfolioService.calculateRiskMetrics', () => {
  it('should return high risk level when portfolio volatility exceeds 20%', () => {});
  it('should handle missing price data by using last known prices', () => {});
  it('should throw ValidationError when portfolio contains invalid positions', () => {});
});

// ❌ Bad - Vague or implementation-focused
describe('PortfolioService', () => {
  it('should work', () => {});
  it('should call calculateVolatility method', () => {});
  it('should return object', () => {});
});
```

### Test Data Management

**Use Factories for Test Data:**
```typescript
// src/__helpers__/test-factories.ts
export class PortfolioFactory {
  static create(overrides: Partial<Portfolio> = {}): Portfolio {
    return {
      id: `portfolio-${Math.random().toString(36).substr(2, 9)}`,
      userId: 'test-user-id',
      name: 'Test Portfolio',
      totalValue: 100000,
      riskLevel: 'medium',
      positions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  }

  static withPositions(positions: Position[]): Portfolio {
    return this.create({
      positions,
      totalValue: positions.reduce((sum, p) => sum + p.marketValue, 0)
    });
  }

  static highRisk(): Portfolio {
    return this.create({
      riskLevel: 'high',
      positions: [
        PositionFactory.create({ symbol: 'TSLA', quantity: 100 }),
        PositionFactory.create({ symbol: 'GME', quantity: 50 })
      ]
    });
  }
}
```

### Async Testing

**Proper Async Test Handling:**
```typescript
describe('Async operations', () => {
  it('should handle async portfolio calculation', async () => {
    // Arrange
    const portfolioId = 'test-portfolio';
    const mockData = { totalValue: 150000 };
    jest.spyOn(portfolioService, 'fetchPortfolioData')
      .mockResolvedValue(mockData);

    // Act
    const result = await portfolioService.calculatePortfolioMetrics(portfolioId);

    // Assert
    expect(result.totalValue).toBe(150000);
    expect(portfolioService.fetchPortfolioData).toHaveBeenCalledWith(portfolioId);
  });

  it('should handle async errors gracefully', async () => {
    // Arrange
    const portfolioId = 'invalid-portfolio';
    jest.spyOn(portfolioService, 'fetchPortfolioData')
      .mockRejectedValue(new Error('Portfolio not found'));

    // Act & Assert
    await expect(portfolioService.calculatePortfolioMetrics(portfolioId))
      .rejects.toThrow('Portfolio not found');
  });
});
```

## 🔍 Testing Security & Compliance

### Security Testing

**Authentication & Authorization Tests:**
```typescript
describe('Security Tests', () => {
  it('should reject requests without valid JWT token', async () => {
    const response = await request(app)
      .get('/api/portfolio')
      .expect(401);

    expect(response.body.error.code).toBe('UNAUTHORIZED');
  });

  it('should prevent access to other users portfolios', async () => {
    const userAToken = await getAuthToken('<EMAIL>');
    const userBPortfolioId = 'user-b-portfolio-id';

    const response = await request(app)
      .get(`/api/portfolio/${userBPortfolioId}`)
      .set('Authorization', `Bearer ${userAToken}`)
      .expect(403);

    expect(response.body.error.code).toBe('FORBIDDEN');
  });

  it('should sanitize input to prevent injection attacks', async () => {
    const maliciousInput = {
      name: '<script>alert("xss")</script>',
      description: 'DROP TABLE portfolios;'
    };

    const response = await request(app)
      .post('/api/portfolio')
      .set('Authorization', `Bearer ${validToken}`)
      .send(maliciousInput)
      .expect(400);

    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });
});
```

### Compliance Testing

**Audit Trail Testing:**
```typescript
describe('Compliance Tests', () => {
  it('should log all portfolio modifications for audit trail', async () => {
    // Arrange
    const portfolioUpdate = { name: 'Updated Portfolio' };
    const logSpy = jest.spyOn(auditLogger, 'logAction');

    // Act
    await request(app)
      .put('/api/portfolio/test-id')
      .set('Authorization', `Bearer ${validToken}`)
      .send(portfolioUpdate)
      .expect(200);

    // Assert
    expect(logSpy).toHaveBeenCalledWith({
      action: 'PORTFOLIO_UPDATED',
      userId: 'test-user-id',
      resourceId: 'test-id',
      changes: portfolioUpdate,
      timestamp: expect.any(Date)
    });
  });

  it('should enforce KYC requirements for large transactions', async () => {
    // Arrange
    const largeOrder = {
      symbol: 'AAPL',
      quantity: 10000, // Large order requiring KYC
      orderType: 'market'
    };

    // Mock user without completed KYC
    jest.spyOn(kycService, 'isKycCompleted').mockResolvedValue(false);

    // Act & Assert
    const response = await request(app)
      .post('/api/orders')
      .set('Authorization', `Bearer ${validToken}`)
      .send(largeOrder)
      .expect(400);

    expect(response.body.error.code).toBe('KYC_REQUIRED');
  });
});
```

## 📈 Performance Testing

**Load Testing Example:**
```typescript
describe('Performance Tests', () => {
  it('should handle portfolio calculation within acceptable time limits', async () => {
    // Arrange
    const largePortfolio = PortfolioFactory.withPositions(
      Array.from({ length: 1000 }, (_, i) => 
        PositionFactory.create({ symbol: `STOCK${i}` })
      )
    );

    // Act
    const startTime = Date.now();
    const result = await portfolioService.calculatePortfolioMetrics(largePortfolio);
    const endTime = Date.now();

    // Assert
    expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    expect(result).toBeDefined();
  });
});
```

---

*This testing guide ensures our financial platform maintains the highest standards of quality, security, and reliability. Regular updates to testing practices help us stay current with industry best practices.*
