# Coding Standards & Best Practices

*Comprehensive coding standards for the Valura AI development team.*

## 🎯 Overview

This document establishes coding standards and best practices for the Valura AI platform. Following these guidelines ensures code consistency, maintainability, and security across our fintech application.

## 📋 General Principles

### Code Quality Principles

1. **Readability First** - Code is read more often than it's written
2. **Consistency** - Follow established patterns throughout the codebase
3. **Security by Design** - Consider security implications in every decision
4. **Performance Awareness** - Write efficient code, especially for financial calculations
5. **Maintainability** - Write code that's easy to modify and extend

### SOLID Principles

- **Single Responsibility** - Each function/class should have one reason to change
- **Open/Closed** - Open for extension, closed for modification
- **Liskov Substitution** - Derived classes must be substitutable for base classes
- **Interface Segregation** - Many client-specific interfaces are better than one general-purpose interface
- **Dependency Inversion** - Depend on abstractions, not concretions

## 🔧 TypeScript Standards

### Type Definitions

**Use Explicit Types:**
```typescript
// ✅ Good - Explicit types
interface UserPortfolio {
  userId: string;
  totalValue: number;
  positions: Position[];
  lastUpdated: Date;
}

function calculatePortfolioValue(portfolio: UserPortfolio): number {
  return portfolio.positions.reduce((total, position) => {
    return total + (position.quantity * position.currentPrice);
  }, 0);
}

// ❌ Bad - Implicit any types
function calculateValue(portfolio: any): any {
  return portfolio.positions.reduce((total: any, position: any) => {
    return total + (position.quantity * position.currentPrice);
  }, 0);
}
```

**Prefer Interfaces Over Types:**
```typescript
// ✅ Good - Interface for object shapes
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: Date;
}

// ✅ Good - Type for unions and primitives
type OrderStatus = 'pending' | 'executed' | 'cancelled' | 'failed';
type UserId = string;

// ❌ Bad - Type for object shapes
type ApiResponseType<T> = {
  data: T;
  success: boolean;
  message?: string;
  timestamp: Date;
};
```

### Naming Conventions

**Variables and Functions:**
```typescript
// ✅ Good - Descriptive camelCase
const userPortfolioValue = 150000;
const isKycCompleted = true;
const hasActiveOrders = false;

function calculateRiskAdjustedReturn(portfolio: Portfolio): number {
  // Implementation
}

// ❌ Bad - Unclear or abbreviated names
const val = 150000;
const kyc = true;
const orders = false;

function calcRAR(p: any): number {
  // Implementation
}
```

**Constants:**
```typescript
// ✅ Good - SCREAMING_SNAKE_CASE for constants
const MAX_PORTFOLIO_POSITIONS = 100;
const DEFAULT_RISK_TOLERANCE = 'medium';
const API_ENDPOINTS = {
  PORTFOLIO: '/api/portfolio',
  ORDERS: '/api/orders',
  KYC: '/api/kyc'
} as const;

// ❌ Bad - camelCase for constants
const maxPositions = 100;
const defaultRisk = 'medium';
```

**Classes and Interfaces:**
```typescript
// ✅ Good - PascalCase
interface PortfolioAnalytics {
  sharpeRatio: number;
  volatility: number;
  beta: number;
}

class RiskAssessmentService {
  private riskModels: RiskModel[];
  
  public assessPortfolioRisk(portfolio: Portfolio): RiskAssessment {
    // Implementation
  }
}

// ❌ Bad - camelCase or snake_case
interface portfolio_analytics {
  sharpe_ratio: number;
  volatility: number;
  beta: number;
}
```

## ⚛️ React/Next.js Standards

### Component Structure

**Functional Components with TypeScript:**
```typescript
// ✅ Good - Functional component with proper typing
interface PortfolioSummaryProps {
  userId: string;
  showDetails?: boolean;
  onPortfolioUpdate?: (portfolio: Portfolio) => void;
}

export function PortfolioSummary({ 
  userId, 
  showDetails = false, 
  onPortfolioUpdate 
}: PortfolioSummaryProps) {
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPortfolio() {
      try {
        setIsLoading(true);
        const data = await portfolioService.getPortfolio(userId);
        setPortfolio(data);
        onPortfolioUpdate?.(data);
      } catch (err) {
        setError(getErrorMessage(err));
      } finally {
        setIsLoading(false);
      }
    }

    fetchPortfolio();
  }, [userId, onPortfolioUpdate]);

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;
  if (!portfolio) return <EmptyState message="No portfolio data available" />;

  return (
    <div className="portfolio-summary">
      <h2>Portfolio Summary</h2>
      <div className="total-value">
        ${formatCurrency(portfolio.totalValue)}
      </div>
      {showDetails && (
        <PortfolioDetails portfolio={portfolio} />
      )}
    </div>
  );
}
```

### Custom Hooks

**Extract Reusable Logic:**
```typescript
// ✅ Good - Custom hook for portfolio data
export function usePortfolio(userId: string) {
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshPortfolio = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await portfolioService.getPortfolio(userId);
      setPortfolio(data);
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    refreshPortfolio();
  }, [refreshPortfolio]);

  return {
    portfolio,
    isLoading,
    error,
    refreshPortfolio
  };
}
```

### State Management

**Use Zustand for Global State:**
```typescript
// ✅ Good - Zustand store with TypeScript
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,

  login: async (credentials) => {
    set({ isLoading: true });
    try {
      const user = await authService.login(credentials);
      set({ user, isAuthenticated: true, isLoading: false });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  logout: () => {
    authService.logout();
    set({ user: null, isAuthenticated: false });
  },

  refreshUser: async () => {
    try {
      const user = await authService.getCurrentUser();
      set({ user, isAuthenticated: true });
    } catch (error) {
      set({ user: null, isAuthenticated: false });
    }
  }
}));
```

## 🗄️ Database & API Standards

### Prisma Schema Conventions

**Model Naming:**
```prisma
// ✅ Good - Singular PascalCase model names
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  firstName String   @map("first_name")
  lastName  String   @map("last_name")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  portfolios Portfolio[]
  orders     Order[]

  @@map("users")
}

model Portfolio {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  totalValue   Decimal  @map("total_value") @db.Decimal(15, 2)
  riskLevel    RiskLevel @map("risk_level")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  user      User       @relation(fields: [userId], references: [id])
  positions Position[]

  @@map("portfolios")
}
```

### API Design Standards

**RESTful Endpoints:**
```typescript
// ✅ Good - RESTful API design
// GET /api/users/:userId/portfolios - Get user portfolios
// POST /api/users/:userId/portfolios - Create new portfolio
// GET /api/portfolios/:portfolioId - Get specific portfolio
// PUT /api/portfolios/:portfolioId - Update portfolio
// DELETE /api/portfolios/:portfolioId - Delete portfolio

// API Response format
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationInfo;
    timestamp: string;
  };
}

// ✅ Good - Consistent error handling
export async function handleApiRequest<T>(
  handler: () => Promise<T>
): Promise<ApiResponse<T>> {
  try {
    const data = await handler();
    return {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString()
      }
    };
  } catch (error) {
    logger.error('API request failed', { error });
    
    return {
      success: false,
      error: {
        code: error.code || 'INTERNAL_ERROR',
        message: error.message || 'An unexpected error occurred'
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    };
  }
}
```

## 🔒 Security Standards

### Input Validation

**Use Zod for Schema Validation:**
```typescript
// ✅ Good - Comprehensive input validation
import { z } from 'zod';

const createPortfolioSchema = z.object({
  name: z.string().min(1).max(100),
  riskLevel: z.enum(['low', 'medium', 'high']),
  initialInvestment: z.number().positive().max(10_000_000),
  investmentGoals: z.array(z.string()).min(1).max(5),
  timeHorizon: z.number().int().min(1).max(50) // years
});

export async function createPortfolio(
  req: Request,
  res: Response
): Promise<void> {
  try {
    // Validate input
    const validatedData = createPortfolioSchema.parse(req.body);
    
    // Business logic
    const portfolio = await portfolioService.create(
      req.user.id,
      validatedData
    );
    
    res.json({
      success: true,
      data: portfolio
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: error.errors
        }
      });
    } else {
      throw error;
    }
  }
}
```

### Sensitive Data Handling

**Never Log Sensitive Information:**
```typescript
// ✅ Good - Safe logging
function logUserAction(userId: string, action: string, metadata: any) {
  logger.info('User action performed', {
    userId,
    action,
    timestamp: new Date().toISOString(),
    // Only log non-sensitive metadata
    metadata: sanitizeForLogging(metadata)
  });
}

function sanitizeForLogging(data: any): any {
  const sensitiveFields = ['password', 'ssn', 'creditCard', 'bankAccount'];
  
  if (typeof data !== 'object' || data === null) {
    return data;
  }
  
  const sanitized = { ...data };
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

// ❌ Bad - Logging sensitive data
function badLogging(user: User, password: string) {
  logger.info('User login attempt', {
    user, // Contains PII
    password, // Sensitive credential
    timestamp: new Date()
  });
}
```

## 📝 Documentation Standards

### Code Comments

**Write Meaningful Comments:**
```typescript
// ✅ Good - Explains why, not what
/**
 * Calculates the Sharpe ratio for a portfolio.
 * 
 * The Sharpe ratio measures risk-adjusted return by comparing
 * the excess return to the standard deviation of returns.
 * We use the risk-free rate from the current 10-year Treasury bond.
 * 
 * @param returns - Array of portfolio returns (as decimals)
 * @param riskFreeRate - Current risk-free rate (as decimal)
 * @returns Sharpe ratio (higher is better)
 */
function calculateSharpeRatio(returns: number[], riskFreeRate: number): number {
  const excessReturns = returns.map(r => r - riskFreeRate);
  const avgExcessReturn = excessReturns.reduce((sum, r) => sum + r, 0) / excessReturns.length;
  const stdDev = calculateStandardDeviation(excessReturns);
  
  // Avoid division by zero for portfolios with no volatility
  return stdDev === 0 ? 0 : avgExcessReturn / stdDev;
}

// ❌ Bad - States the obvious
// Loops through returns and calculates Sharpe ratio
function calculateSharpeRatio(returns: number[], riskFreeRate: number): number {
  // Calculate excess returns
  const excessReturns = returns.map(r => r - riskFreeRate);
  // Calculate average
  const avgExcessReturn = excessReturns.reduce((sum, r) => sum + r, 0) / excessReturns.length;
  // Calculate standard deviation
  const stdDev = calculateStandardDeviation(excessReturns);
  // Return ratio
  return stdDev === 0 ? 0 : avgExcessReturn / stdDev;
}
```

### JSDoc Standards

**Comprehensive Function Documentation:**
```typescript
/**
 * Executes a trade order with proper validation and risk checks.
 * 
 * This function performs comprehensive validation including:
 * - Portfolio balance verification
 * - Risk limit checks
 * - Market hours validation
 * - Compliance screening
 * 
 * @param userId - The ID of the user placing the order
 * @param orderRequest - The order details to execute
 * @param options - Additional execution options
 * @param options.bypassRiskChecks - Skip risk validation (admin only)
 * @param options.dryRun - Validate order without executing
 * 
 * @returns Promise resolving to the executed order details
 * 
 * @throws {ValidationError} When order data is invalid
 * @throws {InsufficientFundsError} When portfolio lacks sufficient balance
 * @throws {RiskLimitExceededError} When order exceeds risk limits
 * @throws {MarketClosedError} When market is closed for trading
 * 
 * @example
 * ```typescript
 * const order = await executeTradeOrder('user123', {
 *   symbol: 'AAPL',
 *   quantity: 100,
 *   orderType: 'market',
 *   side: 'buy'
 * });
 * ```
 */
async function executeTradeOrder(
  userId: string,
  orderRequest: OrderRequest,
  options: ExecutionOptions = {}
): Promise<ExecutedOrder> {
  // Implementation
}
```

## ✅ Code Review Checklist

### Before Submitting PR

- [ ] **Functionality** - Code works as intended
- [ ] **Tests** - Adequate test coverage (>80%)
- [ ] **Security** - No security vulnerabilities
- [ ] **Performance** - No performance regressions
- [ ] **Documentation** - Code is well-documented
- [ ] **Standards** - Follows coding standards
- [ ] **Dependencies** - No unnecessary dependencies added

### Review Criteria

**Code Quality:**
- [ ] Clear and descriptive variable/function names
- [ ] Proper error handling and logging
- [ ] No code duplication
- [ ] Appropriate abstraction levels

**Security:**
- [ ] Input validation implemented
- [ ] No sensitive data in logs
- [ ] Proper authentication/authorization
- [ ] SQL injection prevention

**Performance:**
- [ ] Efficient algorithms used
- [ ] Database queries optimized
- [ ] No memory leaks
- [ ] Appropriate caching strategies

## 🛠️ Tools & Automation

### ESLint Configuration

**Strict Linting Rules:**
```json
{
  "extends": [
    "@repo/eslint-config/react-internal",
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "@typescript-eslint/no-explicit-any": "error",
    "prefer-const": "error",
    "no-console": "warn"
  }
}
```

### Prettier Configuration

**Consistent Formatting:**
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### Pre-commit Hooks

**Automated Quality Checks:**
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "pre-push": "pnpm test"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
```

---

*These coding standards are living documents that evolve with our team and technology. Suggest improvements through pull requests or team discussions.*
