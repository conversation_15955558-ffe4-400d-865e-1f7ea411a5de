from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1.question import router as questionnaire_router
from app.api.v1.company_profile import router as company_analysis_router
from app.api.v1.portfolio_weights import router as portfolio_weights_router
from app.api.v1.metrics import router as metrics_router
from app.api.v1.risk import router as scores_router
from app.api.v1.returns import router as return_router

def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    app = FastAPI(title="Stock Analysis API", version="0.1.0")

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    app.include_router(questionnaire_router, prefix="/api/v1/questionnaire", tags=["Questionnaire"])
    app.include_router(company_analysis_router, prefix="/api/v1/analysis", tags=["Company Analysis"])
    app.include_router(portfolio_weights_router, prefix="/api/v1/portfolio", tags=["Portfolio Weights"])
    app.include_router(metrics_router, prefix="/api/v1/metrics", tags=["Metrics"])
    app.include_router(scores_router, prefix="/api/v1/scores", tags=["Scores"])
    app.include_router(return_router, prefix="/api/v1/returns", tags=["Returns"])

    @app.get("/", tags=["Root"])
    def read_root():
        return {"message": "Welcome to Stock Analysis API"}

    return app


app = create_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="localhost", port=8000) 