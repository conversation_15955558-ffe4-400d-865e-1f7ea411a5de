import httpx
from datetime import datetime, timedelta
from typing import List, Dict, Any
from app.config.settings import get_settings
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FMPClient:
    """Wrapper around FMP REST API for historical OHLCV data, company profiles, and forex."""

    BASE_URL: str = "https://financialmodelingprep.com/api/v3"

    def __init__(self):
        settings = get_settings()
        self.api_key = settings.FMP_API_KEY
        self.client = httpx.AsyncClient(base_url=self.BASE_URL, timeout=20)

    async def get_historical_daily(self, symbol: str, lookback_days: int = 252*5) -> List[Dict[str, Any]]:
        """Fetch last `lookback_days` of daily OHLCV data for *symbol*.

        Args:
            symbol: Stock ticker symbol (e.g., TATAMOTORS.NS or TATAMOTORS)
            lookback_days: Number of days to look back

        Returns:
            List of dicts containing date, open, high, low, close, volume.
        """
        # Try multiple symbol formats
        symbol_formats = [symbol, f"{symbol}.NS", f"{symbol}.BO"] if not (symbol.endswith('.NS') or symbol.endswith('.BO')) else [symbol]
        for formatted_symbol in symbol_formats:
            endpoint = f"/historical-price-full/{formatted_symbol}?timeseries={lookback_days}&apikey={self.api_key}"
            try:
                logger.info(f"Fetching historical data for {formatted_symbol} with lookback {lookback_days} days")
                r = await self.client.get(endpoint)
                r.raise_for_status()
                data = r.json()
                historical = data.get("historical", [])
                if historical:
                    logger.info(f"Successfully fetched {len(historical)} days of data for {formatted_symbol}")
                    return historical
                logger.warning(f"No historical data returned for {formatted_symbol}")
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error for {formatted_symbol}: {e.response.status_code} - {e.response.text}")
            except Exception as e:
                logger.error(f"Failed to fetch data for {formatted_symbol}: {str(e)}")
        logger.error(f"No data found for {symbol} after trying all formats: {symbol_formats}")
        return []

    async def get_company_profile(self, symbol: str) -> Dict[str, Any]:
        """Fetch company profile data for a symbol."""
        endpoint = f"/profile/{symbol}?apikey={self.api_key}"
        try:
            logger.info(f"Fetching company profile for {symbol}")
            r = await self.client.get(endpoint)
            r.raise_for_status()
            data = r.json()
            result = data[0] if isinstance(data, list) and data else data
            logger.info(f"Successfully fetched profile for {symbol}")
            return result
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error for profile {symbol}: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Failed to fetch profile for {symbol}: {str(e)}")
            raise

    async def get_batch_company_profiles(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Fetch company profiles for multiple symbols in batch.
        
        Returns dict mapping symbol -> profile data.
        """
        import asyncio
        tasks = [self.get_company_profile(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        profiles = {}
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                logger.error(f"Failed to fetch profile for {symbol}: {result}")
                profiles[symbol] = None
            else:
                profiles[symbol] = result
        return profiles
    
    async def get_forex_quote(self, currency_pair: str) -> Dict[str, Any]:
        """Get real-time forex quote for a currency pair.
        
        Args:
            currency_pair: Currency pair like "EURUSD", "GBPUSD", etc.
            
        Returns:
            Dict with current exchange rate and related data
        """
        endpoint = f"/quote/{currency_pair}?apikey={self.api_key}"
        r = await self.client.get(endpoint)
        r.raise_for_status()
        data = r.json()
        return data[0] if isinstance(data, list) and data else data
    
    async def get_historical_forex(self, currency_pair: str, lookback_days: int = 252) -> List[Dict[str, Any]]:
        """Fetch historical forex data for a currency pair.
        
        Args:
            currency_pair: Currency pair like "EURUSD"
            lookback_days: Number of days to look back
            
        Returns:
            List of historical exchange rate data
        """
        endpoint = f"/historical-price-full/{currency_pair}?timeseries={lookback_days}&apikey={self.api_key}"
        r = await self.client.get(endpoint)
        r.raise_for_status()
        data = r.json()
        return data.get("historical", [])

    async def get_historical_forex_range(self, currency_pair: str, from_date: str, to_date: str) -> List[Dict[str, Any]]:
        """Fetch historical forex data for a specific date range."""
        endpoint = f"/historical-price-full/{currency_pair}?from={from_date}&to={to_date}&apikey={self.api_key}"
        try:
            logger.info(f"Fetching forex data for {currency_pair} from {from_date} to {to_date}")
            r = await self.client.get(endpoint)
            r.raise_for_status()
            data = r.json()
            historical = data.get("historical", [])
            logger.info(f"Successfully fetched {len(historical)} forex data points for {currency_pair}")
            return historical
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error for forex {currency_pair}: {e.response.status_code} - {e.response.text}")
            return []
        except Exception as e:
            logger.error(f"Failed to fetch forex data for {currency_pair}: {str(e)}")
            return []
        
    async def get_available_forex_pairs(self) -> List[Dict[str, Any]]:
        """Get list of available forex pairs.
        
        Returns:
            List of available currency pairs
        """
        endpoint = f"/forex-list?apikey={self.api_key}"
        r = await self.client.get(endpoint)
        r.raise_for_status()
        return r.json()

    async def get_all_forex_quotes(self) -> List[Dict[str, Any]]:
        """Get real-time quotes for all forex pairs.
        
        Returns:
            List of current forex quotes
        """
        endpoint = f"/forex?apikey={self.api_key}"
        r = await self.client.get(endpoint)
        r.raise_for_status()
        data = r.json()
        return data.get("forexList", []) if isinstance(data, dict) else data

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()