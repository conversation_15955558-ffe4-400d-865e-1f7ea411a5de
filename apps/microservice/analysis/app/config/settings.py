import os
from dotenv import load_dotenv
from functools import lru_cache

load_dotenv()

class Settings:
    """Application configuration pulled from environment variables."""

    def __init__(self):
        self.FMP_API_KEY: str = os.getenv("FMP_API_KEY")
        if not self.FMP_API_KEY:
            raise ValueError("FMP_API_KEY environment variable is not set.")

@lru_cache()
def get_settings() -> Settings:
    return Settings()
