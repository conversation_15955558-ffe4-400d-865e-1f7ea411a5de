from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
from scipy import stats
from app.utils.fmp import FMPClient

WEIGHTS = {
    "Skewness": 0.10,
    "Downside Deviation": 0.20,
    "Omega Ratio": 0.20,
    "CVaR (95%)": 0.20,
    "Ulcer Index": 0.15,
    "Tail Ratio": 0.15,
}

THRESHOLDS = {
    "Skewness": {"10": 0.3, "7": 0.0, "4": -0.3, "1": float("-inf")},
    "Downside Deviation": {"10": 4.0, "7": 7.0, "4": 10.0, "1": float("inf")},
    "Omega Ratio": {"10": 2.5, "7": 1.8, "4": 1.2, "1": 0.0},
    "CVaR (95%)": {"10": -4.0, "7": -7.0, "4": -10.0, "1": float("-inf")},
    "Ulcer Index": {"10": 1.5, "7": 3.0, "4": 5.0, "1": float("inf")},
    "Tail Ratio": {"10": 1.5, "7": 1.2, "4": 0.9, "1": 0.0},
}

RISK_FREE_RATE_ANNUAL = 0.0435
TRADING_DAYS = 252


class MetricsService:
    def __init__(self):
        self.fmp = FMPClient()

    async def get_price_df(self, symbol: str) -> pd.DataFrame:
        raw = await self.fmp.get_historical_daily(symbol, lookback_days=TRADING_DAYS)
        if not raw:
            raise ValueError(f"No price data for symbol {symbol}")
        df = pd.DataFrame(raw)
        df["date"] = pd.to_datetime(df["date"])
        df.sort_values("date", inplace=True)
        return df.tail(TRADING_DAYS)  # Ensure 1 year data only

    @staticmethod
    def calculate_downside_metrics(df: pd.DataFrame) -> Dict[str, float]:
        # Calculate returns
        df = df.copy()
        df["daily_return"] = df["close"].pct_change()
        returns = df["daily_return"].dropna()

        # Skewness of returns
        skewness = stats.skew(returns)

        # Downside Deviation (volatility of returns below 0)
        downside_returns = returns[returns < 0]
        downside_deviation = np.sqrt(np.mean(downside_returns ** 2)) * 100 if len(downside_returns) > 0 else 0.0

        # Omega Ratio (sum gains / sum losses)
        threshold = 0.0
        gains = returns[returns > threshold].sum()
        losses = -returns[returns < threshold].sum()
        omega_ratio = gains / losses if losses > 0 else float("inf")

        # Conditional Value at Risk (CVaR 95%)
        var_95 = returns.quantile(0.05)
        cvar_95 = returns[returns <= var_95].mean() * 100 if len(returns) > 0 else 0.0

        # Ulcer Index (drawdown depth and duration)
        df["cum_max"] = df["close"].cummax()
        df["drawdown"] = (df["cum_max"] - df["close"]) / df["cum_max"]
        ulcer_index = np.sqrt(np.mean(df["drawdown"] ** 2)) * 100  # Scale as percentage

        # Tail Ratio (95th percentile gain / 5th percentile loss magnitude)
        pos_tail = np.percentile(returns, 95)
        neg_tail = -np.percentile(returns, 5)
        tail_ratio = pos_tail / neg_tail if neg_tail > 0 else float("inf")

        return {
            "Skewness": skewness,
            "Downside Deviation": downside_deviation,
            "Omega Ratio": omega_ratio,
            "CVaR (95%)": cvar_95,
            "Ulcer Index": ulcer_index,
            "Tail Ratio": tail_ratio,
        }

    @staticmethod
    def map_to_score(value: float, metric: str) -> int:
        # Maps metric value to score 1-10 based on thresholds from your table
        if metric not in THRESHOLDS:
            return 5  # default score middle

        thresholds = THRESHOLDS[metric]

        if metric in ["Downside Deviation", "Ulcer Index"]:
            # Lower is better
            if value <= thresholds["10"]:
                return 10
            elif value <= thresholds["7"]:
                return 7
            elif value <= thresholds["4"]:
                return 4
            else:
                return 1
        else:
            # Higher is better
            if value >= thresholds["10"]:
                return 10
            elif value >= thresholds["7"]:
                return 7
            elif value >= thresholds["4"]:
                return 4
            else:
                return 1

    @staticmethod
    def calculate_downside_score(metrics: Dict[str, float]) -> float:
        total = 0.0
        for metric, val in metrics.items():
            weight = WEIGHTS.get(metric, 0)
            score = MetricsService.map_to_score(val, metric)
            total += score * weight
        return total

    @staticmethod
    def calculate_annual_return_and_volatility(df: pd.DataFrame) -> Dict[str, float]:
        df = df.copy()
        df["daily_return"] = df["close"].pct_change()
        daily_returns = df["daily_return"].dropna()
        mean_daily = daily_returns.mean()
        annual_return = (1 + mean_daily) ** TRADING_DAYS - 1 if not np.isnan(mean_daily) else 0.0
        annual_volatility = daily_returns.std() * np.sqrt(TRADING_DAYS)
        return {"annual_return": annual_return, "annual_volatility": annual_volatility}

    @staticmethod
    def calculate_risk_adjusted_metrics(df: pd.DataFrame, market_return: float = 0.08, beta: float = 1.0) -> Dict[str, float]:
        df = df.copy()
        df["daily_return"] = df["close"].pct_change()
        returns = df["daily_return"].dropna()

        risk_free_rate_daily = (1 + RISK_FREE_RATE_ANNUAL) ** (1 / TRADING_DAYS) - 1
        excess_returns = returns - risk_free_rate_daily

        # Sharpe Ratio
        sharpe = (
            excess_returns.mean() / returns.std() * np.sqrt(TRADING_DAYS) if returns.std() > 0 else 0.0
        )

        # Sortino Ratio (use downside deviation for volatility)
        downside_returns = returns[returns < 0]
        sortino_denom = downside_returns.std() * np.sqrt(TRADING_DAYS) if len(downside_returns) > 1 else 0
        sortino = excess_returns.mean() / sortino_denom if sortino_denom > 0 else 0.0

        # Information Ratio: Active return vs market tracking error (assumed market stdev)
        active_return = returns.mean() * TRADING_DAYS - market_return
        tracking_error = returns.std() * np.sqrt(TRADING_DAYS)
        info_ratio = active_return / tracking_error if tracking_error > 0 else 0.0

        # Treynor Ratio: (Return - Risk-free) / Beta
        annual_return = returns.mean() * TRADING_DAYS
        treynor = (annual_return - RISK_FREE_RATE_ANNUAL) / beta if beta > 0 else 0.0

        return {
            "Sharpe Ratio": sharpe,
            "Sortino Ratio": sortino,
            "Information Ratio": info_ratio,
            "Treynor Ratio": treynor,
        }

    @staticmethod
    def map_risk_adjusted_to_score(value: float, metric: str) -> int:
        thresholds = {
            "Sharpe Ratio": {"10": 1.0, "7": 0.61, "4": 0.31, "1": 0.0},
        }
        if metric not in thresholds:
            return 5
        t = thresholds[metric]
        if value >= t["10"]:
            return 10
        if value >= t["7"]:
            return 7
        if value >= t["4"]:
            return 4
        return 1

    @staticmethod
    def calculate_risk_adjusted_score(metrics: Dict[str, float]) -> float:
        sharpe = metrics.get("Sharpe Ratio", None)
        if sharpe is None:
            return 0.0
        return float(MetricsService.map_risk_adjusted_to_score(sharpe, "Sharpe Ratio"))

    async def compute_portfolio_metrics(
        self, symbols: List[str], weights: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        if not symbols:
            raise ValueError("At least one symbol is required")

        # Normalize weights
        if weights is None:
            weights = [1.0 / len(symbols)] * len(symbols)
        else:
            if len(weights) != len(symbols):
                raise ValueError("Weights length must match symbols")
            if abs(sum(weights) - 1.0) > 0.001:
                raise ValueError("Weights must sum to 1.0")

        # Fetch price data
        price_dfs = {sym: await self.get_price_df(sym) for sym in symbols}

        # Align dates for portfolio assembly
        common_dates = set.intersection(*[set(df["date"]) for df in price_dfs.values()])
        if not common_dates:
            raise ValueError("No overlapping trading dates found for symbols")
        common_dates = sorted(common_dates)

        # Compose portfolio daily close price series
        portfolio_values = []
        for date in common_dates:
            value = 0.0
            for sym, w in zip(symbols, weights):
                price = price_dfs[sym][price_dfs[sym]["date"] == date]["close"]
                if price.empty:
                    raise ValueError(f"Missing price for {sym} on {date}")
                value += w * price.iloc[0]
            portfolio_values.append(value)

        portfolio_df = pd.DataFrame({"date": common_dates, "close": portfolio_values})

        # Calculate metrics on portfolio
        basic_metrics = self.calculate_annual_return_and_volatility(portfolio_df)
        downside_metrics = self.calculate_downside_metrics(portfolio_df)
        downside_score = self.calculate_downside_score(downside_metrics)
        ra_metrics = self.calculate_risk_adjusted_metrics(portfolio_df)
        risk_adjusted_score = self.calculate_risk_adjusted_score(ra_metrics)

        metric_scores = {}

        # Score downside
        for metric, val in downside_metrics.items():
            metric_scores[f"{metric}_score"] = self.map_to_score(val, metric)

        # Score risk-adjusted
        for metric, val in ra_metrics.items():
            metric_scores[f"{metric}_score"] = self.map_risk_adjusted_to_score(val, metric)

        return {
            "portfolio_symbols": symbols,
            "weights_used": weights,
            **basic_metrics,
            **downside_metrics,
            "downside_score": downside_score,
            **ra_metrics,
            "risk_adjusted_score": risk_adjusted_score,
            "metric_scores": metric_scores,
        }

    async def compute_individual_metrics(self, symbols: List[str]) -> List[Dict[str, Any]]:
        results = []
        for sym in symbols:
            df = await self.get_price_df(sym)
            basic = self.calculate_annual_return_and_volatility(df)
            downside_metrics = self.calculate_downside_metrics(df)
            downside_score = self.calculate_downside_score(downside_metrics)
            downside_scores = {f"{m}_score": self.map_to_score(v, m) for m, v in downside_metrics.items()}
            ra_metrics = self.calculate_risk_adjusted_metrics(df)
            risk_adjusted_score = self.calculate_risk_adjusted_score(ra_metrics)
            ra_scores = {f"{k}_score": self.map_risk_adjusted_to_score(v, k) for k, v in ra_metrics.items()}

            results.append({
                "symbol": sym,
                **basic,
                **downside_metrics,
                "downside_score": downside_score,
                **downside_scores,
                **ra_metrics,
                "risk_adjusted_score": risk_adjusted_score,
                **ra_scores,
            })
        return results