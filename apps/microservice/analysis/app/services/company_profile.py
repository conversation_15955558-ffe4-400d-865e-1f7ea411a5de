from typing import List, Optional, Dict, Any, Tuple
from collections import defaultdict
from datetime import datetime
import pandas as pd
import numpy as np

from app.utils.fmp import FMPClient
from app.models.company_profile import CompanyProfileData, PortfolioCompanyProfile
from app.models.currency import (
    PortfolioValueConversion,
    HistoricalPortfolioValue,
    SupportedCurrency,
)


class CompanyProfileService:
    """
    Service layer handling company profile retrieval, portfolio aggregation,
    and currency conversion logic.
    """

    def __init__(self):
        self.fmp = FMPClient()
        self.common_currencies = {
            "USD": "US Dollar",
            "EUR": "Euro",
            "GBP": "British Pound",
            "JPY": "Japanese Yen",
            "CAD": "Canadian Dollar",
            "AUD": "Australian Dollar",
            "CHF": "Swiss Franc",
            "CNY": "Chinese Yuan",
            "HKD": "Hong Kong Dollar",
            "SGD": "Singapore Dollar",
            "INR": "Indian Rupee",
            "KRW": "South Korean Won",
            "BRL": "Brazilian Real",
            "MXN": "Mexican Peso",
            "RUB": "Russian Ruble",
            "ZAR": "South African Rand",
            "NOK": "Norwegian Krone",
            "SEK": "Swedish Krona",
            "DKK": "Danish Krone",
            "PLN": "Polish Zloty",
        }
        self.country_mappings = {
            "US": "United States",
            "CA": "Canada",
            "GB": "United Kingdom",
            "DE": "Germany",
            "FR": "France",
            "IT": "Italy",
            "ES": "Spain",
            "NL": "Netherlands",
            "CH": "Switzerland",
            "JP": "Japan",
            "CN": "China",
            "KR": "South Korea",
            "IN": "India",
            "AU": "Australia",
            "BR": "Brazil",
            "MX": "Mexico",
            "RU": "Russia",
            "ZA": "South Africa",
        }

    async def get_portfolio_company_profiles(
        self, symbols: List[str], weights: Optional[List[float]] = None
    ) -> PortfolioCompanyProfile:
        """
        Fetch company profiles and return portfolio allocation and summary.

        Args:
            symbols: List of stock ticker symbols
            weights: Optional normalized weights summing to 1.0

        Returns:
            Aggregated portfolio profile data.
        """

        if not symbols:
            raise ValueError("At least one symbol must be provided.")

        # Set equal allocation weights if none provided
        if weights is None:
            weights = [1.0 / len(symbols)] * len(symbols)
        else:
            if len(weights) != len(symbols):
                raise ValueError("Count of weights must match count of symbols.")
            if not np.isclose(sum(weights), 1.0, atol=1e-3):
                raise ValueError("Weights must sum to 1.0.")

        # Fetch profiles concurrently
        profiles_data = await self.fmp.get_batch_company_profiles(symbols)

        companies: List[CompanyProfileData] = []
        valid_symbols: List[str] = []
        valid_weights: List[float] = []

        for idx, symbol in enumerate(symbols):
            profile_data = profiles_data.get(symbol)
            if not profile_data or not isinstance(profile_data, dict):
                # Skip missing or invalid profiles
                continue
            
            country_code = profile_data.get("country", "US")
            country_name = self.country_mappings.get(country_code, country_code)
            try:
                profile = CompanyProfileData(
                    symbol=symbol,
                    companyName=profile_data.get("companyName", symbol),
                    currency=profile_data.get("currency", "USD"),
                    exchangeShortName=profile_data.get("exchangeShortName", "UNKNOWN"),
                    industry=profile_data.get("industry", "Unknown Industry"),
                    sector=profile_data.get("sector", "Unknown Sector"),
                    country=country_name,
                    isEtf=profile_data.get("isEtf", False),
                    isFund=profile_data.get("isFund", False),
                    isActivelyTrading=profile_data.get("isActivelyTrading", True),
                    mktCap=profile_data.get("mktCap"),
                    price=profile_data.get("price"),
                    beta=profile_data.get("beta"),
                    volAvg=profile_data.get("volAvg"),
                    website=profile_data.get("website"),
                    description=profile_data.get("description"),
                    ceo=profile_data.get("ceo"),
                    fullTimeEmployees=int(profile_data.get("fullTimeEmployees")) if profile_data.get("fullTimeEmployees") else None,
                    address=profile_data.get("address"),
                    city=profile_data.get("city"),
                    state=profile_data.get("state"),
                    zip=profile_data.get("zip"),
                    image=profile_data.get("image"),
                )
                companies.append(profile)
                valid_symbols.append(symbol)
                valid_weights.append(weights[idx])
            except Exception as ex:
                continue

        if not companies:
            raise ValueError("No valid company profiles found for given symbols.")

        # Normalize weights again (for valid subset)
        total_weight = sum(valid_weights)
        valid_weights = [w / total_weight for w in valid_weights]

        # Compute allocations
        sector_allocation = self._calculate_allocation(companies, valid_weights, "sector")
        industry_allocation = self._calculate_allocation(companies, valid_weights, "industry")
        country_allocation = self._calculate_allocation(companies, valid_weights, "country")
        currency_allocation = self._calculate_allocation(companies, valid_weights, "currency")

        # Portfolio summaries
        total_market_cap = sum(
            (c.mktCap or 0) * w for c, w in zip(companies, valid_weights)
        )
        weighted_beta = None
        beta_values = [(c.beta, w) for c, w in zip(companies, valid_weights) if c.beta is not None]
        if beta_values:
            weighted_beta = sum(beta * weight for beta, weight in beta_values)

        return PortfolioCompanyProfile(
            symbols=valid_symbols,
            weights=valid_weights,
            companies=companies,
            sector_allocation=sector_allocation,
            industry_allocation=industry_allocation,
            country_allocation=country_allocation,
            currency_allocation=currency_allocation,
            total_market_cap=total_market_cap,
            weighted_beta=weighted_beta,
            portfolio_currency="USD",
        )

    def _calculate_allocation(
        self, companies: List[CompanyProfileData], weights: List[float], attribute: str
    ) -> Dict[str, float]:
        """
        Calculate weighted allocation percentage for specified attribute.
        """
        allocation: Dict[str, float] = defaultdict(float)
        for company, weight in zip(companies, weights):
            key = getattr(company, attribute, "Unknown")
            if key:
                allocation[key] += weight * 100  # Percent
        return dict(allocation)

    async def convert_portfolio_value(
        self,
        symbol_units: Dict[str, float],
        target_currency: str = "USD",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> Tuple[PortfolioValueConversion, Optional[List[HistoricalPortfolioValue]], List[float], Dict[str, Any]]:
        
        if not symbol_units:
            raise ValueError("No symbols/units provided.")

        symbols = list(symbol_units.keys())
        units = list(symbol_units.values())

        profiles = await self.get_batch_company_profiles(symbols)

        converted_values = []
        total_value = 0.0

        for symbol, unit in symbol_units.items():
            profile = profiles.get(symbol)
            if not profile or not isinstance(profile, dict):
                raise ValueError(f"Profile missing for symbol '{symbol}'")

            price = profile.get("price")
            currency = profile.get("currency", "USD")
            if price is None:
                raise ValueError(f"Price missing for symbol '{symbol}'")

            value_in_base = price * unit

            if currency != target_currency:
                fx_data = await self.get_exchange_rate(currency, target_currency)
                rate = fx_data.get("rate", 1.0)
            else:
                rate = 1.0

            value_in_target = value_in_base * rate
            converted_values.append(value_in_target)
            total_value += value_in_target

        if total_value == 0:
            raise ValueError("Total portfolio value is zero after conversion.")

        weights_used = [v / total_value for v in converted_values]

        current_conversion = PortfolioValueConversion(
            base_currency=target_currency,
            target_currency=target_currency,
            conversion_rate=1.0,
            original_value=total_value,
            converted_value=total_value,
            conversion_date=datetime.utcnow().strftime("%Y-%m-%d"),
        )

        historical_values = None
        data_quality_info = {"message": "Historical conversion not implemented for units-only portfolio."}

        return current_conversion, historical_values, weights_used, data_quality_info
    
    async def get_batch_company_profiles(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Fetch company profiles for multiple symbols concurrently.
        Returns dict: symbol -> profile dict.
        """
        profiles = {}
        for symbol in symbols:
            try:
                profile = await self.get_company_profile(symbol)
                profiles[symbol] = profile
            except Exception as e:
                profiles[symbol] = None
        return profiles

    async def get_company_profile(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch single company profile for symbol from FMP.
        Adjust if you already have this method or your FMPClient equivalent.
        """
        endpoint = f"/profile/{symbol}?apikey={self.fmp.api_key}"
        resp = await self.fmp.client.get(endpoint)
        resp.raise_for_status()
        data = resp.json()
        if isinstance(data, list) and data:
            return data[0]
        return {}

    async def _get_historical_portfolio_values(
        self,
        symbols: List[str],
        weights: List[float],
        base_currency: str,
        target_currency: str,
        start_date: str,
        end_date: str,
    ) -> Tuple[List[HistoricalPortfolioValue], List[str], Dict[str, Any]]:
        """
        Fetch, preprocess, and calculate historical portfolio currency conversion values.

        Returns:
            List of historical data points,
            List of missing dates,
            Data quality / preprocessing info.
        """
        if base_currency == target_currency:
            return [], [], {"message": "No conversion necessary - base and target currency are the same."}

        # Try to get forex rates for currency pair or reverse pair with inversion
        forex_data = None
        try:
            forex_data = await self.fmp.get_historical_forex_range(f"{base_currency}{target_currency}", start_date, end_date)
        except Exception:
            try:
                reverse_data = await self.fmp.get_historical_forex_range(f"{target_currency}{base_currency}", start_date, end_date)
                # Invert rates
                for entry in reverse_data:
                    rate = entry.get("close", 0)
                    entry["close"] = 1.0 / rate if rate else 0
                forex_data = reverse_data
            except Exception as e:
                return [], [], {"error": f"Failed to fetch forex data: {e}"}

        if not forex_data:
            return [], [], {"error": "No historical exchange rate data available for given period."}

        # Prepare dataframe for easier manipulation
        df = pd.DataFrame(forex_data)
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        full_index = pd.date_range(start=start_date, end=end_date, freq='D')
        df = df.reindex(full_index)

        missing_before = df['close'].isna().sum()
        total_days = len(full_index)

        # Fill missing data: forward fill, backward fill, interpolate
        df['close'].fillna(method='ffill', inplace=True)
        df['close'].fillna(method='bfill', inplace=True)
        df['close'].interpolate(method='linear', inplace=True)
        missing_after = df['close'].isna().sum()

        # Compose historical portfolio values
        # NOTE: For simplicity, we assume fixed base portfolio value (e.g., market cap)
        # This can be extended to use actual historical prices.
        base_portfolio_value = 100000.0  
        historical_values: List[HistoricalPortfolioValue] = []
        missing_dates: List[str] = []

        for date in full_index:
            rate = df.loc[date, 'close']
            if np.isnan(rate) or rate == 0:
                missing_dates.append(date.strftime("%Y-%m-%d"))
                continue
            historical_values.append(
                HistoricalPortfolioValue(
                    date=date.strftime("%Y-%m-%d"),
                    portfolio_value_base=base_portfolio_value,
                    portfolio_value_target=base_portfolio_value * rate,
                    exchange_rate=rate,
                    base_currency=base_currency,
                    target_currency=target_currency,
                )
            )

        completeness_pct = ((total_days - len(missing_dates)) / total_days) * 100 if total_days else 0
        data_quality = {
            "total_requested_days": total_days,
            "successful_conversions": len(historical_values),
            "missing_data_count": len(missing_dates),
            "data_completeness_percentage": completeness_pct,
            "preprocessing_notes": [
                f"Filled {missing_before} missing values initially.",
                f"Interpolated and filled missing dates, remaining missing after fill: {missing_after}."
            ],
        }

        return historical_values, missing_dates, data_quality

    async def get_exchange_rate(self, from_currency: str, to_currency: str) -> Dict[str, Any]:
        """
        Retrieve current exchange rate for specified currency pair.
        Falls back to reversed pair with inversion if needed.
        """
        if from_currency == to_currency:
            return {"rate": 1.0, "pair": f"{from_currency}{to_currency}"}

        primary_pair = f"{from_currency}{to_currency}"
        try:
            quote = await self.fmp.get_forex_quote(primary_pair)
            rate = float(quote.get("price", 1.0))
            return {"rate": rate, "pair": primary_pair, "data": quote}
        except Exception:
            reversed_pair = f"{to_currency}{from_currency}"
            try:
                quote = await self.fmp.get_forex_quote(reversed_pair)
                price = float(quote.get("price", 0))
                rate = 1.0 / price if price else 1.0
                return {"rate": rate, "pair": reversed_pair, "inverted": True, "data": quote}
            except Exception as e:
                raise ValueError(f"Could not fetch exchange rate for {from_currency} to {to_currency}: {str(e)}")

    async def get_supported_currencies(self) -> List[SupportedCurrency]:
        """
        Return supported currencies inferred from available forex pairs.
        Falls back to common currency list on failure.
        """
        try:
            forex_pairs = await self.fmp.get_available_forex_pairs()

            currencies_set = set()
            for pair in forex_pairs:
                symbol = pair.get("symbol", "")
                if len(symbol) == 6:
                    currencies_set.add(symbol[:3])  # Base currency
                    currencies_set.add(symbol[3:])  # Quote currency

            result = []
            for code in sorted(currencies_set):
                name = self.common_currencies.get(code, f"{code} Currency")
                result.append(SupportedCurrency(code=code, name=name, symbol=code))

            return result
        except Exception:
            return [
                SupportedCurrency(code=code, name=name, symbol=code)
                for code, name in self.common_currencies.items()
            ]

    async def close(self) -> None:
        """
        Clean up / close any connections held by the FMP client.
        """
        await self.fmp.close()