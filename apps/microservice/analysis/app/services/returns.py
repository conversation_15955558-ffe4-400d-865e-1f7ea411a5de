import asyncio
import logging
from datetime import datetime, timedelta
import math
from typing import List, Dict, Tuple
from collections import defaultdict, deque

import numpy as np
import pandas as pd
from fastapi import HTTPException
from sklearn.covariance import LedoitWolf

from app.utils.fmp import FMPClient
from app.models.returns import (
    CorrelationResponse,
    Transaction,
    InstrumentReturn,
    DailyPortfolioReturn,
    PortfolioReturnResponse,
    TickerPerformance,
    TopMoversResponse,
)

logger = logging.getLogger(__name__)

class PortfolioAnalysisService:
    def __init__(self):
        self.fmp = FMPClient()

    async def calculate_portfolio_returns(
        self,
        transactions: List[Transaction],
        target_currency: str,
        allow_short: bool = True
    ) -> PortfolioReturnResponse:
        tx_rows = []
        for i, tx in enumerate(transactions):
            try:
                dt_raw = pd.to_datetime(tx.tradeDate)
                if dt_raw.tzinfo is not None:
                    dt_raw = dt_raw.tz_convert(None)
                dt = dt_raw 
                qty = float(tx.quantity)
                qty = qty if tx.type.upper() == "BUY" else -abs(qty)
                tx_rows.append({
                    "date": dt,
                    "symbol": tx.symbol,
                    "qty": qty,
                    "price_local": float(tx.price),
                    "orig_currency": (tx.currency or target_currency).upper()
                })
            except Exception as e:
                raise HTTPException(400, f"Invalid transaction at index {i}: {e}")

        df_tx = pd.DataFrame(tx_rows)
        if df_tx.empty:
            raise HTTPException(400, "No valid transactions provided")

        df_tx = df_tx.sort_values("date").reset_index(drop=True)

        start_date = df_tx["date"].dt.normalize().min()
        now_raw = pd.to_datetime(datetime.utcnow())
        if now_raw.tzinfo is not None:
            now_raw = now_raw.tz_convert(None)
        end_date = now_raw.normalize() - timedelta(days=1)

        dates = pd.date_range(start_date, end_date, freq="D")

        # OPTIMIZED: Fetch forex rates concurrently
        unique_currencies = df_tx["orig_currency"].unique()
        forex_tasks = []
        forex_currencies = []
        
        for cur in unique_currencies:
            if cur != target_currency:
                forex_currencies.append(cur)
                pair = f"{cur}{target_currency}"
                forex_tasks.append(self.fmp.get_historical_forex_range(
                    pair,
                    start_date.strftime("%Y-%m-%d"),
                    end_date.strftime("%Y-%m-%d")
                ))

        # Execute all forex API calls concurrently
        forex_results = await asyncio.gather(*forex_tasks) if forex_tasks else []

        # Process forex results
        fx_data: dict = {}
        fx_data[target_currency] = pd.Series(1.0, index=dates)
        
        for cur, hist in zip(forex_currencies, forex_results):
            ser = (
                pd.DataFrame(hist or [])
                .assign(
                    date=lambda d: pd.to_datetime(d["date"], utc=True)
                    .dt.tz_localize(None)
                    .dt.normalize()
                )
                .set_index("date")["close"]
                .reindex(dates)
                .ffill()
                .bfill()
                .fillna(1.0)
            )
            fx_data[cur] = ser

        # OPTIMIZED: Fetch stock prices concurrently
        symbols = df_tx["symbol"].unique()
        price_tasks = [
            self.fmp.get_historical_daily(sym, lookback_days=len(dates) + 10) 
            for sym in symbols
        ]
        price_results = await asyncio.gather(*price_tasks)

        # Process price results
        price_df = pd.DataFrame(index=dates)
        for sym, hist in zip(symbols, price_results):
            if not hist:
                raise HTTPException(500, f"Failed to fetch price data for {sym}")
            ser = (
                pd.DataFrame(hist)
                .assign(
                    date=lambda d: pd.to_datetime(d["date"], utc=True)
                    .dt.tz_localize(None)
                    .dt.normalize()
                )
                .set_index("date")["close"]
                .reindex(dates)
                .ffill()
                .bfill()
            )
            orig = df_tx.loc[df_tx["symbol"] == sym, "orig_currency"].iloc[0]
            price_df[sym] = ser * fx_data[orig]

        previous_day_buy_lots = defaultdict(deque)
        previous_day_short_lots = defaultdict(deque)

        instrument_rows = []
        portfolio_rows = []
        cumulative_realized = 0.0
        prev_unreal = defaultdict(float)

        for today in dates:
            day_start = pd.Timestamp(today)
            day_end = day_start + pd.Timedelta(days=1) - pd.Timedelta(microseconds=1)

            day_tx = df_tx[(df_tx['date'] >= day_start) & (df_tx['date'] <= day_end)]
            day_tx = day_tx.sort_values('date')

            intraday_buy_lots = defaultdict(deque)
            intraday_short_lots = defaultdict(deque)
            intraday_realized_today = defaultdict(float)

            for sym in symbols:
                sym_day_tx = day_tx[day_tx['symbol'] == sym]

                # Process intraday trades FIFO per symbol
                for _, tx in sym_day_tx.iterrows():
                    qty = tx['qty']
                    price_local = tx['price_local']
                    orig_cur = tx['orig_currency']
                    trade_day = pd.Timestamp(tx['date']).normalize()
                    rate = fx_data[orig_cur].get(trade_day, 1.0)
                    price = price_local * rate

                    if qty > 0:
                        qty_to_buy = qty
                        while intraday_short_lots[sym] and qty_to_buy > 0:
                            lot = intraday_short_lots[sym][0]
                            lot_qty = abs(lot["qty"])
                            matched_qty = min(qty_to_buy, lot_qty)
                            realized_pl = (lot["price_per_share"] - price) * matched_qty
                            intraday_realized_today[sym] += realized_pl
                            qty_to_buy -= matched_qty
                            if matched_qty == lot_qty:
                                intraday_short_lots[sym].popleft()
                            else:
                                lot["qty"] += matched_qty
                        if qty_to_buy > 0:
                            intraday_buy_lots[sym].append({"qty": qty_to_buy, "price_per_share": price})

                    else:
                        qty_to_sell = -qty
                        while intraday_buy_lots[sym] and qty_to_sell > 0:
                            lot = intraday_buy_lots[sym][0]
                            lot_qty = lot["qty"]
                            matched_qty = min(qty_to_sell, lot_qty)
                            realized_pl = (price - lot["price_per_share"]) * matched_qty
                            intraday_realized_today[sym] += realized_pl
                            qty_to_sell -= matched_qty
                            if matched_qty == lot_qty:
                                intraday_buy_lots[sym].popleft()
                            else:
                                lot["qty"] -= matched_qty
                        if qty_to_sell > 0:
                            while previous_day_buy_lots[sym] and qty_to_sell > 0:
                                lot = previous_day_buy_lots[sym][0]
                                lot_qty = lot["qty"]
                                matched_qty = min(qty_to_sell, lot_qty)
                                realized_pl = (price - lot["price_per_share"]) * matched_qty
                                intraday_realized_today[sym] += realized_pl
                                qty_to_sell -= matched_qty
                                if matched_qty == lot_qty:
                                    previous_day_buy_lots[sym].popleft()
                                else:
                                    lot["qty"] -= matched_qty
                            if qty_to_sell > 0:
                                if not allow_short:
                                    raise HTTPException(
                                        400,
                                        f"Short selling not allowed for {sym} on {today.strftime('%Y-%m-%d')}"
                                    )
                                intraday_short_lots[sym].append({"qty": -qty_to_sell, "price_per_share": price})

                combined_buy_lots = deque(previous_day_buy_lots[sym])
                combined_short_lots = deque(previous_day_short_lots[sym])
                combined_buy_lots.extend(intraday_buy_lots[sym])
                combined_short_lots.extend(intraday_short_lots[sym])

                net_qty = sum(lot['qty'] for lot in combined_buy_lots) + sum(lot['qty'] for lot in combined_short_lots)
                cost_long = sum(lot['qty'] * lot['price_per_share'] for lot in combined_buy_lots)
                cost_short = sum(lot['qty'] * lot['price_per_share'] for lot in combined_short_lots)
                cost_basis = cost_long + cost_short

                if abs(net_qty) < 1e-10 and abs(cost_basis) > 1e-10:
                    intraday_realized_today[sym] += (- cost_basis) 
                    combined_buy_lots.clear()
                    combined_short_lots.clear()
                    cost_basis = 0.0
                    net_qty = 0.0

                previous_day_buy_lots[sym] = combined_buy_lots
                previous_day_short_lots[sym] = combined_short_lots

            total_mv = 0.0
            sym_returns = {}
            buy_qty_map = defaultdict(float)
            start_holdings = defaultdict(float)
            cost_basis_map = defaultdict(float)

            for sym in symbols:
                total_qty_long = sum(lot["qty"] for lot in previous_day_buy_lots[sym])
                total_qty_short = sum(lot["qty"] for lot in previous_day_short_lots[sym])
                start_holdings[sym] = total_qty_long + total_qty_short
                cost_long = sum(lot["qty"] * lot["price_per_share"] for lot in previous_day_buy_lots[sym])
                cost_short = sum(lot["qty"] * lot["price_per_share"] for lot in previous_day_short_lots[sym])
                cost_basis_map[sym] = cost_long + cost_short
                buy_qty_map[sym] = sum(lot["qty"] for lot in intraday_buy_lots[sym])
            
            total_net_pl = 0.0

            for sym in symbols:
                net_qty = start_holdings[sym]
                cost = cost_basis_map[sym]

                if abs(net_qty) < 1e-10:
                    net_qty = 0.0
                    cost = 0.0
                    avg_cost = 0.0
                else:
                    avg_cost = cost / net_qty

                ltp = price_df.at[today, sym]
                unrealized_pl = (ltp - avg_cost) * net_qty if net_qty != 0 else 0.0
                change_unrealized_pl = unrealized_pl - prev_unreal[sym]
                realized_pl = intraday_realized_today[sym]
                net_pl = realized_pl + change_unrealized_pl

                max_qty = max(
                    abs(start_holdings[sym]) + buy_qty_map.get(sym, 0.0),
                    abs(net_qty),
                    abs(start_holdings[sym])
                )
                denom = abs(avg_cost * max_qty) if max_qty > 0 else 0.0
                net_pl_pct = (net_pl / denom * 100) if denom else 0.0
                stock_return = (net_pl / denom) if denom else 0.0

                inst_row = {
                    "date": today.strftime("%Y-%m-%d"),
                    "symbol": sym,
                    "quantity": net_qty,
                    "avg_cost": avg_cost,
                    "cost_of_holdings": cost,
                    "realized_pl": realized_pl,
                    "unrealized_pl": unrealized_pl,
                    "change_unrealized_pl": change_unrealized_pl,
                    "net_pl": net_pl,
                    "net_pl_pct": net_pl_pct,
                    "stock_return": stock_return,
                    "ltp": ltp
                }

                zero_fields = all(
                    (inst_row[f] == 0 or inst_row[f] == 0.0)
                    for f in [
                        "quantity",
                        "avg_cost",
                        "cost_of_holdings",
                        "realized_pl",
                        "unrealized_pl",
                        "change_unrealized_pl",
                        "net_pl"
                    ]
                )

                if not zero_fields:
                    instrument_rows.append(inst_row)
                    total_mv += ltp * net_qty
                    sym_returns[sym] = stock_return

                prev_unreal[sym] = unrealized_pl
                total_net_pl += net_pl 

            cumulative_realized += sum(intraday_realized_today.values())

            port_ret = 0.0
            if total_mv != 0:
                port_ret = sum(
                    ((price_df.at[today, sym] * start_holdings[sym]) / total_mv) * sym_returns.get(sym, 0)
                    for sym in sym_returns
                )

            portfolio_rows.append({
                "date": today.strftime("%Y-%m-%d"),
                "total_value": round(total_mv + cumulative_realized, 6),
                "portfolio_return": round(port_ret, 6),
                "change_pnl": round(total_net_pl, 6) 
            })

        instrument_returns = [InstrumentReturn(**r) for r in instrument_rows]
        portfolio_returns = [DailyPortfolioReturn(**r) for r in portfolio_rows]

        instrument_df = pd.DataFrame([r.dict() for r in instrument_returns])
        portfolio_df = pd.DataFrame([r.dict() for r in portfolio_returns])

        last_date = portfolio_df['date'].max()
        holdings_today = instrument_df[instrument_df['date'] == last_date].copy()
        holdings_today['symbol'] = holdings_today['symbol'].astype(str)
        holdings_today = holdings_today[holdings_today['quantity'].abs() > 1e-6]

        holdings_df = holdings_today[['symbol', 'quantity', 'ltp']]

        var_95, cvar_95 = await calculate_portfolio_var_cvar(holdings_df, lookback_days=252*3, fmp_client=self.fmp)

        metrics, time_series = calculate_advanced_metrics(portfolio_df, instrument_df)

        metrics['var_95'] = round(var_95, 6)
        metrics['cvar_95'] = round(cvar_95, 6)

        return PortfolioReturnResponse(
            target_currency=target_currency,
            instrument_returns=instrument_returns,
            portfolio_returns=portfolio_returns,
            realized_return=round(cumulative_realized, 6),
            **metrics,
            drawdown_series=time_series['drawdown'],
            rolling_vol_series=time_series['rolling_volatility'],
            annual_returns=time_series['annual_returns'],
            monthly_returns=time_series['monthly_returns'],
        )

    async def get_top_movers(self, symbols: List[str]) -> TopMoversResponse:
        lookback_days = 365
        
        # OPTIMIZED: Fetch all stock prices concurrently
        price_tasks = [self.fmp.get_historical_daily(symbol, lookback_days) for symbol in symbols]
        all_hist = await asyncio.gather(*price_tasks)
        
        price_map = {}
        for symbol, hist in zip(symbols, all_hist):
            if hist:
                df = pd.DataFrame(hist)
                df["date"] = pd.to_datetime(df["date"])
                df.set_index("date", inplace=True)
                if df.index.duplicated().any():
                    df = df[~df.index.duplicated(keep='last')]
                price_map[symbol] = df.sort_index()

        today = datetime.utcnow().date() - timedelta(days=1)
        periods = {
            "daily": today - timedelta(days=1),
            "weekly": today - timedelta(weeks=1),
            "monthly": today - timedelta(days=30),
            "yearly": today - timedelta(days=365)
        }

        results = {key: [] for key in periods}
        for symbol, df in price_map.items():
            if not df.empty:
                latest_price = df["close"].iloc[-1]
                for period_name, past_date in periods.items():
                    past_df = df[df.index.date <= past_date]
                    if not past_df.empty:
                        past_price = past_df["close"].iloc[-1]
                        if past_price > 0:
                            change_pct = round((latest_price - past_price) / past_price * 100, 2)
                            results[period_name].append(
                                TickerPerformance(
                                    symbol=symbol,
                                    change_pct=change_pct,
                                    timeframe=period_name
                                )
                            )

        def top_gainers(data):
            return sorted([x for x in data if x.change_pct > 0], key=lambda x: x.change_pct, reverse=True)[:4]

        def top_losers(data):
            return sorted([x for x in data if x.change_pct < 0], key=lambda x: x.change_pct)[:4]

        return TopMoversResponse(
            daily_gainers=top_gainers(results["daily"]),
            daily_losers=top_losers(results["daily"]),
            weekly_gainers=top_gainers(results["weekly"]),
            weekly_losers=top_losers(results["weekly"]),
            monthly_gainers=top_gainers(results["monthly"]),
            monthly_losers=top_losers(results["monthly"]),
            yearly_gainers=top_gainers(results["yearly"]),
            yearly_losers=top_losers(results["yearly"]),
        )

    async def get_correlation_matrix(
        self,
        symbols: List[str],
        lookback: int = 252 * 3,
    ) -> CorrelationResponse:
        # ALREADY OPTIMIZED: This was already using asyncio.gather
        tasks = [self.fmp.get_historical_daily(sym, lookback) for sym in symbols]
        all_hist = await asyncio.gather(*tasks)

        price_frames = []
        for sym, hist in zip(symbols, all_hist):
            if not hist:
                continue
            df = (
                pd.DataFrame(hist)
                  .assign(
                      date=lambda d: pd.to_datetime(d["date"], utc=True)
                                      .dt.tz_localize(None)
                                      .dt.normalize()
                  )
                  .set_index("date")["close"]
                  .rename(sym)
            )
            price_frames.append(df)

        if not price_frames:
            raise ValueError("No price data for any symbol")

        prices = pd.concat(price_frames, axis=1).sort_index()

        bdays = pd.date_range(prices.index.min(), prices.index.max(), freq="B")
        prices = prices.reindex(bdays).ffill()

        logrets = np.log(prices / prices.shift(1)).dropna(how="any")

        lw = LedoitWolf().fit(logrets.values)
        cov = lw.covariance_

        std = np.sqrt(np.diag(cov))
        corr = cov / np.outer(std, std)
        np.fill_diagonal(corr, 1.0)
        corr_list = corr.tolist()

        return CorrelationResponse(
            symbols=symbols,
            correlation_matrix=corr_list
        )

async def calculate_portfolio_var_cvar(
    holdings: pd.DataFrame,
    lookback_days: int,
    fmp_client
) -> tuple[float, float]:
    """
    Calculate 5% VaR and CVaR for the portfolio based on holdings and historical returns.
    
    Args:
        holdings: pd.DataFrame with columns ['symbol', 'quantity', 'ltp'] representing current holdings.
        lookback_days: Number of days of historical data to fetch.
        fmp_client: Instance of your FMPClient for data fetching.

    Returns:
        var_95: Value at Risk at 95% confidence level (float).
        cvar_95: Conditional VaR at 95% confidence level (float).
    """
    symbols = holdings['symbol'].tolist()
    logger.info(f"Calculating VaR/CVaR for symbols: {symbols}")
    logger.info(f"Holdings:\n{holdings}")

    # OPTIMIZED: Fetch all historical data concurrently
    tasks = [fmp_client.get_historical_daily(sym, lookback_days) for sym in symbols]
    all_hist = await asyncio.gather(*tasks)
    
    price_frames = []
    for sym, hist in zip(symbols, all_hist):
        if not hist:
            logger.warning(f"No historical data fetched for {sym}")
            continue
        df = (
            pd.DataFrame(hist)
              .assign(
                  date=lambda d: pd.to_datetime(d["date"], utc=True)
                                  .dt.tz_localize(None)
                                  .dt.normalize()
              )
              .set_index("date")["close"]
              .rename(sym)
        )
        logger.info(f"Fetched {len(df)} price points for {sym}")
        price_frames.append(df)

    if not price_frames:
        logger.error("No price data fetched for any symbols; returning zeros for VaR and CVaR")
        return 0.0, 0.0
      
    prices = pd.concat(price_frames, axis=1).sort_index()
    
    logger.info(f"Combined prices dataframe shape: {prices.shape}")
    prices = prices.ffill().dropna(how='any')
    logger.info(f"Prices after forward-fill and dropna shape: {prices.shape}")

    returns = prices.pct_change().dropna()
    logger.info(f"Returns dataframe shape: {returns.shape}")
    logger.debug(f"Returns dataframe head:\n{returns.head()}")

    values = holdings.set_index('symbol').eval('quantity * ltp')
    total_value = values.sum()
    logger.info(f"Total portfolio market value: {total_value}")
    if total_value == 0:
        logger.error("Total portfolio value is zero, cannot compute weights.")
        return 0.0, 0.0
    weights = values / total_value
    logger.info(f"Weights before alignment:\n{weights}")

    weights = weights.reindex(returns.columns).fillna(0)
    logger.info(f"Weights after reindex alignment to returns columns:\n{weights}")
    logger.info(f"Sum of weights: {weights.sum()}")

    if len(returns) == 0 or len(weights) == 0:
        logger.error("Empty returns or weights after alignment; returning zeros for VaR and CVaR.")
        return 0.0, 0.0

    portfolio_returns = returns.dot(weights)
    logger.info(f"Portfolio returns stats: count={portfolio_returns.count()}, mean={portfolio_returns.mean()}, std={portfolio_returns.std()}")

    if portfolio_returns.empty or portfolio_returns.std() == 0:
        logger.warning("Portfolio returns empty or zero variance; VaR and CVaR set to 0.0")
        return 0.0, 0.0

    var_95 = np.percentile(portfolio_returns, 5)
    cvar_95 = portfolio_returns[portfolio_returns <= var_95].mean()
    logger.info(f"Calculated VaR(95%): {var_95}, CVaR(95%): {cvar_95}")

    return var_95, cvar_95

def calculate_advanced_metrics(
    portfolio_returns_df: pd.DataFrame,
    instrument_returns_df: pd.DataFrame
) -> Tuple[Dict[str, float], Dict[str, any]]:
    df = portfolio_returns_df.copy()
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    daily_r = df['portfolio_return'].fillna(0)

    cum_returns = (1 + daily_r).cumprod()
    peak = cum_returns.cummax()
    drawdown = cum_returns / peak - 1

    def safe_round(val):
        return round(val, 6) if (pd.notna(val) and np.isfinite(val)) else 0.0

    drawdown_series = {
        dt.strftime('%Y-%m-%d'): safe_round(val)
        for dt, val in drawdown.items()
    }

    annual_series = daily_r.resample('Y').apply(lambda x: (1 + x).prod() - 1)
    annual_returns = {
        idx.strftime('%Y'): safe_round(val)
        for idx, val in annual_series.items()
        if pd.notna(val)
    }

    monthly_series = daily_r.resample('M').apply(lambda x: (1 + x).prod() - 1)
    monthly_returns = {
        idx.strftime('%Y-%m'): safe_round(val)
        for idx, val in monthly_series.items()
        if pd.notna(val)
    }

    rolling_std = daily_r.rolling(window=21).std()
    rolling_volatility = rolling_std * math.sqrt(252)
    rolling_vol_series = {
        dt.strftime('%Y-%m-%d'): safe_round(val)
        for dt, val in rolling_volatility.items()
    }

    years = ((df.index[-1] - df.index[0]).days + 1)/ 365.25 if len(df) > 1 else 0
    logger.info(f"start date: {df.index[-1]} end date: {df.index[0]}  ki:{(df.index[-1] - df.index[0]).days / 365.25}")
    cumulative_return_product = (1 + daily_r).prod()
    cagr = ((cumulative_return_product ** (1 / years) - 1)) if years > 0 else 0.0

    monthly_pct = (1 + daily_r).resample('M').prod() - 1
    best_month = safe_round(monthly_pct.max()) if not monthly_pct.empty else 0.0
    worst_month = safe_round(monthly_pct.min()) if not monthly_pct.empty else 0.0

    mean_r = daily_r.mean()
    std_r = daily_r.std(ddof=1)
    down_std = daily_r[daily_r < 0].std(ddof=1) or 0.0
    sharpe_ratio = (mean_r / std_r) * math.sqrt(252) if std_r > 0 else 0.0
    sortino_ratio = (mean_r / down_std) * math.sqrt(252) if down_std > 0 else 0.0

    volatility = std_r * math.sqrt(252)
    max_drawdown = (cum_returns - peak).min() / peak.max() if peak.max() > 0 else 0.0

    var_95 = float(np.percentile(daily_r, 5))
    cvar_95 = float(daily_r[daily_r <= var_95].mean()) if not daily_r[daily_r <= var_95].empty else 0.0

    pos = daily_r[daily_r > 0].sum()
    neg = abs(daily_r[daily_r < 0].sum())
    omega_ratio = (pos / neg) if neg > 0 else 0.0

    calmar_ratio = (cagr / abs(max_drawdown)) if max_drawdown < 0 else 0.0

    inst = instrument_returns_df.copy()
    inst['date'] = pd.to_datetime(inst['date'])
    last = inst[inst['date'] == inst['date'].max()]

    if not last.empty:
        w = last['ltp'] * last['quantity']
        total_w = w.sum()
        weights = (w / total_w) if total_w > 0 else pd.Series(dtype=float)
        num_assets = int(weights.size)
        max_weight = safe_round(weights.max()) if num_assets else 0.0
        non_zero = weights[weights > 0]
        min_weight = safe_round(non_zero.min()) if not non_zero.empty else 0.0
        herfindahl_index = safe_round((weights**2).sum()) if num_assets else 0.0
    else:
        num_assets = 0
        max_weight = min_weight = herfindahl_index = 0.0

    metrics = {
        'cagr': safe_round(cagr),
        'best_month': safe_round(best_month),
        'worst_month': safe_round(worst_month),
        'sharpe_ratio': safe_round(sharpe_ratio),
        'sortino_ratio': safe_round(sortino_ratio),
        'volatility': safe_round(volatility),
        'max_drawdown': safe_round(max_drawdown),
        'var_95': safe_round(var_95),
        'cvar_95': safe_round(cvar_95),
        'omega_ratio': safe_round(omega_ratio),
        'calmar_ratio': safe_round(calmar_ratio),
        'num_assets': num_assets,
        'herfindahl_index': safe_round(herfindahl_index),
        'max_weight': safe_round(max_weight),
        'min_weight': safe_round(min_weight),
    }

    time_series = {
        'drawdown': drawdown_series,
        'rolling_volatility': rolling_vol_series,
        'annual_returns': annual_returns,
        'monthly_returns': monthly_returns,
    }
    return metrics, time_series