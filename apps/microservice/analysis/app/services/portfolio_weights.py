import logging
from typing import List
from app.utils.fmp import FMPClient
from app.models.portfolio_weights import PortfolioWeightsResponse, StockValueInTargetCurrency

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class PortfolioWeightsService:
    def __init__(self):
        self.fmp = FMPClient()

    async def calculate_weights(self, symbols: List[str], units: List[float], target_currency: str) -> PortfolioWeightsResponse:
        logger.info(f"Calculating weights for symbols={symbols}, units={units}, target_currency={target_currency}")

        if not symbols or not units:
            raise ValueError("Symbols and units must be provided")
        if len(symbols) != len(units):
            raise ValueError("Length of symbols and units must be equal")

        profiles = await self.fmp.get_batch_company_profiles(symbols)
        logger.debug(f"Fetched profiles: {profiles}")

        stock_data = []
        for sym, unit in zip(symbols, units):
            profile = profiles.get(sym)
            if not profile or not isinstance(profile, dict):
                raise ValueError(f"Failed to get profile/price for symbol {sym}")

            price = profile.get("price")
            currency = profile.get("currency", "USD")

            if price is None:
                raise ValueError(f"Price data missing for {sym}")

            stock_data.append({
                "symbol": sym,
                "units": unit,
                "price": price,
                "currency": currency,
                "value_in_original": unit * price
            })

        logger.info(f"Intermediate stock data: {stock_data}")

        converted_stocks = []
        total_value = 0.0

        for stock in stock_data:
            try:
                if stock["currency"] == target_currency:
                    converted_value = stock["value_in_original"]
                else:
                    rate_data = await self.get_exchange_rate(stock["currency"], target_currency)
                    rate = rate_data.get("rate", 1.0)
                    converted_value = stock["value_in_original"] * rate

                total_value += converted_value
                converted_stocks.append({
                    "symbol": stock["symbol"],
                    "units": stock["units"],
                    "price": stock["price"],
                    "currency": stock["currency"],
                    "value_in_target_currency": converted_value
                })
            except Exception as e:
                logger.exception(f"Currency conversion failed for {stock['symbol']}: {e}")
                raise

        logger.info(f"Total portfolio value in {target_currency}: {total_value}")
        logger.debug(f"Converted stock values: {converted_stocks}")

        if total_value == 0:
            raise ValueError("Total portfolio value is zero after conversion. Check input data.")

        weights = [stock["value_in_target_currency"] / total_value for stock in converted_stocks]

        response = PortfolioWeightsResponse(
            total_value=total_value,
            target_currency=target_currency,
            stocks=[StockValueInTargetCurrency(**stock) for stock in converted_stocks],
            weights=weights
        )
        logger.info(f"Final response: {response}")
        return response

    async def get_exchange_rate(self, from_currency: str, to_currency: str):
        logger.info(f"Fetching exchange rate from {from_currency} to {to_currency}")
        if from_currency == to_currency:
            return {"rate": 1.0}
        try:
            rate_data = await self.fmp.get_forex_quote(f"{from_currency}{to_currency}")
            if rate_data and "price" in rate_data:
                return {"rate": rate_data["price"]}

            reverse_data = await self.fmp.get_forex_quote(f"{to_currency}{from_currency}")
            if reverse_data and "price" in reverse_data and reverse_data["price"] != 0:
                return {"rate": 1.0 / reverse_data["price"]}
        except Exception as e:
            logger.warning(f"Exchange rate fetch failed: {e}")
        return {"rate": 1.0}

    async def close(self):
        await self.fmp.close()
