from typing import List, Dict, Any, Tuple

QUESTIONS: List[Dict[str, Any]] = [
    {
        "id": 1,
        "text": "In general, how would your best friend describe you as a risk taker?",
        "choices": [
            {"id": "a", "text": "Takes very high risks", "score": 4},
            {"id": "b", "text": "Willing to take risks after completing adequate research", "score": 3},
            {"id": "c", "text": "Cautious", "score": 2},
            {"id": "d", "text": "A real risk avoider", "score": 1},
        ],
    },
    {
        "id": 2,
        "text": "If you unexpectedly received $20,000 to invest, what would you do?",
        "choices": [
            {"id": "a", "text": "Deposit it in a savings account or conservative income product", "score": 1},
            {"id": "b", "text": "Invest it in safe high quality bonds or bond mutual funds", "score": 2},
            {"id": "c", "text": "Invest it in stocks or stock mutual funds", "score": 3},
        ],
    },
    {
        "id": 3,
        "text": "In terms of experience, how comfortable are you investing in stocks or stock mutual funds?",
        "choices": [
            {"id": "a", "text": "Not at all comfortable", "score": 1},
            {"id": "b", "text": "Somewhat comfortable", "score": 2},
            {"id": "c", "text": "Very comfortable", "score": 3},
        ],
    },
    {
        "id": 4,
        "text": "Which situation would make you the happiest?",
        "choices": [
            {"id": "a", "text": "You win $50,000 in a publisher's contest", "score": 2},
            {"id": "b", "text": "You inherit $50,000 from a rich relative", "score": 1},
            {"id": "c", "text": "You earn $50,000 from a high-risk investment", "score": 3},
            {"id": "d", "text": "Any of the above—after all, you're happy with the $50,000", "score": 1},
        ],
    },
    {
        "id": 5,
        "text": "Some experts predict prices of assets such as gold, jewels, collectibles, and real estate to increase in value, while bond prices may fall. Most of your investment assets are now in high interest government bonds. What would you do?",
        "choices": [
            {"id": "a", "text": "Hold the bonds", "score": 1},
            {"id": "b", "text": "Sell the bonds, put half the proceeds into money market accounts, and the other half into hard assets", "score": 2},
            {"id": "c", "text": "Sell the bonds and put the total proceeds into hard assets", "score": 3},
            {"id": "d", "text": "Sell the bonds, put all the money into hard assets, and arrange financing to buy more", "score": 4},
        ],
    },
    {
        "id": 6,
        "text": "Suppose a relative left you an inheritance of $100,000, stipulating in the will that you invest ALL the money in ONE of the following choices. Which one would you select?",
        "choices": [
            {"id": "a", "text": "A savings account or money market mutual fund", "score": 1},
            {"id": "b", "text": "A mutual fund that owns stocks and bonds", "score": 2},
            {"id": "c", "text": "A portfolio of 15 common stocks", "score": 3},
            {"id": "d", "text": "Physical assets like gold, silver, and oil", "score": 4},
        ],
    },
    {
        "id": 7,
        "text": "If you had to invest $20,000, which of the following investment choices would you find most appealing?",
        "choices": [
            {"id": "a", "text": "60% in low-risk investments, 30% in medium-risk investments, 10% in high-risk investments", "score": 1},
            {"id": "b", "text": "30% in low-risk investments, 40% in medium-risk investments, 30% in high-risk investments", "score": 2},
            {"id": "c", "text": "10% in low-risk investments, 40% in medium-risk investments, 50% in high-risk investments", "score": 3},
        ],
    },
    {
        "id": 8,
        "text": "Your trusted friend and neighbor, an experienced geologist, is putting together a group of investors to fund an exploratory gold mining venture. The venture could pay back 50 to 100 times the investment if successful. If the mine is a bust, the entire investment is worthless. Your friend estimates the chance of success is only 20%. If you had the money, how much would you invest?",
        "choices": [
            {"id": "a", "text": "Nothing", "score": 1},
            {"id": "b", "text": "One month's salary", "score": 2},
            {"id": "c", "text": "Three month's salary", "score": 3},
            {"id": "d", "text": "Six month's salary", "score": 4},
        ],
    },
    {
        "id": 9,
        "text": "When you think of the word 'risk,' which of the following words comes to mind first?",
        "choices": [
            {"id": "a", "text": "Loss", "score": 1},
            {"id": "b", "text": "Uncertainty", "score": 2},
            {"id": "c", "text": "Opportunity", "score": 3},
            {"id": "d", "text": "Excitement", "score": 4},
        ],
    },
]

RISK_BUCKETS = [
    (9, 14, "Conservative", 4.0),
    (15, 19, "Moderately Conservative", 8.0),
    (20, 23, "Moderately Aggressive", 12.0),
    (24, 27, "Aggressive", 16.0),
    (28, 31, "Very Aggressive", 20.0), 
    (32, 40, "Extreme", 25.0),
]


def risk_level_from_total(total: int) -> Tuple[str, float]:
    """Return risk category name and target volatility score (0-10) from questionnaire *total* score."""
    for low, high, cat, vol in RISK_BUCKETS:
        if low <= total <= high:
            return cat, vol/100
    return "Moderately Conservative", 5.0/100

def get_questions() -> List[Dict[str, Any]]:
    return QUESTIONS


def score_answers(answers: Dict[int, str]) -> int:
    """answers: mapping from question_id to choice_id"""
    total = 0
    for q in QUESTIONS:
        qid = q["id"]
        if qid in answers:
            choice_id = answers[qid]
            choice = next((c for c in q["choices"] if c["id"] == choice_id), None)
            if choice:
                total += choice["score"]
    return total 