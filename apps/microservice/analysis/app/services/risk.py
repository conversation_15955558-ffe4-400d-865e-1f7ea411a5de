from typing import Tuple

# Risk buckets for questionnaire score mapping
RISK_BUCKETS = [
    (9, 14, "Conservative", 2.5),
    (15, 19, "Moderately Conservative", 5.0),
    (20, 23, "Moderately Aggressive", 7.5),
    (24, 27, "Aggressive", 10.0),
    # Extensions if needed can be added here
]

def risk_level_from_questionnaire(total: int) -> Tuple[str, float]:
    for low, high, category, vol_score in RISK_BUCKETS:
        if low <= total <= high:
            return category, vol_score
    return "Moderately Conservative", 5.0  # default fallback

def questionnaire_score_to_volatility(questionnaire_score: int) -> float:
    # Maps 9-27 questionnaire range to 0-10 volatility scale linearly
    min_q, max_q = 9, 27
    min_vol, max_vol = 0.0, 10.0

    vol = min_vol + ((questionnaire_score - min_q) * (max_vol - min_vol) / (max_q - min_q))
    vol = max(min_vol, min(max_vol, vol))
    return vol

def metrics_score_to_volatility(metrics_score: float) -> float:
    # Inverse relation: higher downside protection => lower volatility
    return 10.0 - metrics_score

def calculate_risk_matching(current_vol: float, desired_vol: float) -> float:
    """
    According to your given formula:
    If desired_vol < current_vol:
        score = 10 - [(current_vol - desired_vol) * 10 / desired_vol], floor 0
    Else:
        score = 10 - [(desired_vol - current_vol) * 10 / desired_vol], floor 5
    """
    if desired_vol == 0:
        return 5.0  # safe midpoint when desired vol is zero (to avoid div by zero)
    if desired_vol < current_vol:
        score = 10 - ((current_vol - desired_vol) * 10 / desired_vol)
        return max(0.0, score)
    else:
        score = 10 - ((desired_vol - current_vol) * 10 / desired_vol)
        return max(5.0, score)

def final_score_composite(risk_adjusted_score: float, downside_protection_score: float,
                          risk_matching_score: float = None) -> Tuple[float, str]:
    """
    Combine the three scores into final composite score (0-100), following weighted average:
    If risk_matching_score provided, average across 3 components
    Else average across 2 components.

    Ratings by final score:
     - 0–30: Poor
     - 40–59: Average
     - 60–100: Good
     - >=80: Very Good to Exceptional (you may tune this)
    """

    if risk_matching_score is not None:
        raw_score = (risk_adjusted_score + downside_protection_score + risk_matching_score) * 10 / 3
    else:
        raw_score = (risk_adjusted_score + downside_protection_score) * 10 / 2

    if raw_score < 40:
        rating = "Poor"
    elif raw_score < 60:
        rating = "Average"
    elif raw_score < 80:
        rating = "Good"
    else:
        rating = "Very Good to Exceptional"

    return raw_score, rating

def analyze_portfolio_risk_profile(downside_score: float, questionnaire_score: int) -> dict:
    current_vol = metrics_score_to_volatility(downside_score)
    desired_vol = questionnaire_score_to_volatility(questionnaire_score)
    risk_matching = calculate_risk_matching(current_vol, desired_vol)
    risk_category, _ = risk_level_from_questionnaire(questionnaire_score)
    return {
        "current_volatility": current_vol,
        "desired_volatility": desired_vol,
        "risk_matching_score": risk_matching,
        "risk_category": risk_category,
        "downside_score": downside_score,
        "questionnaire_score": questionnaire_score
    }