from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from app.models.company_profile import CompanyProfileRequest, CompanyProfileResponse
from app.models.currency import (
    CurrencyConversionRequest, CurrencyConversionResponse,
    AvailableCurrenciesResponse
)
from app.services.company_profile import CompanyProfileService

router = APIRouter()

@router.post("/portfolio-profile", response_model=CompanyProfileResponse)
async def analyze_portfolio_profile(payload: CompanyProfileRequest):
    """
    Analyze portfolio company profiles including sector, industry, and country allocations.
    Provides comprehensive company information for portfolio diversification analysis.
    """
    service = CompanyProfileService()
    try:
        portfolio_profile = await service.get_portfolio_company_profiles(
            payload.symbols,
            payload.weights
        )
        return CompanyProfileResponse(
            portfolio_profile=portfolio_profile,
            target_currency=payload.target_currency or "USD"
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Portfolio profile analysis failed: {str(e)}")
    finally:
        await service.close()


@router.post("/currency-conversion", response_model=CurrencyConversionResponse)
async def convert_portfolio_currency(payload: CurrencyConversionRequest):
    service = CompanyProfileService()
    try:
        if not payload.symbol_units:
            raise HTTPException(status_code=400, detail="symbol_units dict is required")
        current_conversion, historical_values, weights_used, data_quality_info = await service.convert_portfolio_value(
            symbol_units=payload.symbol_units,
            target_currency=payload.target_currency,
            start_date=payload.start_date,
            end_date=payload.end_date,
        )
        base_currency = payload.target_currency
        return CurrencyConversionResponse(
            portfolio_symbols=list(payload.symbol_units.keys()),
            weights_used=weights_used,
            base_currency=base_currency,
            target_currency=payload.target_currency,
            current_conversion=current_conversion,
            historical_values=historical_values,
            missing_data_periods=None,
            data_quality_info=data_quality_info,
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Currency conversion failed: {str(e)}")
    finally:
        await service.close()

@router.get("/exchange-rate")
async def get_exchange_rate(
    from_currency: str = Query(..., description="Source currency code (e.g., USD)"),
    to_currency: str = Query(..., description="Target currency code (e.g., EUR)")
):
    """Get current exchange rate between two currencies."""
    service = CompanyProfileService()
    try:
        rate_data = await service.get_exchange_rate(from_currency.upper(), to_currency.upper())
        return rate_data
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Exchange rate lookup failed: {str(e)}")
    finally:
        await service.close()

@router.get("/supported-currencies", response_model=AvailableCurrenciesResponse)
async def get_supported_currencies():
    """Get list of all supported currencies for conversion."""
    service = CompanyProfileService()
    try:
        currencies = await service.get_supported_currencies()
        return AvailableCurrenciesResponse(
            currencies=currencies,
            total_count=len(currencies)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch supported currencies: {str(e)}")
    finally:
        await service.close()
