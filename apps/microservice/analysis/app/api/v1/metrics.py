from fastapi import APIRouter, HTTPException, Query, logger
from typing import List
from app.models.metrics import MetricsRequest, MetricsResponse, IndividualStockMetrics
from app.services.metrics import MetricsService
router = APIRouter()
service = MetricsService()

@router.post("/portfolio", response_model=MetricsResponse)
async def calculate_portfolio_metrics(payload: MetricsRequest):
    if not payload.symbols:
        raise HTTPException(status_code=400, detail="At least one symbol required")
    if payload.weights and len(payload.weights) != len(payload.symbols):
        raise HTTPException(status_code=400, detail="Weights count must be same as symbols count")
    if payload.weights and abs(sum(payload.weights) - 1.0) > 0.001:
        raise HTTPException(status_code=400, detail="Weights must sum to 1.0")

    try:
        metrics_dict = await service.compute_portfolio_metrics(payload.symbols, payload.weights)
        return MetricsResponse(portfolio_metrics=metrics_dict)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to compute portfolio metrics: {e}")


@router.post("/individual", response_model=List[IndividualStockMetrics])
async def calculate_individual_metrics(symbols: List[str]):
    if not symbols:
        raise HTTPException(status_code=400, detail="At least one symbol required")

    try:
        results = await service.compute_individual_metrics(symbols)
        return [IndividualStockMetrics(**res) for res in results]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to compute individual metrics: {e}")

