from fastapi import APIRouter, HTTPException
from typing import List

from app.models.returns import CorrelationResponse, PortfolioReturnRequest, PortfolioReturnResponse, SymbolListRequest, TopMoversResponse
from app.services.returns import PortfolioAnalysisService

router = APIRouter()
service = PortfolioAnalysisService()

@router.post("/portfolio-returns", response_model=PortfolioReturnResponse)
async def portfolio_returns(req: PortfolioReturnRequest):
    if not req.transactions:
        raise HTTPException(status_code=400, detail="At least one transaction required")
    return await service.calculate_portfolio_returns(
        req.transactions,
        req.target_currency
    )

@router.post("/top-movers", response_model=TopMoversResponse)
async def top_movers(req: SymbolListRequest):
    if not req.symbols:
        raise HTTPException(status_code=400, detail="At least one symbol is required")
    return await service.get_top_movers(req.symbols)

@router.post("/correlations", response_model=CorrelationResponse)
async def correlations(req: SymbolListRequest):
    if not req.symbols:
        raise HTTPException(status_code=400, detail="At least one symbol is required")

    try:
        return await service.get_correlation_matrix(req.symbols)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")

