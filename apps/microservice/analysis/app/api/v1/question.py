from fastapi import APIRouter

from app.services.question import get_questions, score_answers, risk_level_from_total
from app.models.question import QuestionnaireAnswers, QuestionnaireScore

router = APIRouter()


@router.get("/", response_model=list)
async def list_questions():
    """Return questionnaire questions for front-end rendering."""
    return get_questions()


@router.post("/score", response_model=QuestionnaireScore)
async def compute_score(payload: QuestionnaireAnswers):
    """Compute total risk score and category from answers."""
    total = score_answers(payload.answers)
    category, desired_vol = risk_level_from_total(total)
    return QuestionnaireScore(total_score=total, category=category, desired_volatility=desired_vol) 