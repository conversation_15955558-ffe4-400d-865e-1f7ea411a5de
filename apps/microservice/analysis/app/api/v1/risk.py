from fastapi import APIRouter, HTTPException
from app.models.risk import RiskScoreRequest, RiskScoreResponse, PortfolioAnalysisRequest, PortfolioAnalysisResponse
from app.services.risk import (
    calculate_risk_matching,
    final_score_composite,
    questionnaire_score_to_volatility,
    metrics_score_to_volatility,
    analyze_portfolio_risk_profile,
    risk_level_from_questionnaire
)
from app.services.metrics import MetricsService

router = APIRouter()
metrics_service = MetricsService()

@router.post("/final-score", response_model=RiskScoreResponse)
async def compute_final_score(payload: RiskScoreRequest):
    try:
        risk_matching_score = calculate_risk_matching(payload.current_volatility, payload.desired_volatility)
        final_score, rating = final_score_composite(
            payload.risk_adjusted_score,
            payload.downside_protection_score,
            risk_matching_score,
        )
        return RiskScoreResponse(final_score=final_score, rating=rating)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to compute final score: {e}")

@router.post("/portfolio-analysis", response_model=PortfolioAnalysisResponse)
async def analyze_portfolio(payload: PortfolioAnalysisRequest):
    try:
        portfolio_metrics = await metrics_service.compute_portfolio_metrics(payload.symbols, payload.weights)

        risk_matching_score = None
        desired_volatility = None
        current_volatility = None

        if payload.questionnaire_score is not None:
            desired_volatility = questionnaire_score_to_volatility(payload.questionnaire_score)
            current_volatility = metrics_score_to_volatility(portfolio_metrics["downside_score"])
            risk_matching_score = calculate_risk_matching(current_volatility, desired_volatility)

        if risk_matching_score is not None:
            final_score_val, rating_val = final_score_composite(
                portfolio_metrics["risk_adjusted_score"],
                portfolio_metrics["downside_score"],
                risk_matching_score
            )
        else:
            final_score_val, rating_val = final_score_composite(
                portfolio_metrics["risk_adjusted_score"],
                portfolio_metrics["downside_score"],
                None
            )

        return PortfolioAnalysisResponse(
            portfolio_symbols=portfolio_metrics["portfolio_symbols"],
            weights_used=portfolio_metrics["weights_used"],
            annual_return=portfolio_metrics["annual_return"],
            annual_volatility=portfolio_metrics["annual_volatility"],
            downside_score=portfolio_metrics["downside_score"],
            risk_adjusted_score=portfolio_metrics["risk_adjusted_score"],
            risk_matching_score=risk_matching_score,
            current_volatility=current_volatility,
            desired_volatility=desired_volatility,
            questionnaire_score=payload.questionnaire_score,
            final_score=final_score_val,
            rating=rating_val,
            detailed_metrics=portfolio_metrics,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Portfolio analysis failed: {e}")

@router.post("/risk-profile")
async def get_risk_profile(questionnaire_score: int, downside_score: float):
    try:
        risk_analysis = analyze_portfolio_risk_profile(downside_score, questionnaire_score)
        return risk_analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Risk profile analysis failed: {e}")