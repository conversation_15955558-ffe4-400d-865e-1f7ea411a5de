from fastapi import APIRouter, HTTPException
from app.models.portfolio_weights import PortfolioUnitsRequest, PortfolioWeightsResponse
from app.services.portfolio_weights import PortfolioWeightsService
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/weights", response_model=PortfolioWeightsResponse)
async def calculate_portfolio_weights(payload: PortfolioUnitsRequest):
    logger.info(f"Received portfolio weight request: {payload}")
    service = PortfolioWeightsService()
    try:
        response = await service.calculate_weights(
            symbols=payload.symbols,
            units=payload.units,
            target_currency=payload.target_currency.upper() if payload.target_currency else "USD"
        )
        return response
    except ValueError as e:
        logger.warning(f"Client error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception("Unexpected server error")
        raise HTTPException(status_code=500, detail=f"Portfolio weights calculation failed: {str(e)}")
    finally:
        await service.close()
