from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class PortfolioValueConversion(BaseModel):
    base_currency: str = Field(..., description="Base currency of portfolio")
    target_currency: str = Field(..., description="Target conversion currency")
    conversion_rate: float = Field(..., description="Exchange rate used for conversion")
    original_value: float = Field(..., description="Value in base currency")
    converted_value: float = Field(..., description="Value in target currency")
    conversion_date: str = Field(..., description="Date of conversion rate")


class HistoricalPortfolioValue(BaseModel):
    date: str = Field(..., description="Date of portfolio valuation (YYYY-MM-DD)")
    portfolio_value_base: float = Field(..., description="Portfolio value in base currency")
    portfolio_value_target: float = Field(..., description="Portfolio value in target currency")
    exchange_rate: float = Field(..., description="Exchange rate for the date")
    base_currency: str = Field(..., description="Base currency code")
    target_currency: str = Field(..., description="Target currency code")


class CurrencyConversionRequest(BaseModel):
    symbol_units: Dict[str, float] = Field(
        ..., description="Mapping from stock symbol to the number of units held"
    )
    target_currency: str = Field("USD", description="Target currency code for conversion")
    start_date: Optional[str] = Field(None, description="Start date for historical conversion (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date for historical conversion (YYYY-MM-DD)")


class CurrencyConversionResponse(BaseModel):
    portfolio_symbols: List[str] = Field(..., description="Portfolio symbols analyzed")
    weights_used: List[float] = Field(..., description="Weights used (normalized)")
    base_currency: str = Field(..., description="Base currency code")
    target_currency: str = Field(..., description="Target currency code")
    current_conversion: PortfolioValueConversion = Field(..., description="Current currency conversion data")
    historical_values: Optional[List[HistoricalPortfolioValue]] = Field(
        None, description="Historical portfolio values with conversion rates"
    )
    missing_data_periods: Optional[List[str]] = Field(
        None, description="Dates with missing exchange rate data"
    )
    data_quality_info: Dict[str, Any] = Field(
        ..., description="Information about data completeness and preprocessing applied"
    )


class SupportedCurrency(BaseModel):
    code: str = Field(..., description="Currency code e.g. USD")
    name: str = Field(..., description="Full currency name")
    symbol: str = Field(..., description="Currency symbol (can be same as code)")


class AvailableCurrenciesResponse(BaseModel):
    currencies: List[SupportedCurrency] = Field(..., description="List of supported currencies")
    total_count: int = Field(..., description="Number of supported currencies")