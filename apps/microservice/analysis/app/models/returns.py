from pydantic import BaseModel, Field
from typing import Dict, List, Optional
from datetime import datetime

class Transaction(BaseModel):
    symbol: str = Field(..., description="Stock ticker symbol")
    type: str = Field(..., description="Transaction type (e.g., BUY, SELL, STOCK_DIVIDEND, TRANSFER)")
    quantity: float = Field(..., description="Number of units transacted")
    price: float = Field(..., description="Price per unit at transaction")
    tradeDate: datetime = Field(..., description="Date of the transaction")
    currency: Optional[str] = Field(None, description="Currency of the asset")

class InstrumentReturn(BaseModel):
    date: str
    symbol: str
    quantity: float
    avg_cost: float
    cost_of_holdings: float
    realized_pl: float
    unrealized_pl: float
    change_unrealized_pl: float
    net_pl: float
    net_pl_pct: float
    stock_return: float
    ltp: float

class DailyPortfolioReturn(BaseModel):
    date: str
    total_value: float
    portfolio_return: float
    change_pnl: float

class PortfolioReturnRequest(BaseModel):
    transactions: List[Transaction]
    target_currency: str

class PortfolioReturnResponse(BaseModel):
    target_currency: str
    instrument_returns: List[InstrumentReturn]
    portfolio_returns: List[DailyPortfolioReturn]
    realized_return: float = Field(..., description="Sum of all realized PnL")

    # performance & risk metrics
    cagr: float = Field(..., description="Compound Annual Growth Rate")
    best_month: float = Field(..., description="Best monthly return")
    worst_month: float = Field(..., description="Worst monthly return")
    sharpe_ratio: float = Field(..., description="Sharpe Ratio of the portfolio")
    sortino_ratio: float = Field(..., description="Sortino Ratio of the portfolio")
    volatility: float = Field(..., description="Annualized portfolio volatility")
    max_drawdown: float = Field(..., description="Maximum drawdown of the portfolio")
    var_95: float = Field(..., description="Value at Risk (95% confidence)")
    cvar_95: float = Field(..., description="Conditional Value at Risk (CVaR) at 95% confidence")
    omega_ratio: float = Field(..., description="Omega ratio of the portfolio")
    calmar_ratio: float = Field(..., description="Return vs drawdown")
    num_assets: int = Field(..., description="Number of distinct assets held at the end")
    herfindahl_index: float = Field(..., description="Herfindahl concentration index")
    max_weight: float = Field(..., description="Maximum weight of a single asset at the end")
    min_weight: float = Field(..., description="Minimum non-zero weight of an asset at the end")

    # added time-series series
    drawdown_series: Dict[str, float] = Field(..., description="Drawdown (underwater) series by date")
    rolling_vol_series: Dict[str, float] = Field(..., description="Rolling volatility series by date")
    annual_returns: Dict[str, float] = Field(..., description="Yearly returns by year")
    monthly_returns: Dict[str, float] = Field(..., description="Monthly returns by month")

class SymbolListRequest(BaseModel):
    symbols: List[str]

class TickerPerformance(BaseModel):
    symbol: str
    change_pct: float
    timeframe: str

class TopMoversResponse(BaseModel):
    daily_gainers: List[TickerPerformance]
    daily_losers: List[TickerPerformance]
    weekly_gainers: List[TickerPerformance]
    weekly_losers: List[TickerPerformance]
    monthly_gainers: List[TickerPerformance]
    monthly_losers: List[TickerPerformance]
    yearly_gainers: List[TickerPerformance]
    yearly_losers: List[TickerPerformance]

class CorrelationResponse(BaseModel):
    symbols: List[str] = Field(..., description="Same list of symbols in row/col order")
    correlation_matrix: List[List[float]] = Field(
        ...,
        description="NxN list of floats, row i / col j = corr(symbols[i], symbols[j])"
    )

