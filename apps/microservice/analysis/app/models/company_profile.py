from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class CompanyProfileData(BaseModel):
    """Company profile data from FMP API."""
    symbol: str = Field(..., description="Stock ticker symbol")
    companyName: str = Field(..., description="Full company name")
    currency: str = Field(..., description="Trading currency")
    exchangeShortName: str = Field(..., description="Exchange short name")
    industry: str = Field(..., description="Industry classification")
    sector: str = Field(..., description="Sector classification")
    country: str = Field(..., description="Country where company is headquartered")
    isEtf: bool = Field(False, description="Is this an ETF")
    isFund: bool = Field(False, description="Is this a Fund")
    isActivelyTrading: bool = Field(True, description="Is actively trading")
    mktCap: Optional[float] = Field(None, description="Market capitalization")
    price: Optional[float] = Field(None, description="Current price")
    beta: Optional[float] = Field(None, description="Beta value")
    volAvg: Optional[int] = Field(None, description="Average daily volume")
    website: Optional[str] = Field(None, description="Company website URL")
    description: Optional[str] = Field(None, description="Company description")
    ceo: Optional[str] = Field(None, description="CEO name")
    fullTimeEmployees: Optional[int] = Field(None, description="Number of full-time employees")
    address: Optional[str] = Field(None, description="Company address")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State or region")
    zip: Optional[str] = Field(None, description="Zip or postal code")
    image: Optional[str] = Field(None, description="Image URL or logo")


class PortfolioCompanyProfile(BaseModel):
    """Aggregated company profile data for portfolio analysis."""
    symbols: List[str] = Field(..., description="Portfolio stock ticker symbols")
    weights: List[float] = Field(..., description="Normalized allocation weights")
    companies: List[CompanyProfileData] = Field(..., description="Detailed company profiles")
    
    # Allocation breakdowns in percentages (sum ~ 100)
    sector_allocation: Dict[str, float] = Field(
        ..., description="Allocation percentage by sector"
    )
    industry_allocation: Dict[str, float] = Field(
        ..., description="Allocation percentage by industry"
    )
    country_allocation: Dict[str, float] = Field(
        ..., description="Allocation percentage by country"
    )
    currency_allocation: Dict[str, float] = Field(
        ..., description="Allocation percentage by trading currency"
    )
    
    # Portfolio summary statistics
    total_market_cap: Optional[float] = Field(None, description="Weighted total market capitalization")
    weighted_beta: Optional[float] = Field(None, description="Weighted beta of the portfolio")
    portfolio_currency: str = Field("USD", description="Base currency for portfolio valuation")


class CompanyProfileRequest(BaseModel):
    """Request schema for portfolio company profile and allocation analysis."""
    symbols: List[str] = Field(..., description="Stock ticker symbols for analysis")
    weights: Optional[List[float]] = Field(
        None, description="Allocation weights (sum must be 1.0)"
    )
    target_currency: Optional[str] = Field("USD", description="Target currency code")


class CompanyProfileResponse(BaseModel):
    """Response schema for portfolio profile endpoint."""
    portfolio_profile: PortfolioCompanyProfile
    conversion_rate_date: Optional[str] = Field(None, description="Date of conversion rate, if applicable")
    target_currency: str = Field("USD", description="Target currency code")


