from typing import List, Optional
from pydantic import BaseModel, Field

class PortfolioUnitsRequest(BaseModel):
    symbols: List[str] = Field(..., description="Stock tickers")
    units: List[float] = Field(..., description="Number of units owned per symbol")
    target_currency: Optional[str] = Field("USD", description="Currency to convert portfolio values into")

class StockValueInTargetCurrency(BaseModel):
    symbol: str
    units: float
    price: float
    currency: str
    value_in_target_currency: float

class PortfolioWeightsResponse(BaseModel):
    total_value: float
    target_currency: str
    stocks: List[StockValueInTargetCurrency]
    weights: List[float]