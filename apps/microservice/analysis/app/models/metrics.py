import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class MetricsRequest(BaseModel):
    symbols: List[str] = Field(..., description="Stock tickers in the portfolio")
    weights: Optional[List[float]] = Field(None, description="Optional weights for each symbol (must sum to 1.0). If not provided, equal weights are used.")

class IndividualStockMetrics(BaseModel):
    symbol: str
    annual_return: float
    annual_volatility: float
    downside_score: float
    risk_adjusted_score: float

    # Downside Protection Metrics
    Skewness: float = Field(..., alias="Skewness")
    Downside_Deviation: float = Field(..., alias="Downside Deviation")
    Omega_Ratio: float = Field(..., alias="Omega Ratio")
    CVaR_95: float = Field(..., alias="CVaR (95%)")
    Ulcer_Index: float = Field(..., alias="Ulcer Index")
    Tail_Ratio: float = Field(..., alias="Tail Ratio")

    # Risk-Adjusted Return Metrics
    Sharpe_Ratio: float = Field(..., alias="Sharpe Ratio")
    Sortino_Ratio: float = Field(..., alias="Sortino Ratio")
    Information_Ratio: float = Field(..., alias="Information Ratio")
    Treynor_Ratio: float = Field(..., alias="Treynor Ratio")

    # Individual metric scores
    Skewness_score: int = Field(..., alias="Skewness_score")
    Downside_Deviation_score: int = Field(..., alias="Downside Deviation_score")
    Omega_Ratio_score: int = Field(..., alias="Omega Ratio_score")
    CVaR_95_score: int = Field(..., alias="CVaR (95%)_score")
    Ulcer_Index_score: int = Field(..., alias="Ulcer Index_score")
    Tail_Ratio_score: int = Field(..., alias="Tail Ratio_score")
    Sharpe_Ratio_score: int = Field(..., alias="Sharpe Ratio_score")
    Sortino_Ratio_score: int = Field(..., alias="Sortino Ratio_score")
    Information_Ratio_score: int = Field(..., alias="Information Ratio_score")
    Treynor_Ratio_score: int = Field(..., alias="Treynor Ratio_score")

    class Config:
        allow_population_by_field_name = True

class PortfolioMetrics(BaseModel):
    portfolio_symbols: List[str]
    weights_used: List[float]
    annual_return: float
    annual_volatility: float
    downside_score: float
    risk_adjusted_score: float
    
    # Downside Protection Metrics
    Skewness: float = Field(..., alias="Skewness")
    Downside_Deviation: float = Field(..., alias="Downside Deviation")
    Omega_Ratio: float = Field(..., alias="Omega Ratio")
    CVaR_95: float = Field(..., alias="CVaR (95%)")
    Ulcer_Index: float = Field(..., alias="Ulcer Index")
    Tail_Ratio: float = Field(..., alias="Tail Ratio")

    # Risk-Adjusted Return Metrics
    Sharpe_Ratio: float = Field(..., alias="Sharpe Ratio")
    Sortino_Ratio: float = Field(..., alias="Sortino Ratio")
    Information_Ratio: float = Field(..., alias="Information Ratio")
    Treynor_Ratio: float = Field(..., alias="Treynor Ratio")

    metric_scores: Dict[str, int] = Field(..., description="Individual scores for each metric (1-10)")

    class Config:
        allow_population_by_field_name = True

class MetricsResponse(BaseModel):
    portfolio_metrics: PortfolioMetrics

    class Config:
        allow_population_by_field_name = True

