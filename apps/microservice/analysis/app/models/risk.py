from typing import Optional
from pydantic import BaseModel, Field
from typing import List, Dict, Any

class RiskScoreRequest(BaseModel):
    risk_adjusted_score: float = Field(..., description="Risk-adjusted return score (0-10)")
    downside_protection_score: float = Field(..., description="Downside protection score (0-10)")
    current_volatility: float = Field(..., description="Current portfolio volatility score (0-10)")
    desired_volatility: float = Field(..., description="Desired volatility score from questionnaire (0-10)")

class RiskScoreResponse(BaseModel):
    final_score: float = Field(..., description="Composite final score (0-100)")
    rating: str = Field(..., description="Final rating category")

class PortfolioAnalysisRequest(BaseModel):
    symbols: List[str] = Field(..., description="Stock tickers in the portfolio")
    weights: Optional[List[float]] = Field(None, description="Optional weights for each symbol (must sum to 1.0)")
    questionnaire_score: Optional[int] = Field(None, description="Risk questionnaire score (9-27) for risk matching")

class PortfolioAnalysisResponse(BaseModel):
    portfolio_symbols: List[str]
    weights_used: List[float]

    annual_return: float
    annual_volatility: float
    downside_score: float
    risk_adjusted_score: float

    risk_matching_score: Optional[float] = None
    current_volatility: Optional[float] = None
    desired_volatility: Optional[float] = None
    questionnaire_score: Optional[int] = None

    final_score: float
    rating: str

    detailed_metrics: Dict[str, Any] = Field(..., description="Detailed portfolio metrics")