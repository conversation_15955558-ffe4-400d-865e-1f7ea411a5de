import React from "react";
import { Input } from "@admin/components/ui/input";
import {
  EyeIcon,
  Edit2Icon,
  Trash2Icon,
  FilterIcon,
  UserIcon,
} from "lucide-react";
import { User, RoleColors } from "../types";

interface UserTableProps {
  users: User[];
  roleColors: RoleColors;
  search: string;
  roleFilter: string;
  statusFilter: string;
  activeTab: string;
  loading?: boolean;
  onSearchChange: (value: string) => void;
  onRoleFilterChange: (value: string) => void;
  onStatusFilterChange: (value: string) => void;
  onTabChange: (tab: string) => void;
}
const selectStyles = {
  appearance: "none" as const,
  backgroundImage: "none",
  paddingRight: "2.5rem",
};

export const UserTable: React.FC<UserTableProps> = ({
  users,
  roleColors,
  search,
  roleFilter,
  statusFilter,
  activeTab,
  loading = false,
  onSearchChange,
  onRoleFilterChange,
  onStatusFilter<PERSON>hange,
  onTabChange,
}) => {
  const tabs = [
    { id: 'admin', label: 'Admin Users' },
    { id: 'investors', label: 'Investors' },
    { id: 'archived', label: 'Archived Users' }
  ];

  return (
    <div className="bg-white rounded-xl shadow-sm mt-6 mx-8 px-6 pt-6 pb-2">
      <div className="flex items-center gap-6 mb-4">
        <div className="flex gap-2 border-b border-gray-200">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`px-3 pb-2 text-[15px] font-semibold border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-[#05A049] text-[#05A049]'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}{" "}
              <span className="ml-2 text-xs bg-gray-100 px-2 py-0.5 rounded-full text-gray-700 font-medium">
                {users.length}
              </span>
            </button>
          ))}
        </div>
        <div className="ml-auto flex items-center gap-2 text-gray-500 text-sm">
          <FilterIcon className="w-4 h-4 mr-1" />
          <span>{users.length} users found</span>
        </div>
      </div>

      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-4">
        <div className="flex gap-2 flex-1">
          <Input
            placeholder="Search users..."
            value={search}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-64 border border-gray-200 p-5 rounded-[10px] relative"
          />
          <select
            value={roleFilter}
            onChange={(e) => onRoleFilterChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
            style={selectStyles}
          >
            <option>All Roles</option>
            {Object.keys(roleColors).map((role, idx) => (
              <option key={idx}>{role}</option>
            ))}
          </select>
          <select
            value={statusFilter}
            onChange={(e) => onStatusFilterChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
            style={selectStyles}
          >
            <option>All Status</option>
            <option>Active</option>
            <option>Inactive</option>
          </select>
        </div>
      </div>

      <div className="overflow-x-auto rounded-[8px] border border-gray-100">
        <table className="min-w-full text-sm bg-white">
          <thead>
            <tr className="text-left text-gray-500 border-b bg-[#F9FAFB]">
              <th className="py-3 px-4 font-semibold">USER DETAILS</th>
              <th className="py-3 px-4 font-semibold">ROLE & DEPARTMENT</th>
              <th className="py-3 px-4 font-semibold">CONTACT INFO</th>
              <th className="py-3 px-4 font-semibold">STATUS</th>
              <th className="py-3 px-4 font-semibold">LAST LOGIN</th>
              <th className="py-3 px-4 font-semibold text-center">ACTIONS</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={6} className="py-8 text-center text-gray-500">
                  Loading users...
                </td>
              </tr>
            ) : users.length === 0 ? (
              <tr>
                <td colSpan={6} className="py-8 text-center text-gray-500">
                  No users found
                </td>
              </tr>
            ) : (
              users.map((user, idx) => (
              <tr
                key={user.id} 
                className="border-b hover:bg-gray-50 transition-all"
              >
                <td className="py-3 px-4 flex items-center gap-3 min-w-[220px]">
                  <span className="inline-flex items-center justify-center w-9 h-9 rounded-full bg-blue-50">
                    <UserIcon className="w-5 h-5 text-[#05A049]" />
                  </span>
                  <div>
                    <div className="font-medium text-gray-900 leading-tight">
                      {user.name}
                    </div>
                    <div className="text-xs text-gray-500 leading-tight">
                      {user.email}
                    </div>
                    <div className="text-[11px] text-gray-400 leading-tight">
                      {user.id}
                    </div>
                  </div>
                </td>
                <td className="py-3 px-4 min-w-[180px]">
                  <span
                    className={`inline-block rounded px-2 py-0.5 text-xs font-semibold mb-1 ${roleColors[user.role]}`}
                  >
                    {user.role}
                  </span>
                  <div className="text-xs text-gray-500">{user.department}</div>
                </td>
                <td className="py-3 px-4 min-w-[170px]">
                  <div className="text-xs text-gray-900">{user.contact}</div>
                  <div className="text-xs text-gray-400">
                    Joined: {user.joined}
                  </div>
                </td>
                <td className="py-3 px-4">
                  <span
                    className={`inline-block rounded-full px-3 py-1 text-xs font-semibold ${
                      user.status === "Active"
                        ? "bg-[#EDF8F2] text-[#05A049]"
                        : "bg-red-100 text-red-700"
                    }`}
                  >
                    {user.status}
                  </span>
                </td>
                <td className="py-3 px-4">
                  <div className="text-xs text-gray-900">{user.lastLogin}</div>
                </td>
                <td className="py-3 px-4 flex gap-2 justify-center">
                  <button
                    className="p-2 rounded hover:bg-gray-100"
                    title="Edit"
                  >
                    <Edit2Icon className="w-4 h-4 text-[#05A049]" />
                  </button>
                  <button
                    className="p-2 rounded hover:bg-gray-100"
                    title="Delete"
                  >
                    <Trash2Icon className="w-4 h-4 text-red-500" />
                  </button>
                </td>
              </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};
