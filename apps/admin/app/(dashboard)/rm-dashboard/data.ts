import {
  SummaryCard,
  RMPerformance,
  UpcomingMeeting,
  RecentActivity,
  WorkloadData,
} from "./types";

export const summaryData: SummaryCard[] = [
  {
    title: "Active RMs",
    value: "8",
    icon: "UsersIcon",
    color: "text-blue-600",
  },
  {
    title: "Total AUM",
    value: "$437.8M",
    icon: "DollarSignIcon",
    color: "text-blue-600",
  },
  {
    title: "Avg Resolution Time",
    value: "2.4 days",
    icon: "ClockIcon",
    color: "text-yellow-600",
  },
  {
    title: "Avg Satisfaction",
    value: "4.75",
    icon: "StarIcon",
    color: "text-purple-600",
  },
];

export const rmPerformanceData: RMPerformance[] = [
  {
    name: "<PERSON>",
    role: "Structured Products",
    customers: "45 customers",
    aum: "AUM: $124.2M",
    tickets: "28 resolved",
    performance: "Avg: 2.1 days",
    satisfaction: "4.9/5.0",
    rating: 5,
    revenue: "$124M",
    quarter: "This quarter",
    status: "Active",
    statusColor: "bg-[#EDF8F2] text-[#05A049]",
  },
  {
    name: "<PERSON>",
    role: "Fixed Income",
    customers: "38 customers",
    aum: "AUM: $98.7M",
    tickets: "22 resolved",
    performance: "Avg: 2.8 days",
    satisfaction: "4.6/5.0",
    rating: 5,
    revenue: "$98M",
    quarter: "This quarter",
    status: "Active",
    statusColor: "bg-[#EDF8F2] text-[#05A049]",
  },
  {
    name: "Emma Wilson",
    role: "Alternative Investments",
    customers: "52 customers",
    aum: "AUM: $156.8M",
    tickets: "31 resolved",
    performance: "Avg: 2.1 days",
    satisfaction: "4.7/5.0",
    rating: 5,
    revenue: "$157M",
    quarter: "This quarter",
    status: "Active",
    statusColor: "bg-[#EDF8F2] text-[#05A049]",
  },
  {
    name: "David Park",
    role: "Equity Products",
    customers: "29 customers",
    aum: "AUM: $87.1M",
    tickets: "18 resolved",
    performance: "Avg: 3.2 days",
    satisfaction: "4.5/5.0",
    rating: 4,
    revenue: "$87M",
    quarter: "This quarter",
    status: "On Leave",
    statusColor: "bg-yellow-100 text-yellow-800",
  },
];

export const upcomingMeetings: UpcomingMeeting[] = [
  {
    client: "Global Wealth Partners",
    rm: "Sarah Johnson",
    type: "Product Review",
    time: "Today at 10:00 AM",
    priority: "HIGH",
    priorityColor: "bg-red-100 text-red-800",
  },
  {
    client: "Tech Innovations Fund",
    rm: "Emma Wilson",
    type: "Risk Assessment",
    time: "Today at 2:30 PM",
    priority: "MEDIUM",
    priorityColor: "bg-yellow-100 text-yellow-800",
  },
  {
    client: "Premium Investments Ltd",
    rm: "Michael Chen",
    type: "Portfolio Discussion",
    time: "Tomorrow at 9:00 AM",
    priority: "HIGH",
    priorityColor: "bg-red-100 text-red-800",
  },
];

export const recentActivities: RecentActivity[] = [
  {
    rm: "Sarah Johnson",
    action: "Completed structured product quote for Premium Investments",
    time: "2 hours ago",
    avatar: "SJ",
  },
  {
    rm: "Emma Wilson",
    action: "Updated customer risk profile for Global Wealth Partners",
    time: "4 hours ago",
    avatar: "EW",
  },
  {
    rm: "Michael Chen",
    action: "Scheduled follow-up meeting with Tech Fund",
    time: "6 hours ago",
    avatar: "MC",
  },
];

export const workloadData: WorkloadData[] = [
  {
    name: "Sarah Johnson",
    role: "Structured Products",
    activeTickets: "12",
    pendingQuotes: "5",
    thisWeek: "High Load",
    loadColor: "bg-red-100 text-red-800",
  },
  {
    name: "Michael Chen",
    role: "Fixed Income",
    activeTickets: "8",
    pendingQuotes: "3",
    thisWeek: "Medium Load",
    loadColor: "bg-yellow-100 text-yellow-800",
  },
  {
    name: "Emma Wilson",
    role: "Alternative Investments",
    activeTickets: "15",
    pendingQuotes: "7",
    thisWeek: "High Load",
    loadColor: "bg-red-100 text-red-800",
  },
];
