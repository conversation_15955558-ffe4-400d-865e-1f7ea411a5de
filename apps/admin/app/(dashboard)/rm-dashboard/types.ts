export interface SummaryCard {
  title: string;
  value: string;
  icon: string;
  color: string;
}

export interface RMPerformance {
  name: string;
  role: string;
  customers: string;
  aum: string;
  tickets: string;
  performance: string;
  satisfaction: string;
  rating: number;
  revenue: string;
  quarter: string;
  status: string;
  statusColor: string;
}

export interface UpcomingMeeting {
  client: string;
  rm: string;
  type: string;
  time: string;
  priority: string;
  priorityColor: string;
}

export interface RecentActivity {
  rm: string;
  action: string;
  time: string;
  avatar: string;
}

export interface WorkloadData {
  name: string;
  role: string;
  activeTickets: string;
  pendingQuotes: string;
  thisWeek: string;
  loadColor: string;
}
