import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { UpcomingMeeting } from "../types";

interface UpcomingMeetingsProps {
  meetings: UpcomingMeeting[];
}

export const UpcomingMeetings: React.FC<UpcomingMeetingsProps> = ({
  meetings,
}) => {
  return (
    <Card className="bg-white">
      <div className="border-b p-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Upcoming Meetings
        </h3>
      </div>
      <CardContent className="p-4">
        <div className="space-y-4">
          {meetings.map((meeting, index) => (
            <div
              key={index}
              className="flex items-start justify-between p-3 border rounded-[8px]"
            >
              <div className="flex-1">
                <p className="font-medium text-gray-900">{meeting.client}</p>
                <p className="text-sm text-gray-600">
                  {meeting.rm} • {meeting.type}
                </p>
                <p className="text-sm text-gray-500">{meeting.time}</p>
              </div>
              <Badge
                className={`${meeting.priorityColor} text-xs rounded-full`}
              >
                {meeting.priority}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
