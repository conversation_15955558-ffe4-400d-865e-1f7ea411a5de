import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { WorkloadData } from "../types";

interface WorkloadDistributionProps {
  data: WorkloadData[];
}

export const WorkloadDistribution: React.FC<WorkloadDistributionProps> = ({
  data,
}) => {
  return (
    <Card className="bg-white">
      <div className="border-b p-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Current Workload Distribution
        </h3>
      </div>
      <CardContent className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {data.map((rm, index) => (
            <div key={index} className="p-4 border rounded-[8px]">
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="#3B82F6"
                    className="w-5 h-5"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.5 6a4.5 4.5 0 1 1 9 0a4.5 4.5 0 0 1 -9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1 -.437 .695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1 -.437-.695Z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{rm.name}</h4>
                  <p className="text-sm text-gray-600">{rm.role}</p>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Active Tickets:</span>
                  <span className="font-medium">{rm.activeTickets}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Pending Quotes:</span>
                  <span className="font-medium">{rm.pendingQuotes}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">This Week:</span>
                  <Badge className={`${rm.loadColor} text-xs rounded-full`}>
                    {rm.thisWeek}
                  </Badge>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
