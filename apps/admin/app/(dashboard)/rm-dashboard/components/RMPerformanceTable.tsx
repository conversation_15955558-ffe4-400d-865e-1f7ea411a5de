import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { StarIcon } from "lucide-react";
import { RMPerformance } from "../types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@admin/components/ui/table";

interface RMPerformanceTableProps {
  data: RMPerformance[];
}

export const RMPerformanceTable: React.FC<RMPerformanceTableProps> = ({
  data,
}) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`w-4 h-4 ${
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <Card className="bg-white mb-6">
      <div className="border-b p-6">
        <h3 className="text-lg font-semibold text-gray-900">
          RM Performance Metrics
        </h3>
      </div>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-left p-6 text-sm font-medium text-gray-700">
                  RELATIONSHIP MANAGER
                </TableHead>
                <TableHead className="text-left p-6 text-sm font-medium text-gray-700">
                  CUSTOMERS & AUM
                </TableHead>
                <TableHead className="text-left p-6 text-sm font-medium text-gray-700">
                  TICKETS PERFORMANCE
                </TableHead>
                <TableHead className="text-left p-6 text-sm font-medium text-gray-700">
                  SATISFACTION
                </TableHead>
                <TableHead className="text-left p-6 text-sm font-medium text-gray-700">
                  REVENUE GENERATED
                </TableHead>
                <TableHead className="text-left p-6 text-sm font-medium text-gray-700">
                  STATUS
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((rm, index) => (
                <TableRow key={index} className="hover:bg-gray-50">
                  <TableCell className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="#3B82F6"
                          className="w-5 h-5"
                        >
                          <path
                            fillRule="evenodd"
                            d="M7.5 6a4.5 4.5 0 1 1 9 0a4.5 4.5 0 0 1 -9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1 -.437 .695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1 -.437-.695Z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{rm.name}</p>
                        <p className="text-sm text-gray-600 mt-1">{rm.role}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="p-6">
                    <div>
                      <p className="font-medium text-gray-900">
                        {rm.customers}
                      </p>
                      <p className="text-sm text-gray-600 mt-1">{rm.aum}</p>
                    </div>
                  </TableCell>
                  <TableCell className="p-6">
                    <div>
                      <p className="font-medium text-gray-900">{rm.tickets}</p>
                      <p className="text-sm text-gray-600 mt-1">
                        {rm.performance}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell className="p-6">
                    <div className="flex items-center space-x-3">
                      <span className="font-medium text-gray-900">
                        {rm.satisfaction}
                      </span>
                      <div className="flex space-x-1">
                        {renderStars(rm.rating)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="p-6">
                    <div>
                      <p className="font-medium text-gray-900">{rm.revenue}</p>
                      <p className="text-sm text-gray-600 mt-1">{rm.quarter}</p>
                    </div>
                  </TableCell>
                  <TableCell className="p-6">
                    <Badge
                      className={`${rm.statusColor} text-xs px-3 py-1 rounded-full`}
                    >
                      {rm.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};
