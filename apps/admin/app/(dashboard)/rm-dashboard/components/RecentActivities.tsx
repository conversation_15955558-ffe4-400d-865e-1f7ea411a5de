import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { RecentActivity } from "../types";

interface RecentActivitiesProps {
  activities: RecentActivity[];
}

export const RecentActivities: React.FC<RecentActivitiesProps> = ({
  activities,
}) => {
  return (
    <Card className="bg-white">
      <div className="border-b p-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Recent Activities
        </h3>
      </div>
      <CardContent className="p-4">
        <div className="space-y-8">
          {activities.map((activity, index) => {
            let dotColor = "bg-blue-500";
            if (activity.rm === "Emma Wilson") dotColor = "bg-[#05A049]";
            if (activity.rm === "Michael Chen") dotColor = "bg-purple-500";
            return (
              <div key={index} className="flex items-start">
                <span
                  className={`w-3 h-3 rounded-full mt-2 mr-3 flex-shrink-0 ${dotColor} inline-block`}
                ></span>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{activity.rm}</p>
                  <p className="text-sm text-gray-600">{activity.action}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
