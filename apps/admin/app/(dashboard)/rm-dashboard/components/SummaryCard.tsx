import React from "react";
import { <PERSON>, CardContent } from "@admin/components/ui/card";
import { UsersIcon, DollarSignIcon, ClockIcon, StarIcon } from "lucide-react";
import { SummaryCard as SummaryCardType } from "../types";

const iconMap = {
  UsersIcon: UsersIcon,
  DollarSignIcon: DollarSignIcon,
  ClockIcon: ClockIcon,
  StarIcon: StarIcon,
};

interface SummaryCardProps {
  data: SummaryCardType;
}

export const SummaryCard: React.FC<SummaryCardProps> = ({ data }) => {
  const Icon = iconMap[data.icon as keyof typeof iconMap];
  const bgColor = data.color.replace("text-", "bg-").replace("-600", "-100");
  return (
    <Card className="bg-white">
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div
            className={`w-10 h-10 ${bgColor} rounded-[8px] flex items-center justify-center`}
          >
            <Icon className={`w-6 h-6 ${data.color}`} />
          </div>
          <div>
            <p className="text-sm text-gray-600">{data.title}</p>
            <p className={`text-2xl font-bold ${data.color}`}>{data.value}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
