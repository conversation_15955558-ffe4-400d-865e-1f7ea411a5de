"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { CustomSelect } from "@admin/components/ui/CustomSelect";
import { SearchIcon, DownloadIcon, PlusIcon } from "lucide-react";
import { AssignTicketModal } from "@admin/components/AssignTicketModal";
import { SummaryCard } from "./components/SummaryCard";
import { RMPerformanceTable } from "./components/RMPerformanceTable";
import { UpcomingMeetings } from "./components/UpcomingMeetings";
import { RecentActivities } from "./components/RecentActivities";
import { WorkloadDistribution } from "./components/WorkloadDistribution";
import {
  summaryData,
  rmPerformanceData,
  upcomingMeetings,
  recentActivities,
  workloadData,
} from "./data";

export const RMDashboard = (): JSX.Element => {
  const [searchTerm, setSearchTerm] = useState("");
  const [assignModalOpen, setAssignModalOpen] = useState(false);

  return (
    <div className="w-full h-full overflow-y-auto">
      {/* Header */}
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Relationship Manager Dashboard
            </h1>
            <p className="text-gray-600 text-sm">
              Overview of RM performance and activities
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button className="border border-gray-300 bg-white hover:bg-gray-50 text-black rounded-[8px]">
              <DownloadIcon className="w-4 h-4 mr-2" />
              Export Report
            </Button>
            <Button
              className="admin_green_gradient hover:admin_green_gradient_hover rounded-[8px] text-white"
              onClick={() => setAssignModalOpen(true)}
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              Assign Ticket
            </Button>
          </div>
        </div>
      </div>

      {/* Assign Ticket Modal */}
      <AssignTicketModal
        open={assignModalOpen}
        onClose={() => setAssignModalOpen(false)}
      />

      <div className="p-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {summaryData.map((item, index) => (
            <SummaryCard key={index} data={item} />
          ))}
        </div>

        {/* RM Performance Metrics */}
        <RMPerformanceTable data={rmPerformanceData} />

        {/* Bottom Section - Meetings and Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <UpcomingMeetings meetings={upcomingMeetings} />
          <RecentActivities activities={recentActivities} />
        </div>

        {/* Current Workload Distribution */}
        <WorkloadDistribution data={workloadData} />
      </div>
    </div>
  );
};
