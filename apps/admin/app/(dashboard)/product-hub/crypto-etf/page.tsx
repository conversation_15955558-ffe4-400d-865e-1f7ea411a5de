"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from "@admin/components/ui/button";
import { ArrowLeft, Download, MessageCircle, Save, Eye, Info, TrendingUp, BarChart3, PieChart, Calendar, DollarSign, Users, Shield, Target, Activity } from "lucide-react";
import { sampleCryptoETF, CryptoETFProduct } from "./data";
import {
  CryptoETFOverview,
  CryptoETFEssentials,
  CryptoETFVitals,
  CryptoETFValuation,
  CryptoETFExpenses,
  CryptoETFDividend,
  CryptoETFTaxAnalysis,
  CryptoETFPriceCharts,
  CryptoETFFundFlows,
  CryptoETFAUMInfluence,
  CryptoETFPerformance,
  CryptoETFRealtimeRating,
  CryptoETFSimilarRecommendations
} from "./components";
import axios from 'axios';
import { createCryptoEtf } from '@admin/app/lib/productApiService';

export default function CryptoETFDetailPage() {
  const searchParams = useSearchParams();
  const [mode, setMode] = useState<'form' | 'review'>('form');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cryptoETF, setCryptoETF] = useState<CryptoETFProduct>(sampleCryptoETF);
  const [vendor_id, setVendorId] = useState("");
  const router = useRouter();

  useEffect(() => {
    const vendorParam = searchParams.get('vendor');
    if (vendorParam) {
      setVendorId(vendorParam);
      setCryptoETF(prev => ({
        ...prev,
        vendor_id: vendorParam
      }));
    }
  }, [searchParams]);

  const handleProductDetailsSave = (productDetails: CryptoETFProduct['productDetails']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, productDetails }));
  };

  const handleEssentialsSave = (essentials: CryptoETFProduct['essentials']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, essentials }));
  };

  const handleVitalsSave = (vitals: CryptoETFProduct['vitals']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, vitals }));
  };

  const handleValuationSave = (fbtcValuation: CryptoETFProduct['fbtcValuation']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, fbtcValuation }));
  };

  const handleExpensesSave = (fbtcExpenses: CryptoETFProduct['fbtcExpenses']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, fbtcExpenses }));
  };

  const handleDividendSave = (fbtcDividend: CryptoETFProduct['fbtcDividend']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, fbtcDividend }));
  };

  const handleTaxAnalysisSave = (taxAnalysis: CryptoETFProduct['taxAnalysis']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, taxAnalysis }));
  };

  const handlePriceChartsSave = (priceVolumeCharts: CryptoETFProduct['priceVolumeCharts']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, priceVolumeCharts }));
  };

  const handleFundFlowsSave = (fbtcFundFlows: CryptoETFProduct['fbtcFundFlows']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, fbtcFundFlows }));
  };

  const handleAUMInfluenceSave = (priceVsFlowsAum: CryptoETFProduct['priceVsFlowsAum']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, priceVsFlowsAum }));
  };

  const handlePerformanceSave = (fbtcPerformance: CryptoETFProduct['fbtcPerformance']) => {
    setCryptoETF((prev: CryptoETFProduct) => ({ ...prev, fbtcPerformance }));
  };

  const handleRealtimeRatingSave = (realtimeRating: any) => {
    // This will be updated when we add realtime rating back
    console.log('Realtime rating save:', realtimeRating);
  };

  const handleSimilarRecommendationsSave = (similarRecommendations: any) => {
    // This will be updated when we add similar recommendations back
    console.log('Similar recommendations save:', similarRecommendations);
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      
      // Prepare the payload with all required fields
      const payload = {
        vendor_id,
        productDetails: {
          productCode: cryptoETF.productDetails?.productCode,
          logo_url: cryptoETF.productDetails?.logo_url,
          fullName: cryptoETF.productDetails?.fullName,
          yearlyReturn: cryptoETF.productDetails?.yearlyReturn,
          fundAum: cryptoETF.productDetails?.fundAum,
          riskProfile: cryptoETF.productDetails?.riskProfile,
          rebalancingFrequency: cryptoETF.productDetails?.rebalancingFrequency,
          fundInceptionDate: cryptoETF.productDetails?.fundInceptionDate
        },
        essentials: {
          price: cryptoETF.essentials?.price,
          priceChange: cryptoETF.essentials?.priceChange,
          priceChangePercentage: cryptoETF.essentials?.priceChangePercentage,
          priceDirection: cryptoETF.essentials?.priceDirection,
          category: cryptoETF.essentials?.category,
          lastUpdated: cryptoETF.essentials?.lastUpdated
        },
        vitals: {
          issuer: cryptoETF.vitals?.issuer,
          brand: cryptoETF.vitals?.brand,
          structure: cryptoETF.vitals?.structure,
          expenseRatio: cryptoETF.vitals?.expenseRatio,
          etfHomePage: cryptoETF.vitals?.etfHomePage,
          inception: cryptoETF.vitals?.inception
        },
        fbtcValuation: {
          peRatio: cryptoETF.fbtcValuation?.peRatio
        },
        fbtcExpenses: {
          expensesRatioAnalysis: cryptoETF.fbtcExpenses?.expensesRatioAnalysis
        },
        fbtcDividend: {
          dividend: cryptoETF.fbtcDividend?.dividend,
          dividendDate: cryptoETF.fbtcDividend?.dividendDate,
          annualDividendRate: cryptoETF.fbtcDividend?.annualDividendRate,
          annualDividendYield: cryptoETF.fbtcDividend?.annualDividendYield,
          etfDatabaseCategoryAverage: cryptoETF.fbtcDividend?.etfDatabaseCategoryAverage,
          factSetSegmentAverage: cryptoETF.fbtcDividend?.factSetSegmentAverage
        },
        taxAnalysis: {
          maxSTCapitalGainsRate: cryptoETF.taxAnalysis?.maxSTCapitalGainsRate,
          maxLtCapitalGainsRate: cryptoETF.taxAnalysis?.maxLtCapitalGainsRate,
          taxOnDistributions: cryptoETF.taxAnalysis?.taxOnDistributions,
          distributesK1: cryptoETF.taxAnalysis?.distributesK1
        },
        priceVolumeCharts: {
          historicalData: cryptoETF.priceVolumeCharts?.historicalData
        },
        fbtcFundFlows: {
          oneMonthNetFlows: cryptoETF.fbtcFundFlows?.oneMonthNetFlows,
          flowsData: cryptoETF.fbtcFundFlows?.flowsData
        },
        priceVsFlowsAum: {
          oneMonthNetAumChange: cryptoETF.priceVsFlowsAum?.oneMonthNetAumChange,
          influenceData: cryptoETF.priceVsFlowsAum?.influenceData
        },
        fbtcPerformance: {
          performanceMetrics: cryptoETF.fbtcPerformance?.performanceMetrics
        }
      };

      const response: any = await createCryptoEtf(payload);

      if (response && response.statusCode === 201) {
        alert('Crypto ETF product created successfully!');
        router.push('/product-hub');
      } else if (response && response.id) {
        alert('Crypto ETF product created successfully!');
        router.push('/product-hub');
      } else {
        throw new Error('Error creating crypto ETF product');
      }
    } catch (error: any) {
      console.error('Error creating crypto ETF product:', error);
      alert(error.message || 'Error creating crypto ETF product. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitWithValidation = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    handleSubmit();
  };

  const validateForm = () => {
    const errors = [];
    
    // Check required fields from productDetails
    if (!cryptoETF.productDetails?.fullName) errors.push("Full Name is required");
    if (!cryptoETF.productDetails?.yearlyReturn) errors.push("Yearly Return is required");
    if (!cryptoETF.productDetails?.fundAum) errors.push("Fund AUM is required");
    if (!cryptoETF.productDetails?.riskProfile) errors.push("Risk Profile is required");
    
    // Check required fields from essentials
    if (!cryptoETF.essentials?.price) errors.push("Price is required");
    if (!cryptoETF.essentials?.priceChange) errors.push("Price Change is required");
    if (!cryptoETF.essentials?.category) errors.push("Category is required");
    
    // Check required fields from vitals
    if (!cryptoETF.vitals?.issuer) errors.push("Issuer is required");
    if (!cryptoETF.vitals?.structure) errors.push("Structure is required");
    if (!cryptoETF.vitals?.expenseRatio) errors.push("Expense Ratio is required");
    
    return errors;
  };

  const getFormProgress = () => {
    const errors = validateForm();
    const totalFields = 10; // Approximate number of required fields
    const filledFields = totalFields - errors.length;
    return Math.max(0, Math.min(100, (filledFields / totalFields) * 100));
  };

  const handleReview = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    setMode('review');
  };

  const handleBackToForm = () => {
    setMode('form');
  };

  const handleDownload = () => {
    const dataStr = JSON.stringify(cryptoETF, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `crypto-etf-${cryptoETF.productDetails?.productCode || 'product'}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="text-gray-500 hover:text-gray-700"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {mode === 'form' ? 'Edit Crypto ETF Product' : 'Review Crypto ETF Product'}
                </h1>
                <p className="text-sm text-gray-500">
                  {cryptoETF.productDetails?.productCode || 'New Product'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {mode === 'form' && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                    className="text-gray-600 hover:text-gray-800"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleReview}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Review
                  </Button>
                </>
              )}
              
              {mode === 'review' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBackToForm}
                  className="text-gray-600 hover:text-gray-800"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Back to Edit
                </Button>
              )}
              
              <Button
                size="sm"
                onClick={handleSubmitWithValidation}
                disabled={isSubmitting}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    <span>Save Product</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      {mode === 'form' && (
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
              <span>Form Progress</span>
              <span>{Math.round(getFormProgress())}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getFormProgress()}%` }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Preview Content */}
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-1 space-y-6">
            <CryptoETFOverview 
              productDetails={cryptoETF.productDetails}
              onSave={handleProductDetailsSave}
            />
            
            <CryptoETFEssentials 
              essentials={cryptoETF.essentials}
              onSave={handleEssentialsSave}
            />
            
            <CryptoETFVitals 
              vitals={cryptoETF.vitals}
              onSave={handleVitalsSave}
            />
          </div>

          {/* Right Column */}
          <div className="lg:col-span-2 space-y-6">
            <CryptoETFValuation 
              fbtcValuation={cryptoETF.fbtcValuation}
              onSave={handleValuationSave}
            />
            
            <CryptoETFExpenses 
              fbtcExpenses={cryptoETF.fbtcExpenses}
              onSave={handleExpensesSave}
            />
            
            <CryptoETFDividend 
              fbtcDividend={cryptoETF.fbtcDividend}
              onSave={handleDividendSave}
            />
            
            <CryptoETFTaxAnalysis 
              taxAnalysis={cryptoETF.taxAnalysis}
              onSave={handleTaxAnalysisSave}
            />
            
            <CryptoETFPriceCharts 
              priceVolumeCharts={cryptoETF.priceVolumeCharts}
              onSave={handlePriceChartsSave}
            />
            
            <CryptoETFFundFlows 
              fbtcFundFlows={cryptoETF.fbtcFundFlows}
              onSave={handleFundFlowsSave}
            />
            
            <CryptoETFAUMInfluence 
              priceVsFlowsAum={cryptoETF.priceVsFlowsAum}
              onSave={handleAUMInfluenceSave}
            />
            
            <CryptoETFPerformance 
              fbtcPerformance={cryptoETF.fbtcPerformance}
              onSave={handlePerformanceSave}
            />
          </div>
        </div>
      </div>
    </div>
  );
} 