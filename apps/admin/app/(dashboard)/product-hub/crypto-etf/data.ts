// Crypto ETF Product Types based on MongoDB schema
export interface ProductDetails {
  productCode: string;
  logo_url: string;
  fullName: string;
  yearlyReturn: string;
  fundAum: string;
  riskProfile: string;
  rebalancingFrequency: string;
  fundInceptionDate: Date;
}

export interface Essentials {
  price: string;
  priceChange: string;
  priceChangePercentage: string;
  priceDirection: "up" | "down" | "neutral";
  category: string;
  lastUpdated: Date;
}

export interface Vitals {
  issuer: string;
  brand: string;
  structure: string;
  expenseRatio: string;
  etfHomePage: string;
  inception: Date;
}

export interface Ratio {
  fbtc: string;
  etfDatabaseCategoryAverage: string;
  factSetSegmentAverage: string;
}

export interface Dividend {
  dividend: string;
  dividendDate: string;
  annualDividendRate: string;
  annualDividendYield: string;
}

export interface FbtcDividend {
  dividend: string;
  dividendDate: string;
  annualDividendRate: string;
  annualDividendYield: string;
  etfDatabaseCategoryAverage: Dividend;
  factSetSegmentAverage: Dividend;
}

export interface TaxAnalysis {
  maxSTCapitalGainsRate: string;
  maxLtCapitalGainsRate: string;
  taxOnDistributions: string;
  distributesK1: string;
}

export interface PriceVolumeData {
  date: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface FundFlow {
  date: Date;
  inflow: number;
  outflow: number;
  netFlow: number;
}

export interface AumInfluence {
  date: Date;
  priceInfluence: number;
  fundFlow: number;
  aumChange: number;
}

export interface PerformanceComparison {
  fbtc: number;
  etfDatabaseCategoryAverage: number;
  factSetSegmentAverage: number;
}

export interface PerformanceMetrics {
  oneMonth: PerformanceComparison;
  threeMonth: PerformanceComparison;
  ytd: PerformanceComparison;
  oneYear: PerformanceComparison;
  threeYear: PerformanceComparison;
  fiveYear: PerformanceComparison;
}

export interface CryptoETFProduct {
  productId: string;
  vendor_id?: string;
  productDetails: ProductDetails;
  essentials: Essentials;
  vitals: Vitals;
  fbtcValuation: {
    peRatio: Ratio;
  };
  fbtcExpenses: {
    expensesRatioAnalysis: Ratio;
  };
  fbtcDividend: FbtcDividend;
  taxAnalysis: TaxAnalysis;
  priceVolumeCharts: {
    historicalData: PriceVolumeData[];
  };
  fbtcFundFlows: {
    oneMonthNetFlows: string;
    flowsData: FundFlow[];
  };
  priceVsFlowsAum: {
    oneMonthNetAumChange: string;
    influenceData: AumInfluence[];
  };
  fbtcPerformance: {
    performanceMetrics: PerformanceMetrics;
  };
}

// Similar Recommendation Type for Crypto ETF
export interface SimilarRecommendation {
  fundName: string;
  yearlyReturn: string;
  fundAUM: string;
  riskProfile: string;
  rebalancingFrequency: string;
  fundInceptionDate: string;
}

// Sample data based on the MongoDB schema
export const sampleCryptoETF: CryptoETFProduct = {
  productId: "",
  productDetails: {
    productCode: "FBTC",
    logo_url: "",
    fullName: "Fidelity Wise Origin Bitcoin Fund",
    yearlyReturn: "20.00",
    fundAum: "$1.5B",
    riskProfile: "Medium",
    rebalancingFrequency: "Monthly",
    fundInceptionDate: new Date("2024-01-05"),
  },
  essentials: {
    price: "$60.06",
    priceChange: "$1.39",
    priceChangePercentage: "2.37%",
    priceDirection: "up",
    category: "Currency",
    lastUpdated: new Date("2025-06-23"),
  },
  vitals: {
    issuer: "BlackRock, Inc.",
    brand: "iShares",
    structure: "ETF",
    expenseRatio: "0.12%",
    etfHomePage: "https://www.ishares.com",
    inception: new Date("2024-01-05"),
  },
  fbtcValuation: {
    peRatio: {
      fbtc: "N/A",
      etfDatabaseCategoryAverage: "N/A",
      factSetSegmentAverage: "N/A",
    },
  },
  fbtcExpenses: {
    expensesRatioAnalysis: {
      fbtc: "0.25%",
      etfDatabaseCategoryAverage: "0.58%",
      factSetSegmentAverage: "1.11%",
    },
  },
  fbtcDividend: {
    dividend: "N/A",
    dividendDate: "N/A",
    annualDividendRate: "$2.80",
    annualDividendYield: "7.90%",
    etfDatabaseCategoryAverage: {
      dividend: "$1.31",
      dividendDate: "2025-06-15",
      annualDividendRate: "$2.50",
      annualDividendYield: "6.50%",
    },
    factSetSegmentAverage: {
      dividend: "$0.32",
      dividendDate: "2025-06-15",
      annualDividendRate: "$1.20",
      annualDividendYield: "4.20%",
    },
  },
  taxAnalysis: {
    maxSTCapitalGainsRate: "39.60%",
    maxLtCapitalGainsRate: "39.60%",
    taxOnDistributions: "Ordinary Income",
    distributesK1: "No",
  },
  priceVolumeCharts: {
    historicalData: [
      { date: new Date("2025-05-31"), open: 75.00, high: 76.50, low: 74.50, close: 75.84, volume: 15000000 },
      { date: new Date("2025-06-09"), open: 75.84, high: 81.00, low: 75.00, close: 80.00, volume: 17000000 },
      { date: new Date("2025-06-12"), open: 80.00, high: 87.00, low: 79.50, close: 86.59, volume: 17012800 },
    ],
  },
  fbtcFundFlows: {
    oneMonthNetFlows: "-97.89 M",
    flowsData: [
      { date: new Date("2025-05-22"), inflow: 0.1, outflow: 0, netFlow: 0.1 },
      { date: new Date("2025-05-28"), inflow: 0, outflow: 0.2, netFlow: -0.2 },
      { date: new Date("2025-06-03"), inflow: 0.1, outflow: 0, netFlow: 0.1 },
      { date: new Date("2025-06-09"), inflow: 0, outflow: 0.3, netFlow: -0.3 },
      { date: new Date("2025-06-15"), inflow: 0.1, outflow: 0, netFlow: 0.1 },
      { date: new Date("2025-06-19"), inflow: 0, outflow: 0.1, netFlow: -0.1 },
    ],
  },
  priceVsFlowsAum: {
    oneMonthNetAumChange: "150.87 M",
    influenceData: [
      { date: new Date("2025-05-22"), priceInfluence: 0.5, fundFlow: 0.1, aumChange: 0.6 },
      { date: new Date("2025-05-28"), priceInfluence: -0.8, fundFlow: -0.2, aumChange: -1.0 },
      { date: new Date("2025-06-03"), priceInfluence: 0.3, fundFlow: 0.1, aumChange: 0.4 },
      { date: new Date("2025-06-09"), priceInfluence: -1.2, fundFlow: -0.3, aumChange: -1.5 },
      { date: new Date("2025-06-15"), priceInfluence: 0.6, fundFlow: 0.1, aumChange: 0.7 },
      { date: new Date("2025-06-19"), priceInfluence: -0.4, fundFlow: -0.1, aumChange: -0.5 },
    ],
  },
  fbtcPerformance: {
    performanceMetrics: {
      oneMonth: { fbtc: 5, etfDatabaseCategoryAverage: 0, factSetSegmentAverage: 0 },
      threeMonth: { fbtc: 23, etfDatabaseCategoryAverage: 15, factSetSegmentAverage: 0 },
      ytd: { fbtc: 10, etfDatabaseCategoryAverage: 10, factSetSegmentAverage: 10 },
      oneYear: { fbtc: 60, etfDatabaseCategoryAverage: 55, factSetSegmentAverage: 50 },
      threeYear: { fbtc: 5, etfDatabaseCategoryAverage: 5, factSetSegmentAverage: 5 },
      fiveYear: { fbtc: 5, etfDatabaseCategoryAverage: 5, factSetSegmentAverage: 5 },
    },
  },
};

// RealtimeRating type for CryptoETFRealtimeRating
export interface RealtimeRating {
  overallRating: string | number;
  metrics: Array<{
    metric: string;
    metricRealtimeRating: string | number;
    aPlusMetricRatedETF: string | number;
  }>;
} 