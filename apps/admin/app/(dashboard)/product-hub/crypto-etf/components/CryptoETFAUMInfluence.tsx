import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { AumInfluence } from "../data";

interface CryptoETFAUMInfluenceProps {
  priceVsFlowsAum: {
    oneMonthNetAumChange: string;
    influenceData: AumInfluence[];
  };
  onSave: (priceVsFlowsAum: { oneMonthNetAumChange: string; influenceData: AumInfluence[] }) => void;
}

export function CryptoETFAUMInfluence({ priceVsFlowsAum, onSave }: CryptoETFAUMInfluenceProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<{ oneMonthNetAumChange: string; influenceData: AumInfluence[] }>(priceVsFlowsAum);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(priceVsFlowsAum);
    setIsEditing(false);
  };

  const addInfluenceData = () => {
    const newInfluenceData: AumInfluence = {
      date: new Date(),
      priceInfluence: 0,
      fundFlow: 0,
      aumChange: 0,
    };
    setFormData({
      ...formData,
      influenceData: [...formData.influenceData, newInfluenceData]
    });
  };

  const removeInfluenceData = (index: number) => {
    setFormData({
      ...formData,
      influenceData: formData.influenceData.filter((_, i) => i !== index)
    });
  };

  const updateInfluenceData = (index: number, field: keyof AumInfluence, value: string | number) => {
    const updatedData = [...formData.influenceData];
    if (field === 'date') {
      updatedData[index] = { ...updatedData[index], date: new Date(value as string) };
    } else {
      updatedData[index] = { ...updatedData[index], [field]: value };
    }
    setFormData({
      ...formData,
      influenceData: updatedData
    });
  };

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Price vs Flows AUM:</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <span className="text-sm text-gray-500">One Month Net AUM Change</span>
            <p className="font-medium">{priceVsFlowsAum.oneMonthNetAumChange}</p>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 font-medium">Date</th>
                  <th className="text-right py-2 font-medium">Price Influence</th>
                  <th className="text-right py-2 font-medium">Fund Flow</th>
                  <th className="text-right py-2 font-medium">AUM Change</th>
                </tr>
              </thead>
              <tbody>
                {priceVsFlowsAum.influenceData.map((data, index) => (
                  <tr key={index} className="border-b">
                    <td className="py-2">{data.date.toLocaleDateString()}</td>
                    <td className={`text-right py-2 ${data.priceInfluence >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {data.priceInfluence.toFixed(2)}
                    </td>
                    <td className={`text-right py-2 ${data.fundFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {data.fundFlow.toFixed(2)}
                    </td>
                    <td className={`text-right py-2 font-medium ${data.aumChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {data.aumChange.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit Price vs Flows AUM</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">One Month Net AUM Change</label>
          <Input
            value={formData.oneMonthNetAumChange}
            onChange={(e) => setFormData({ ...formData, oneMonthNetAumChange: e.target.value })}
          />
        </div>
        
        <div className="space-y-4">
          {formData.influenceData.map((data, index) => (
            <div key={index} className="border p-4 rounded-lg">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-sm font-medium">Influence Data {index + 1}</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeInfluenceData(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  Remove
                </Button>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Date</label>
                  <Input
                    type="date"
                    value={data.date.toISOString().split('T')[0]}
                    onChange={(e) => updateInfluenceData(index, 'date', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Price Influence</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.priceInfluence}
                    onChange={(e) => updateInfluenceData(index, 'priceInfluence', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Fund Flow</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.fundFlow}
                    onChange={(e) => updateInfluenceData(index, 'fundFlow', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">AUM Change</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.aumChange}
                    onChange={(e) => updateInfluenceData(index, 'aumChange', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <Button
          variant="outline"
          onClick={addInfluenceData}
          className="w-full"
        >
          Add Influence Data
        </Button>
        
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 