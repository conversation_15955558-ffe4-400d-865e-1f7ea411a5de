import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { TaxAnalysis } from "../data";

interface CryptoETFTaxAnalysisProps {
  taxAnalysis: TaxAnalysis;
  onSave: (taxAnalysis: TaxAnalysis) => void;
}

export function CryptoETFTaxAnalysis({ taxAnalysis, onSave }: CryptoETFTaxAnalysisProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<TaxAnalysis>(taxAnalysis);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(taxAnalysis);
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Tax Analysis:</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-500">Max ST Capital Gains Rate</span>
              <p className="font-medium">{taxAnalysis.maxSTCapitalGainsRate}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Max LT Capital Gains Rate</span>
              <p className="font-medium">{taxAnalysis.maxLtCapitalGainsRate}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Tax on Distributions</span>
              <p className="font-medium">{taxAnalysis.taxOnDistributions}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Distributes K1</span>
              <p className="font-medium">{taxAnalysis.distributesK1}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit Tax Analysis</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Max ST Capital Gains Rate</label>
            <Input
              value={formData.maxSTCapitalGainsRate}
              onChange={(e) => setFormData({ ...formData, maxSTCapitalGainsRate: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Max LT Capital Gains Rate</label>
            <Input
              value={formData.maxLtCapitalGainsRate}
              onChange={(e) => setFormData({ ...formData, maxLtCapitalGainsRate: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Tax on Distributions</label>
            <Input
              value={formData.taxOnDistributions}
              onChange={(e) => setFormData({ ...formData, taxOnDistributions: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Distributes K1</label>
            <Input
              value={formData.distributesK1}
              onChange={(e) => setFormData({ ...formData, distributesK1: e.target.value })}
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 