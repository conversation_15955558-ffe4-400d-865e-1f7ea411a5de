import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Vitals } from "../data";

interface CryptoETFVitalsProps {
  vitals: Vitals;
  onSave: (vitals: Vitals) => void;
}

export function CryptoETFVitals({ vitals, onSave }: CryptoETFVitalsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Vitals>(vitals);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(vitals);
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Vitals:</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-500">Issuer</span>
              <p className="font-medium">{vitals.issuer}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Brand</span>
              <p className="font-medium">{vitals.brand}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Structure</span>
              <p className="font-medium">{vitals.structure}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Expense Ratio</span>
              <p className="font-medium">{vitals.expenseRatio}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">ETF Home Page</span>
              <p className="font-medium">{vitals.etfHomePage}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Inception</span>
              <p className="font-medium">{vitals.inception.toLocaleDateString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit Vitals</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Issuer</label>
            <Input
              value={formData.issuer}
              onChange={(e) => setFormData({ ...formData, issuer: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Brand</label>
            <Input
              value={formData.brand}
              onChange={(e) => setFormData({ ...formData, brand: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Structure</label>
            <Input
              value={formData.structure}
              onChange={(e) => setFormData({ ...formData, structure: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Expense Ratio</label>
            <Input
              value={formData.expenseRatio}
              onChange={(e) => setFormData({ ...formData, expenseRatio: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">ETF Home Page</label>
            <Input
              value={formData.etfHomePage}
              onChange={(e) => setFormData({ ...formData, etfHomePage: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Inception</label>
            <Input
              type="date"
              value={formData.inception.toISOString().split('T')[0]}
              onChange={(e) => setFormData({ ...formData, inception: new Date(e.target.value) })}
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 