import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { CustomSelect } from "@admin/components/ui/CustomSelect";
import { Essentials } from "../data";

interface CryptoETFEssentialsProps {
  essentials: Essentials;
  onSave: (essentials: Essentials) => void;
}

export function CryptoETFEssentials({ essentials, onSave }: CryptoETFEssentialsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Essentials>(essentials);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(essentials);
    setIsEditing(false);
  };

  const isPositiveChange = essentials.priceDirection === "up";

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Essentials:</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-500">Price</span>
              <p className="font-medium">{essentials.price}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Price Change</span>
              <p className={`font-medium ${isPositiveChange ? 'text-green-600' : 'text-red-600'}`}>
                {essentials.priceChange} ({essentials.priceChangePercentage})
              </p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Category</span>
              <p className="font-medium">{essentials.category}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Last Updated</span>
              <p className="font-medium">{essentials.lastUpdated.toLocaleDateString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit Essentials</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Price</label>
            <Input
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Price Change</label>
            <Input
              value={formData.priceChange}
              onChange={(e) => setFormData({ ...formData, priceChange: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Price Change Percentage</label>
            <Input
              value={formData.priceChangePercentage}
              onChange={(e) => setFormData({ ...formData, priceChangePercentage: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Price Direction</label>
            <CustomSelect
              options={["up", "down", "neutral"]}
              value={formData.priceDirection}
              onChange={(value: string) => setFormData({ ...formData, priceDirection: value as "up" | "down" | "neutral" })}
              placeholder="Select Direction"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Category</label>
            <Input
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Last Updated</label>
            <Input
              type="date"
              value={formData.lastUpdated.toISOString().split('T')[0]}
              onChange={(e) => setFormData({ ...formData, lastUpdated: new Date(e.target.value) })}
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 