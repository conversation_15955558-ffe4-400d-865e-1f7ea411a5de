import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { FundFlow } from "../data";

interface CryptoETFFundFlowsProps {
  fbtcFundFlows: {
    oneMonthNetFlows: string;
    flowsData: FundFlow[];
  };
  onSave: (fbtcFundFlows: { oneMonthNetFlows: string; flowsData: FundFlow[] }) => void;
}

export function CryptoETFFundFlows({ fbtcFundFlows, onSave }: CryptoETFFundFlowsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<{ oneMonthNetFlows: string; flowsData: FundFlow[] }>(fbtcFundFlows);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(fbtcFundFlows);
    setIsEditing(false);
  };

  const addFlowData = () => {
    const newFlowData: FundFlow = {
      date: new Date(),
      inflow: 0,
      outflow: 0,
      netFlow: 0,
    };
    setFormData({
      ...formData,
      flowsData: [...formData.flowsData, newFlowData]
    });
  };

  const removeFlowData = (index: number) => {
    setFormData({
      ...formData,
      flowsData: formData.flowsData.filter((_, i) => i !== index)
    });
  };

  const updateFlowData = (index: number, field: keyof FundFlow, value: string | number) => {
    const updatedData = [...formData.flowsData];
    if (field === 'date') {
      updatedData[index] = { ...updatedData[index], date: new Date(value as string) };
    } else {
      updatedData[index] = { ...updatedData[index], [field]: value };
    }
    setFormData({
      ...formData,
      flowsData: updatedData
    });
  };

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">FBTC Fund Flows:</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <span className="text-sm text-gray-500">One Month Net Flows</span>
            <p className="font-medium">{fbtcFundFlows.oneMonthNetFlows}</p>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 font-medium">Date</th>
                  <th className="text-right py-2 font-medium">Inflow</th>
                  <th className="text-right py-2 font-medium">Outflow</th>
                  <th className="text-right py-2 font-medium">Net Flow</th>
                </tr>
              </thead>
              <tbody>
                {fbtcFundFlows.flowsData.map((data, index) => (
                  <tr key={index} className="border-b">
                    <td className="py-2">{data.date.toLocaleDateString()}</td>
                    <td className="text-right py-2 text-green-600">{data.inflow.toFixed(2)}</td>
                    <td className="text-right py-2 text-red-600">{data.outflow.toFixed(2)}</td>
                    <td className={`text-right py-2 font-medium ${data.netFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {data.netFlow.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit FBTC Fund Flows</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">One Month Net Flows</label>
          <Input
            value={formData.oneMonthNetFlows}
            onChange={(e) => setFormData({ ...formData, oneMonthNetFlows: e.target.value })}
          />
        </div>
        
        <div className="space-y-4">
          {formData.flowsData.map((data, index) => (
            <div key={index} className="border p-4 rounded-lg">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-sm font-medium">Flow Data {index + 1}</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeFlowData(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  Remove
                </Button>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Date</label>
                  <Input
                    type="date"
                    value={data.date.toISOString().split('T')[0]}
                    onChange={(e) => updateFlowData(index, 'date', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Inflow</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.inflow}
                    onChange={(e) => updateFlowData(index, 'inflow', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Outflow</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.outflow}
                    onChange={(e) => updateFlowData(index, 'outflow', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Net Flow</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.netFlow}
                    onChange={(e) => updateFlowData(index, 'netFlow', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <Button
          variant="outline"
          onClick={addFlowData}
          className="w-full"
        >
          Add Flow Data
        </Button>
        
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 