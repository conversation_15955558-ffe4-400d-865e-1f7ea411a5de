import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Info } from "lucide-react";
import { Ratio } from "../data";

interface CryptoETFValuationProps {
  fbtcValuation: {
    peRatio: Ratio;
  };
  onSave: (fbtcValuation: { peRatio: Ratio }) => void;
}

export function CryptoETFValuation({ fbtcValuation, onSave }: CryptoETFValuationProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<{ peRatio: Ratio }>(fbtcValuation);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(fbtcValuation);
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CardTitle className="text-lg font-semibold">FBTC Valuation</CardTitle>
              <Info className="w-4 h-4 text-gray-500" />
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <span className="text-sm text-gray-500">FBTC P/E Ratio</span>
              <p className="font-medium">{fbtcValuation.peRatio.fbtc}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">ETF Database Category Average</span>
              <p className="font-medium">{fbtcValuation.peRatio.etfDatabaseCategoryAverage}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">FactSet Segment Average</span>
              <p className="font-medium">{fbtcValuation.peRatio.factSetSegmentAverage}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit FBTC Valuation</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">FBTC P/E Ratio</label>
            <Input
              value={formData.peRatio.fbtc}
              onChange={(e) => setFormData({
                ...formData,
                peRatio: { ...formData.peRatio, fbtc: e.target.value }
              })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">ETF Database Category Average</label>
            <Input
              value={formData.peRatio.etfDatabaseCategoryAverage}
              onChange={(e) => setFormData({
                ...formData,
                peRatio: { ...formData.peRatio, etfDatabaseCategoryAverage: e.target.value }
              })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">FactSet Segment Average</label>
            <Input
              value={formData.peRatio.factSetSegmentAverage}
              onChange={(e) => setFormData({
                ...formData,
                peRatio: { ...formData.peRatio, factSetSegmentAverage: e.target.value }
              })}
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 