import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@admin/components/ui/card";
import { Info } from "lucide-react";
import { RealtimeRating } from '../data';

interface CryptoETFRealtimeRatingProps {
  realtimeRating: RealtimeRating;
  onSave: (realtimeRating: RealtimeRating) => void;
}

export function CryptoETFRealtimeRating({ realtimeRating, onSave }: CryptoETFRealtimeRatingProps) {
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle className="text-lg font-semibold">Realtime Rating</CardTitle>
            <Info className="w-4 h-4 text-gray-500" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Rating */}
        <div className="text-center py-4">
          <div className="text-2xl mb-2">{realtimeRating.overallRating}</div>
          <div className="text-sm text-gray-600">Overall Rating</div>
        </div>

        {/* Metrics Table */}
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2 font-medium">Metric</th>
                <th className="text-center py-2 font-medium">Metric Realtime Rating</th>
                <th className="text-center py-2 font-medium">A+ Metric Rated ETF</th>
              </tr>
            </thead>
            <tbody>
              {realtimeRating.metrics.map((metric, index) => (
                <tr key={index} className="border-b">
                  <td className="py-2">{metric.metric}</td>
                  <td className="text-center py-2">{metric.metricRealtimeRating}</td>
                  <td className="text-center py-2">{metric.aPlusMetricRatedETF}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
} 