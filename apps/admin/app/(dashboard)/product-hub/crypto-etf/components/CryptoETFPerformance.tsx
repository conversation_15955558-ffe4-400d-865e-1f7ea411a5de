import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { PerformanceMetrics, PerformanceComparison } from "../data";

interface CryptoETFPerformanceProps {
  fbtcPerformance: {
    performanceMetrics: PerformanceMetrics;
  };
  onSave: (fbtcPerformance: { performanceMetrics: PerformanceMetrics }) => void;
}

export function CryptoETFPerformance({ fbtcPerformance, onSave }: CryptoETFPerformanceProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<{ performanceMetrics: PerformanceMetrics }>(fbtcPerformance);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(fbtcPerformance);
    setIsEditing(false);
  };

  const updatePerformanceMetric = (period: keyof PerformanceMetrics, field: keyof PerformanceComparison, value: number) => {
    setFormData({
      ...formData,
      performanceMetrics: {
        ...formData.performanceMetrics,
        [period]: {
          ...formData.performanceMetrics[period],
          [field]: value
        }
      }
    });
  };

  const performancePeriods = [
    { key: 'oneMonth', label: '1 Month' },
    { key: 'threeMonth', label: '3 Month' },
    { key: 'ytd', label: 'YTD' },
    { key: 'oneYear', label: '1 Year' },
    { key: 'threeYear', label: '3 Year' },
    { key: 'fiveYear', label: '5 Year' },
  ] as const;

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">FBTC Performance:</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 font-medium">Period</th>
                  <th className="text-right py-2 font-medium">FBTC</th>
                  <th className="text-right py-2 font-medium">ETF Database Category Average</th>
                  <th className="text-right py-2 font-medium">FactSet Segment Average</th>
                </tr>
              </thead>
              <tbody>
                {performancePeriods.map(({ key, label }) => {
                  const metric = fbtcPerformance.performanceMetrics[key];
                  return (
                    <tr key={key} className="border-b">
                      <td className="py-2">{label}</td>
                      <td className="text-right py-2 font-medium">{metric.fbtc}%</td>
                      <td className="text-right py-2">{metric.etfDatabaseCategoryAverage}%</td>
                      <td className="text-right py-2">{metric.factSetSegmentAverage}%</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit FBTC Performance</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          {performancePeriods.map(({ key, label }) => {
            const metric = formData.performanceMetrics[key];
            return (
              <div key={key} className="border p-4 rounded-lg">
                <h4 className="text-sm font-medium mb-3">{label} Performance</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">FBTC (%)</label>
                    <Input
                      type="number"
                      step="0.01"
                      value={metric.fbtc}
                      onChange={(e) => updatePerformanceMetric(key, 'fbtc', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">ETF Database Category Average (%)</label>
                    <Input
                      type="number"
                      step="0.01"
                      value={metric.etfDatabaseCategoryAverage}
                      onChange={(e) => updatePerformanceMetric(key, 'etfDatabaseCategoryAverage', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">FactSet Segment Average (%)</label>
                    <Input
                      type="number"
                      step="0.01"
                      value={metric.factSetSegmentAverage}
                      onChange={(e) => updatePerformanceMetric(key, 'factSetSegmentAverage', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 