import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { ProductDetails } from "../data";

interface CryptoETFOverviewProps {
  productDetails: ProductDetails;
  onSave: (productDetails: ProductDetails) => void;
}

export function CryptoETFOverview({ productDetails, onSave }: CryptoETFOverviewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<ProductDetails>(productDetails);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(productDetails);
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                {productDetails.fullName.charAt(0)}
              </div>
              <CardTitle className="text-xl font-bold">{productDetails.fullName}</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-500">Product Code</span>
              <p className="font-medium">{productDetails.productCode}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Yearly Return</span>
              <p className="font-medium">{productDetails.yearlyReturn}%</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Fund AUM</span>
              <p className="font-medium">{productDetails.fundAum}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Risk Profile</span>
              <p className="font-medium">{productDetails.riskProfile}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Rebalancing Frequency</span>
              <p className="font-medium">{productDetails.rebalancingFrequency}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Fund Inception Date</span>
              <p className="font-medium">{productDetails.fundInceptionDate.toLocaleDateString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-xl font-bold">Edit Product Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Product Code</label>
            <Input
              value={formData.productCode}
              onChange={(e) => setFormData({ ...formData, productCode: e.target.value })}
              placeholder="e.g., FBTC"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Full Name</label>
            <Input
              value={formData.fullName}
              onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
              placeholder="e.g., Fidelity Wise Origin Bitcoin Fund"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Logo URL</label>
            <Input
              value={formData.logo_url}
              onChange={(e) => setFormData({ ...formData, logo_url: e.target.value })}
              placeholder="e.g., https://example.com/logo.png"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Yearly Return (%)</label>
            <Input
              value={formData.yearlyReturn}
              onChange={(e) => setFormData({ ...formData, yearlyReturn: e.target.value })}
              placeholder="e.g., 20.00"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Fund AUM</label>
            <Input
              value={formData.fundAum}
              onChange={(e) => setFormData({ ...formData, fundAum: e.target.value })}
              placeholder="e.g., $1.5B"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Risk Profile</label>
            <select
              value={formData.riskProfile}
              onChange={(e) => setFormData({ ...formData, riskProfile: e.target.value })}
              className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select Risk Profile</option>
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
            </select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Rebalancing Frequency</label>
            <select
              value={formData.rebalancingFrequency}
              onChange={(e) => setFormData({ ...formData, rebalancingFrequency: e.target.value })}
              className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select Frequency</option>
              <option value="Daily">Daily</option>
              <option value="Weekly">Weekly</option>
              <option value="Monthly">Monthly</option>
              <option value="Quarterly">Quarterly</option>
              <option value="Annually">Annually</option>
            </select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Fund Inception Date</label>
            <Input
              type="date"
              value={formData.fundInceptionDate.toISOString().split('T')[0]}
              onChange={(e) => setFormData({ ...formData, fundInceptionDate: new Date(e.target.value) })}
            />
          </div>
        </div>
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 