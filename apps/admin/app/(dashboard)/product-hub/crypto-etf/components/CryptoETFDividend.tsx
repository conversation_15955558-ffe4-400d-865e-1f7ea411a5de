import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Info } from "lucide-react";
import { FbtcDividend } from "../data";

interface CryptoETFDividendProps {
  fbtcDividend: FbtcDividend;
  onSave: (fbtcDividend: FbtcDividend) => void;
}

export function CryptoETFDividend({ fbtcDividend, onSave }: CryptoETFDividendProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<FbtcDividend>(fbtcDividend);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(fbtcDividend);
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CardTitle className="text-lg font-semibold">FBTC Dividend</CardTitle>
              <Info className="w-4 h-4 text-gray-500" />
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-500">Dividend</span>
              <p className="font-medium">{fbtcDividend.dividend}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Dividend Date</span>
              <p className="font-medium">{fbtcDividend.dividendDate}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Annual Dividend Rate</span>
              <p className="font-medium">{fbtcDividend.annualDividendRate}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Annual Dividend Yield</span>
              <p className="font-medium">{fbtcDividend.annualDividendYield}</p>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t">
            <h4 className="text-sm font-medium mb-2">ETF Database Category Average</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-500">Dividend</span>
                <p className="font-medium">{fbtcDividend.etfDatabaseCategoryAverage.dividend}</p>
              </div>
              <div>
                <span className="text-sm text-gray-500">Annual Dividend Yield</span>
                <p className="font-medium">{fbtcDividend.etfDatabaseCategoryAverage.annualDividendYield}</p>
              </div>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t">
            <h4 className="text-sm font-medium mb-2">FactSet Segment Average</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-500">Dividend</span>
                <p className="font-medium">{fbtcDividend.factSetSegmentAverage.dividend}</p>
              </div>
              <div>
                <span className="text-sm text-gray-500">Annual Dividend Yield</span>
                <p className="font-medium">{fbtcDividend.factSetSegmentAverage.annualDividendYield}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit FBTC Dividend</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Dividend</label>
            <Input
              value={formData.dividend}
              onChange={(e) => setFormData({ ...formData, dividend: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Dividend Date</label>
            <Input
              value={formData.dividendDate}
              onChange={(e) => setFormData({ ...formData, dividendDate: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Annual Dividend Rate</label>
            <Input
              value={formData.annualDividendRate}
              onChange={(e) => setFormData({ ...formData, annualDividendRate: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Annual Dividend Yield</label>
            <Input
              value={formData.annualDividendYield}
              onChange={(e) => setFormData({ ...formData, annualDividendYield: e.target.value })}
            />
          </div>
        </div>
        
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium mb-3">ETF Database Category Average</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Dividend</label>
              <Input
                value={formData.etfDatabaseCategoryAverage.dividend}
                onChange={(e) => setFormData({
                  ...formData,
                  etfDatabaseCategoryAverage: { ...formData.etfDatabaseCategoryAverage, dividend: e.target.value }
                })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Dividend Date</label>
              <Input
                value={formData.etfDatabaseCategoryAverage.dividendDate}
                onChange={(e) => setFormData({
                  ...formData,
                  etfDatabaseCategoryAverage: { ...formData.etfDatabaseCategoryAverage, dividendDate: e.target.value }
                })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Annual Dividend Rate</label>
              <Input
                value={formData.etfDatabaseCategoryAverage.annualDividendRate}
                onChange={(e) => setFormData({
                  ...formData,
                  etfDatabaseCategoryAverage: { ...formData.etfDatabaseCategoryAverage, annualDividendRate: e.target.value }
                })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Annual Dividend Yield</label>
              <Input
                value={formData.etfDatabaseCategoryAverage.annualDividendYield}
                onChange={(e) => setFormData({
                  ...formData,
                  etfDatabaseCategoryAverage: { ...formData.etfDatabaseCategoryAverage, annualDividendYield: e.target.value }
                })}
              />
            </div>
          </div>
        </div>
        
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium mb-3">FactSet Segment Average</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Dividend</label>
              <Input
                value={formData.factSetSegmentAverage.dividend}
                onChange={(e) => setFormData({
                  ...formData,
                  factSetSegmentAverage: { ...formData.factSetSegmentAverage, dividend: e.target.value }
                })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Dividend Date</label>
              <Input
                value={formData.factSetSegmentAverage.dividendDate}
                onChange={(e) => setFormData({
                  ...formData,
                  factSetSegmentAverage: { ...formData.factSetSegmentAverage, dividendDate: e.target.value }
                })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Annual Dividend Rate</label>
              <Input
                value={formData.factSetSegmentAverage.annualDividendRate}
                onChange={(e) => setFormData({
                  ...formData,
                  factSetSegmentAverage: { ...formData.factSetSegmentAverage, annualDividendRate: e.target.value }
                })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Annual Dividend Yield</label>
              <Input
                value={formData.factSetSegmentAverage.annualDividendYield}
                onChange={(e) => setFormData({
                  ...formData,
                  factSetSegmentAverage: { ...formData.factSetSegmentAverage, annualDividendYield: e.target.value }
                })}
              />
            </div>
          </div>
        </div>
        
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 