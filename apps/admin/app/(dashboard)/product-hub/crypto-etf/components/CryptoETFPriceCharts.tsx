import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { PriceVolumeData } from "../data";

interface CryptoETFPriceChartsProps {
  priceVolumeCharts: {
    historicalData: PriceVolumeData[];
  };
  onSave: (priceVolumeCharts: { historicalData: PriceVolumeData[] }) => void;
}

export function CryptoETFPriceCharts({ priceVolumeCharts, onSave }: CryptoETFPriceChartsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<{ historicalData: PriceVolumeData[] }>(priceVolumeCharts);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(priceVolumeCharts);
    setIsEditing(false);
  };

  const addDataPoint = () => {
    const newDataPoint: PriceVolumeData = {
      date: new Date(),
      open: 0,
      high: 0,
      low: 0,
      close: 0,
      volume: 0,
    };
    setFormData({
      ...formData,
      historicalData: [...formData.historicalData, newDataPoint]
    });
  };

  const removeDataPoint = (index: number) => {
    setFormData({
      ...formData,
      historicalData: formData.historicalData.filter((_, i) => i !== index)
    });
  };

  const updateDataPoint = (index: number, field: keyof PriceVolumeData, value: string | number) => {
    const updatedData = [...formData.historicalData];
    if (field === 'date') {
      updatedData[index] = { ...updatedData[index], date: new Date(value as string) };
    } else {
      updatedData[index] = { ...updatedData[index], [field]: value };
    }
    setFormData({
      ...formData,
      historicalData: updatedData
    });
  };

  if (!isEditing) {
    return (
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Price Volume Charts:</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="text-gray-500 hover:text-gray-700"
            >
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 font-medium">Date</th>
                  <th className="text-right py-2 font-medium">Open</th>
                  <th className="text-right py-2 font-medium">High</th>
                  <th className="text-right py-2 font-medium">Low</th>
                  <th className="text-right py-2 font-medium">Close</th>
                  <th className="text-right py-2 font-medium">Volume</th>
                </tr>
              </thead>
              <tbody>
                {priceVolumeCharts.historicalData.map((data, index) => (
                  <tr key={index} className="border-b">
                    <td className="py-2">{data.date.toLocaleDateString()}</td>
                    <td className="text-right py-2">{data.open}</td>
                    <td className="text-right py-2">{data.high}</td>
                    <td className="text-right py-2">{data.low}</td>
                    <td className="text-right py-2">{data.close}</td>
                    <td className="text-right py-2">{data.volume.toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Edit Price Volume Charts</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          {formData.historicalData.map((data, index) => (
            <div key={index} className="border p-4 rounded-lg">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-sm font-medium">Data Point {index + 1}</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeDataPoint(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  Remove
                </Button>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Date</label>
                  <Input
                    type="date"
                    value={data.date.toISOString().split('T')[0]}
                    onChange={(e) => updateDataPoint(index, 'date', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Open</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.open}
                    onChange={(e) => updateDataPoint(index, 'open', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">High</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.high}
                    onChange={(e) => updateDataPoint(index, 'high', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Low</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.low}
                    onChange={(e) => updateDataPoint(index, 'low', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Close</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={data.close}
                    onChange={(e) => updateDataPoint(index, 'close', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Volume</label>
                  <Input
                    type="number"
                    value={data.volume}
                    onChange={(e) => updateDataPoint(index, 'volume', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <Button
          variant="outline"
          onClick={addDataPoint}
          className="w-full"
        >
          Add Data Point
        </Button>
        
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 