import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@admin/components/ui/card";
import { SimilarRecommendation } from '../data';

interface CryptoETFSimilarRecommendationsProps {
  similarRecommendations: SimilarRecommendation[];
}

export function CryptoETFSimilarRecommendations({ similarRecommendations }: CryptoETFSimilarRecommendationsProps) {
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Similar Recommendations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {similarRecommendations.map((recommendation, index) => (
            <Card key={index} className="bg-gray-50">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {recommendation.fundName.charAt(0)}
                  </div>
                  <div className="font-semibold">{recommendation.fundName}</div>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="text-gray-600">Yearly return:</span>
                    <span className="ml-1 font-semibold text-green-600">{recommendation.yearlyReturn}%</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Fund AUM:</span>
                    <span className="ml-1 font-semibold">{recommendation.fundAUM}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Risk Profile:</span>
                    <span className="ml-1 font-semibold">{recommendation.riskProfile}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Rebalancing:</span>
                    <span className="ml-1 font-semibold">{recommendation.rebalancingFrequency}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-600">Inception:</span>
                    <span className="ml-1 font-semibold">{recommendation.fundInceptionDate}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 