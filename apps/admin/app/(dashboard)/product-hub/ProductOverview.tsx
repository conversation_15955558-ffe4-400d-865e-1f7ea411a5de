"use client";
import React from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  BarChart3Icon, 
  PieChartIcon,
  UsersIcon,
  DollarSignIcon,
  ActivityIcon,
  TargetIcon
} from "lucide-react";
import { useProducts } from "@admin/app/lib/hooks/api-hooks";
import { Product } from "@admin/lib/productService";

export const ProductOverview = (): JSX.Element => {
  const { data: products = [], isLoading } = useProducts();

  // Calculate analytics from products data
  const totalProducts = products.length;
  const availableProducts = products.filter(p => p.availability === "Available").length;
  const limitedProducts = products.filter(p => p.availability === "Limited Availability").length;
  const unavailableProducts = products.filter(p => p.availability === "Unavailable").length;

  // Risk distribution
  const lowRiskProducts = products.filter(p => p.risk_level === "Low Risk").length;
  const mediumRiskProducts = products.filter(p => p.risk_level === "Medium Risk").length;
  const highRiskProducts = products.filter(p => p.risk_level === "High Risk").length;

  // Product type distribution
  const equityProducts = products.filter(p => 
    p.stock || p.fund || p.structured_product
  ).length;
  const creditProducts = products.filter(p => p.bond).length;

  const analyticsCards = [
    {
      title: "Total Products",
      value: totalProducts,
      change: "+12%",
      isPositive: true,
      icon: BarChart3Icon,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "Available Products",
      value: availableProducts,
      change: "+8%",
      isPositive: true,
      icon: TargetIcon,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      title: "Active Brokers",
      value: 8,
      change: "+2",
      isPositive: true,
      icon: UsersIcon,
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      title: "Market Coverage",
      value: "95%",
      change: "+3%",
      isPositive: true,
      icon: ActivityIcon,
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    }
  ];

  const distributionData = [
    {
      title: "Risk Distribution",
      data: [
        { label: "Low Risk", value: lowRiskProducts, color: "bg-green-500" },
        { label: "Medium Risk", value: mediumRiskProducts, color: "bg-yellow-500" },
        { label: "High Risk", value: highRiskProducts, color: "bg-red-500" }
      ],
      icon: PieChartIcon
    },
    {
      title: "Product Types",
      data: [
        { label: "Equity Based", value: equityProducts, color: "bg-blue-500" },
        { label: "Credit Based", value: creditProducts, color: "bg-purple-500" }
      ],
      icon: BarChart3Icon
    },
    {
      title: "Availability Status",
      data: [
        { label: "Available", value: availableProducts, color: "bg-green-500" },
        { label: "Limited", value: limitedProducts, color: "bg-yellow-500" },
        { label: "Unavailable", value: unavailableProducts, color: "bg-red-500" }
      ],
      icon: TargetIcon
    }
  ];

  return (
    <div className="w-full h-full overflow-y-auto">
      {/* Header */}
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Product Overview</h1>
            <p className="text-gray-600 text-sm">
              Comprehensive analytics and insights for your product portfolio
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="text-sm">
              Export Report
            </Button>
            <Button className="admin_green_gradient hover:admin_green_gradient_hover text-white">
              Generate Insights
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Analytics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {analyticsCards.map((card, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                  <div className="flex items-center mt-2">
                    {card.isPositive ? (
                      <TrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
                    ) : (
                      <TrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm ${card.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                      {card.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg ${card.bgColor}`}>
                  <card.icon className={`w-6 h-6 ${card.color}`} />
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Distribution Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {distributionData.map((section, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center mb-4">
                <section.icon className="w-5 h-5 text-gray-600 mr-2" />
                <h3 className="font-semibold text-gray-900">{section.title}</h3>
              </div>
              <div className="space-y-3">
                {section.data.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full ${item.color} mr-3`}></div>
                      <span className="text-sm text-gray-600">{item.label}</span>
                    </div>
                    <span className="font-semibold text-gray-900">{item.value}</span>
                  </div>
                ))}
              </div>
            </Card>
          ))}
        </div>

        {/* Recent Activity */}
        <Card className="p-6">
          <h3 className="font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {[
              { action: "New product added", product: "AAPL - Apple Inc.", time: "2 hours ago", type: "add" },
              { action: "Product updated", product: "TSLA - Tesla Inc.", time: "4 hours ago", type: "update" },
              { action: "Availability changed", product: "NVDA - NVIDIA Corp.", time: "6 hours ago", type: "change" },
              { action: "New broker added", product: "Goldman Sachs", time: "1 day ago", type: "broker" }
            ].map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-xs text-gray-600">{activity.product}</p>
                  </div>
                </div>
                <span className="text-xs text-gray-500">{activity.time}</span>
              </div>
            ))}
          </div>
        </Card>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Top Performing Products</h3>
            <div className="space-y-3">
              {[
                { symbol: "NVDA", name: "NVIDIA Corp", performance: "+15.2%", volume: "34.1M" },
                { symbol: "AAPL", name: "Apple Inc", performance: "+8.7%", volume: "52.3M" },
                { symbol: "MSFT", name: "Microsoft Corp", performance: "+6.3%", volume: "28.9M" }
              ].map((product, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{product.symbol}</p>
                    <p className="text-sm text-gray-600">{product.name}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-green-600 font-medium">{product.performance}</p>
                    <p className="text-xs text-gray-500">Vol: {product.volume}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Market Trends</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Technology Sector</span>
                <div className="flex items-center">
                  <TrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-green-600 font-medium">+12.5%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Healthcare Sector</span>
                <div className="flex items-center">
                  <TrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-green-600 font-medium">+8.2%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Finance Sector</span>
                <div className="flex items-center">
                  <TrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
                  <span className="text-red-600 font-medium">-2.1%</span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}; 