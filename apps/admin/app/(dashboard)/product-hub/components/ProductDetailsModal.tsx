import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { Button } from "@admin/components/ui/button";
import { XIcon, ExternalLinkIcon, DollarSignIcon, TrendingUpIcon, CalendarIcon, UsersIcon, ShieldIcon, DownloadIcon } from "lucide-react";
import { Product as TableProduct } from "../types";
import { Product as DbProduct } from "@admin/lib/productService";

interface ProductDetailsModalProps {
  product: TableProduct | null;
  dbProduct?: DbProduct | null;
  onClose: () => void;
}

export const ProductDetailsModal: React.FC<ProductDetailsModalProps> = ({
  product,
  dbProduct,
  onClose,
}) => {
  const [showFullDetails, setShowFullDetails] = useState(false);
  
  if (!product) return null;

  const handleViewFullDetails = () => {
    setShowFullDetails(true);
  };

  const handleBackToSummary = () => {
    setShowFullDetails(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {showFullDetails ? "Complete Product Details" : "Product Details"}
            </h2>
            <p className="text-gray-600 text-sm">
              {showFullDetails ? "All database information for" : "Complete information about"} {product.name}
            </p>
          </div>
          <div className="flex items-center gap-2">
            {showFullDetails && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleBackToSummary}
                className="flex items-center gap-2"
              >
                <XIcon className="h-4 w-4" />
                Back to Summary
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {!showFullDetails ? (
            // Summary View
            <>
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-gray-900">{product.symbol}</span>
                    <Badge className="bg-blue-100 text-blue-800">Active</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.name}</h3>
                      <p className="text-gray-600 text-sm">Product Name</p>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.sector}</h3>
                      <p className="text-gray-600 text-sm">Sector</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Financial Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Financial Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">{product.price}</div>
                      <div className="text-sm text-gray-600">Current Price</div>
                      <div className={`text-sm font-medium ${product.isPositive ? "text-green-600" : "text-red-600"}`}>
                        {product.change}
                      </div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-lg font-bold text-gray-900">{product.volume}</div>
                      <div className="text-sm text-gray-600">Volume</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-lg font-bold text-gray-900">{product.marketCap}</div>
                      <div className="text-sm text-gray-600">Market Cap</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Risk & Availability */}
              <Card>
                <CardHeader>
                  <CardTitle>Risk & Availability</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Risk Level</h4>
                      <Badge className={`${product.riskColor} text-sm`}>
                        {product.risk}
                      </Badge>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Availability</h4>
                      <Badge className="bg-[#EDF8F2] text-[#05A049] text-sm">
                        {product.availability}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Brokers */}
              <Card>
                <CardHeader>
                  <CardTitle>Available Brokers</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {product.brokers.map((broker: string, idx: number) => (
                      <Badge
                        key={idx}
                        variant="outline"
                        className="text-sm bg-gray-50"
                      >
                        {broker}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Additional Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Additional Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Product Type</h4>
                      <p className="text-gray-600">Based on symbol and sector analysis</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Last Updated</h4>
                      <p className="text-gray-600">Real-time data</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Trading Status</h4>
                      <p className="text-gray-600">Active Trading</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Data Source</h4>
                      <p className="text-gray-600">Market Data Provider</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button variant="outline" onClick={onClose}>
                  Close
                </Button>
                <Button 
                  className="admin_green_gradient hover:admin_green_gradient_hover text-white"
                  onClick={handleViewFullDetails}
                >
                  <ExternalLinkIcon className="h-4 w-4 mr-2" />
                  View Full Details
                </Button>
              </div>
            </>
          ) : (
            // Full Details View
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>Basic Information</span>
                      <Badge variant="outline">
                        {dbProduct?.type || "Unknown"}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {dbProduct?.stock?.symbol || dbProduct?.bond?.symbol || dbProduct?.fund?.symbol || dbProduct?.structured_product?.symbol || product.symbol}
                        </h3>
                        <p className="text-gray-600 text-sm">Symbol</p>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{dbProduct?.name || product.name}</h3>
                        <p className="text-gray-600 text-sm">Product Name</p>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{dbProduct?.product_type || "N/A"}</h3>
                        <p className="text-gray-600 text-sm">Product Type</p>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{dbProduct?.currency || "N/A"}</h3>
                        <p className="text-gray-600 text-sm">Currency</p>
                      </div>
                      {dbProduct?.description && (
                        <div className="md:col-span-2">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                          <p className="text-gray-600">{dbProduct.description}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Financial Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <DollarSignIcon className="h-5 w-5" />
                      <span>Financial Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {dbProduct?.minimum_investment && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">
                            ${dbProduct.minimum_investment.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600">Minimum Investment</div>
                        </div>
                      )}
                      
                      {/* Stock specific data */}
                      {dbProduct?.stock && (
                        <>
                          {dbProduct.stock.current_price && (
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <div className="text-2xl font-bold text-gray-900">
                                ${dbProduct.stock.current_price}
                              </div>
                              <div className="text-sm text-gray-600">Current Price</div>
                            </div>
                          )}
                          {dbProduct.stock.market_cap && (
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <div className="text-lg font-bold text-gray-900">
                                ${dbProduct.stock.market_cap.toLocaleString()}
                              </div>
                              <div className="text-sm text-gray-600">Market Cap</div>
                            </div>
                          )}
                        </>
                      )}

                      {/* Bond specific data */}
                      {dbProduct?.bond && (
                        <>
                          {dbProduct.bond.current_price && (
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <div className="text-2xl font-bold text-gray-900">
                                ${dbProduct.bond.current_price}
                              </div>
                              <div className="text-sm text-gray-600">Current Price</div>
                            </div>
                          )}
                          {dbProduct.bond.yield && (
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <div className="text-2xl font-bold text-gray-900">
                                {dbProduct.bond.yield}%
                              </div>
                              <div className="text-sm text-gray-600">Yield</div>
                            </div>
                          )}
                          {dbProduct.bond.coupon_rate && (
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <div className="text-2xl font-bold text-gray-900">
                                {dbProduct.bond.coupon_rate}%
                              </div>
                              <div className="text-sm text-gray-600">Coupon Rate</div>
                            </div>
                          )}
                        </>
                      )}

                      {/* Fund specific data */}
                      {dbProduct?.fund && (
                        <>
                          {dbProduct.fund.nav && (
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <div className="text-2xl font-bold text-gray-900">
                                ${dbProduct.fund.nav}
                              </div>
                              <div className="text-sm text-gray-600">NAV</div>
                            </div>
                          )}
                          {dbProduct.fund.aum && (
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <div className="text-lg font-bold text-gray-900">
                                ${dbProduct.fund.aum.toLocaleString()}
                              </div>
                              <div className="text-sm text-gray-600">AUM</div>
                            </div>
                          )}
                          {dbProduct.fund.expense_ratio && (
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <div className="text-2xl font-bold text-gray-900">
                                {dbProduct.fund.expense_ratio}%
                              </div>
                              <div className="text-sm text-gray-600">Expense Ratio</div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Product Specific Details */}
                {(dbProduct?.stock || dbProduct?.bond || dbProduct?.fund || dbProduct?.structured_product) && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <TrendingUpIcon className="h-5 w-5" />
                        <span>Product Specific Details</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {dbProduct?.stock && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {dbProduct.stock.sector && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Sector</h4>
                              <p className="text-gray-600">{dbProduct.stock.sector}</p>
                            </div>
                          )}
                          {dbProduct.stock.risk_metrics && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Risk Metrics</h4>
                              <pre className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                                {JSON.stringify(dbProduct.stock.risk_metrics, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      )}

                      {dbProduct?.bond && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {dbProduct.bond.credit_rating && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Credit Rating</h4>
                              <Badge variant="outline">{dbProduct.bond.credit_rating}</Badge>
                            </div>
                          )}
                          {dbProduct.bond.maturity_date && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Maturity Date</h4>
                              <p className="text-gray-600">{new Date(dbProduct.bond.maturity_date).toLocaleDateString()}</p>
                            </div>
                          )}
                          {dbProduct.bond.isin_code && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">ISIN Code</h4>
                              <p className="text-gray-600">{dbProduct.bond.isin_code}</p>
                            </div>
                          )}
                        </div>
                      )}

                      {dbProduct?.fund && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-1">Fund Type</h4>
                            <p className="text-gray-600">{dbProduct.fund.fund_type}</p>
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-1">Trading Type</h4>
                            <div className="flex space-x-2">
                              {dbProduct.fund.exchange_traded && (
                                <Badge variant="outline">Exchange Traded</Badge>
                              )}
                              {dbProduct.fund.privately_held && (
                                <Badge variant="outline">Privately Held</Badge>
                              )}
                            </div>
                          </div>
                          {dbProduct.fund.dividend_yield && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Dividend Yield</h4>
                              <p className="text-gray-600">{dbProduct.fund.dividend_yield}%</p>
                            </div>
                          )}
                          {dbProduct.fund.performance_metrics && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Performance Metrics</h4>
                              <pre className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                                {JSON.stringify(dbProduct.fund.performance_metrics, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      )}

                      {dbProduct?.structured_product && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {dbProduct.structured_product.strike_price && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Strike Price</h4>
                              <p className="text-gray-600">${dbProduct.structured_product.strike_price}</p>
                            </div>
                          )}
                          {dbProduct.structured_product.return_type && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Return Type</h4>
                              <p className="text-gray-600">{dbProduct.structured_product.return_type}</p>
                            </div>
                          )}
                          {dbProduct.structured_product.maturity_date && (
                            <div>
                              <h4 className="font-semibold text-gray-900 mb-1">Maturity Date</h4>
                              <p className="text-gray-600">{new Date(dbProduct.structured_product.maturity_date).toLocaleDateString()}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Vendor & Issuer Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <UsersIcon className="h-5 w-5" />
                      <span>Vendor & Issuer</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {dbProduct?.vendor && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">Vendor</h4>
                        <p className="text-gray-600">{dbProduct.vendor.name}</p>
                        <Badge variant="outline" className="mt-1">{dbProduct.vendor.type}</Badge>
                      </div>
                    )}
                    {dbProduct?.issuer && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">Issuer</h4>
                        <p className="text-gray-600">{dbProduct.issuer.name}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Timestamps */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <CalendarIcon className="h-5 w-5" />
                      <span>Timestamps</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Created</h4>
                      <p className="text-gray-600 text-sm">
                        {dbProduct?.created_at ? new Date(dbProduct.created_at).toLocaleString() : "N/A"}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">Last Updated</h4>
                      <p className="text-gray-600 text-sm">
                        {dbProduct?.updated_at ? new Date(dbProduct.updated_at).toLocaleString() : "N/A"}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle>Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button className="w-full admin_green_gradient hover:admin_green_gradient_hover text-white">
                      <ExternalLinkIcon className="h-4 w-4 mr-2" />
                      View Market Data
                    </Button>
                    <Button variant="outline" className="w-full">
                      <DownloadIcon className="h-4 w-4 mr-2" />
                      Download Fact Sheet
                    </Button>
                    <Button variant="outline" className="w-full">
                      <ShieldIcon className="h-4 w-4 mr-2" />
                      View Compliance
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 