import React, { useState, useMemo } from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { EyeIcon, PencilIcon, ArchiveIcon, SearchIcon, FilterIcon } from "lucide-react";
import { Product as TableProduct, ProductCategory } from "../types";
import { Product as DbProduct } from "@admin/lib/productService";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@admin/components/ui/table";

interface ProductTableProps {
  products: TableProduct[];
  dbProducts?: DbProduct[];
  onView?: (product: TableProduct) => void;
  onEdit?: (product: TableProduct) => void;
  onArchive?: (product: TableProduct) => void;
  onCategoryChange?: (category: string) => void;
}

export const ProductTable: React.FC<ProductTableProps> = ({ 
  products, 
  dbProducts = [],
  onView, 
  onEdit, 
  onArchive,
  onCategoryChange
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState("All Types");
  const [activeCategory, setActiveCategory] = useState("Stock");

  // Function to get product type from database product using the 'type' field
  const getProductTypeFromDb = (dbProduct: DbProduct) => {
    // Use the 'type' field which contains the ProductType enum value
    return dbProduct.type;
  };

  // Function to get product type from table product (fallback)
  const getProductType = (product: TableProduct) => {
    if (product.sector !== "N/A") return "Stock";
    if (product.name.toLowerCase().includes("bond")) return "Bond";
    if (product.name.toLowerCase().includes("etf")) return "ETFs";
    if (product.name.toLowerCase().includes("crypto")) return "CryptoETF";
    if (product.name.toLowerCase().includes("mutual")) return "MutualFunds";
    if (product.name.toLowerCase().includes("private")) return "Fund";
    if (product.name.toLowerCase().includes("reit")) return "Riets";
    if (product.name.toLowerCase().includes("structured")) return "Structured";
    return "Stock";
  };

  // Generate product categories with counts using exact ProductType enum values
  const productCategories = useMemo(() => {
    const categoryCounts: { [key: string]: number } = {
      "Stock": 0,
      "Bond": 0,
      "Equity": 0,
      "CryptoETF": 0,
      "CryptoFunds": 0,
      "Fund": 0,
      "Structured": 0,
      "MutualFunds": 0,
      "ETFs": 0,
      "Riets": 0
    };

    // Count products by type using database products
    dbProducts.forEach(dbProduct => {
      const productType = getProductTypeFromDb(dbProduct);
      if (categoryCounts.hasOwnProperty(productType)) {
        categoryCounts[productType]++;
      }
    });

    return Object.entries(categoryCounts).map(([name, count]) => ({
      name,
      count: count.toString(),
      isActive: name === activeCategory
    }));
  }, [dbProducts, activeCategory]);

  // Get unique product types from database products
  const productTypes = useMemo(() => {
    const types = new Set<string>();
    dbProducts.forEach(product => {
      const productType = getProductTypeFromDb(product);
      types.add(productType);
    });
    return ["All Types", ...Array.from(types)];
  }, [dbProducts]);

  // Filter products based on search term and type
  const filteredProducts = useMemo(() => {
    return products.filter((product, index) => {
      const matchesSearch = !searchTerm || 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.symbol.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Use database product type for filtering
      const dbProduct = dbProducts[index];
      const productType = dbProduct ? getProductTypeFromDb(dbProduct) : getProductType(product);
      const matchesType = selectedType === "All Types" || productType === selectedType;

      return matchesSearch && matchesType;
    });
  }, [products, dbProducts, searchTerm, selectedType]);

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    setSelectedType(category === "All Types" ? "All Types" : category);
    onCategoryChange?.(category);
  };

  return (
    <Card className="bg-white mb-6 rounded-xl">
      <CardContent className="p-6">
        {/* Product Categories Navigation */}
        <div className="flex gap-6 mb-6 border-b overflow-x-auto">
          {productCategories.map((category, index) => (
            <button
              key={index}
              className={`pb-3 px-1 border-b-2 transition-colors whitespace-nowrap ${
                category.name === activeCategory
                  ? "border-[#05A049] text-[#05A049]"
                  : "border-transparent text-gray-600 hover:text-gray-900"
              }`}
              onClick={() => handleCategoryChange(category.name)}
            >
              <span className="font-medium">{category.name}</span>
              <span className="ml-2 text-sm bg-gray-100 px-2 py-1 rounded-full">
                {category.count}
              </span>
            </button>
          ))}
        </div>

        {/* Search and Filter Bar */}
        <div className="flex flex-col lg:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search products by name or symbol..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <div className="relative">
              <FilterIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg text-sm bg-white appearance-none"
              >
                {productTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            <div className="text-sm text-gray-500 flex items-center">
              {filteredProducts.length} products found
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-gray-50 border-b rounded-t-xl">
              <TableRow className="rounded-t-xl">
                <TableHead className="text-left p-4 text-sm font-medium text-gray-700 rounded-tl-xl">
                  SYMBOL & NAME
                </TableHead>
                <TableHead className="text-left p-4 text-sm font-medium text-gray-700">
                  PRICE & CHANGE
                </TableHead>
                <TableHead className="text-left p-4 text-sm font-medium text-gray-700">
                  VOLUME & MARKET CAP
                </TableHead>
                <TableHead className="text-left p-4 text-sm font-medium text-gray-700">
                  PRODUCT TYPE
                </TableHead>
                <TableHead className="text-left p-4 text-sm font-medium text-gray-700">
                  SECTOR & RISK
                </TableHead>
                <TableHead className="text-left p-4 text-sm font-medium text-gray-700">
                  AVAILABILITY
                </TableHead>
                <TableHead className="text-left p-4 text-sm font-medium text-gray-700">
                  BROKERS
                </TableHead>
                <TableHead className="text-left p-4 text-sm font-medium text-gray-700 rounded-tr-xl">
                  ACTION
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product, index) => {
                const dbProduct = dbProducts[index];
                const productType = dbProduct ? getProductTypeFromDb(dbProduct) : getProductType(product);
                
                return (
                  <TableRow
                    key={product.symbol}
                    className="border-b hover:bg-gray-50"
                  >
                    <TableCell className="p-4">
                      <div>
                        <span className="font-bold text-gray-900 text-lg">
                          {product.symbol}
                        </span>
                        <p className="text-sm text-gray-600">{product.name}</p>
                      </div>
                    </TableCell>
                    <TableCell className="p-4">
                      <div>
                        <span className="font-bold text-gray-900 text-lg">
                          {product.price}
                        </span>
                        <p
                          className={`text-sm ${product.isPositive ? "text-[#05A049]" : "text-red-600"}`}
                        >
                          {product.change}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell className="p-4">
                      <div>
                        <p className="text-sm text-gray-900">{product.volume}</p>
                        <p className="text-sm text-gray-600">
                          {product.marketCap}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell className="p-4">
                      <Badge 
                        variant="outline" 
                        className="text-xs rounded-full bg-blue-50 text-blue-700 border-blue-200"
                      >
                        {productType}
                      </Badge>
                    </TableCell>
                    <TableCell className="p-4">
                      <div>
                        <p className="text-sm text-gray-900 mb-1">
                          {product.sector}
                        </p>
                        <Badge
                          className={`${product.riskColor} text-xs rounded-full`}
                        >
                          {product.risk}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="p-4">
                      <Badge className="bg-[#EDF8F2] text-[#05A049] text-xs rounded-full">
                        {product.availability}
                      </Badge>
                    </TableCell>
                    <TableCell className="p-4">
                      <div className="flex gap-1">
                        {product.brokers.map((broker: string, idx: number) => (
                          <Badge
                            key={idx}
                            variant="outline"
                            className="text-xs rounded-full bg-gray-50"
                          >
                            {broker}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell className="p-4">
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onView?.(product)}
                          className="h-8 w-8 p-0 hover:bg-blue-50"
                          title="View Product"
                        >
                          <EyeIcon className="h-4 w-4 text-blue-600" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit?.(product)}
                          className="h-8 w-8 p-0 hover:bg-green-50"
                          title="Edit Product"
                        >
                          <PencilIcon className="h-4 w-4 text-green-600" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onArchive?.(product)}
                          className="h-8 w-8 p-0 hover:bg-orange-50"
                          title="Archive Product"
                        >
                          <ArchiveIcon className="h-4 w-4 text-orange-600" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};
