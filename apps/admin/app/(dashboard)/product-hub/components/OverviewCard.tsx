import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { OverviewCard as OverviewCardType } from "../types";
import {
  Table,
  ShieldIcon,
  CircleCheckIcon,
  TrendingUpIcon,
} from "lucide-react";

interface OverviewCardProps {
  card: OverviewCardType;
}

const getIcon = (iconName: string) => {
  switch (iconName) {
    case "Table":
      return <Table className="w-6 h-6" />;
    case "Shield":
      return <ShieldIcon className="w-6 h-6" />;
    case "CircleCheck":
      return <CircleCheckIcon className="w-6 h-6" />;
    case "TrendingUp":
      return <TrendingUpIcon className="w-6 h-6" />;
    default:
      return null;
  }
};

export const OverviewCard: React.FC<OverviewCardProps> = ({ card }) => {
  return (
    <Card className="bg-white">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div
            className={`w-12 h-12 ${card.iconBgColor} rounded-[8px] flex items-center justify-center`}
          >
            {getIcon(card.icon)}
          </div>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {card.title}
        </h3>
        <p className="text-sm text-gray-600 mb-4">{card.description}</p>
        <div>
          {card.details.map((detail, detailIndex) => {
            if (detail.type === "keyValue" && detail.label) {
              return (
                <div
                  key={detailIndex}
                  className="flex justify-between items-center text-sm mb-1"
                >
                  <span className="text-gray-700">{detail.label}:</span>
                  <span className="font-semibold text-gray-900">
                    {detail.value}
                  </span>
                </div>
              );
            } else if (detail.type === "text") {
              return (
                <div
                  key={detailIndex}
                  className="flex justify-between items-center text-sm mb-1"
                >
                  <span className="text-gray-700">{detail.value}</span>
                </div>
              );
            }
            return null;
          })}
        </div>
      </CardContent>
    </Card>
  );
};
