import React from "react";
import { ProductCategory } from "../types";

interface ProductCategoriesProps {
  categories: ProductCategory[];
  activeCategory: string;
  onCategoryChange: (category: string) => void;
}

export const ProductCategories: React.FC<ProductCategoriesProps> = ({
  categories,
  activeCategory,
  onCategoryChange,
}) => {
  return (
    <div className="flex gap-6 mb-6 border-b">
      {categories.map((category, index) => (
        <button
          key={index}
          className={`pb-3 px-1 border-b-2 transition-colors ${
            category.name === activeCategory
              ? "border-[#05A049] text-[#05A049]"
              : "border-transparent text-gray-600 hover:text-gray-900"
          }`}
          onClick={() => onCategoryChange(category.name)}
        >
          <span className="font-medium">{category.name}</span>
          <span className="ml-2 text-sm bg-gray-100 px-2 py-1 rounded-full">
            {category.count}
          </span>
        </button>
      ))}
    </div>
  );
};
