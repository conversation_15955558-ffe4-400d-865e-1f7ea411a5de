"use client";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { ArrowLeft, Download, MessageCircle, Eye, Send } from "lucide-react";
import StructuredProductSummary from "./components/StructuredProductSummary";
import ProductSuitability from "./components/ProductSuitability";
import ProductKeyRisks from "./components/ProductKeyRisks";
import UnderlyingAssets from "./components/UnderlyingAssets";
import PayoffChart from "./components/PayoffChart";
import UnderlyingPerformanceChart from "./components/UnderlyingPerformanceChart";
import ProductScenarios from "./components/ProductScenarios";
import ProductCoupons from "./components/ProductCoupons";
import EarlyRedemptionTable from "./components/EarlyRedemptionTable";
import ProductRedemption from "./components/ProductRedemption";
import GeneralInformation from "./components/GeneralInformation";
import Recommendations from "./components/Recommendations";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { createStructuredProduct } from '@admin/app/lib/productApiService';

export default function StructuredProductDetailPage() {
  const [mode, setMode] = useState<'form' | 'review'>('form');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const searchParams = useSearchParams();
  const [vendor_id, setVendorId] = useState("");

  useEffect(() => {
    const vendorParam = searchParams.get('vendor');
    if (vendorParam) {
      setVendorId(vendorParam);
      setFormData(prev => ({
        ...prev,
        vendor_id: vendorParam
      }));
    }
  }, [searchParams]);

  const router = useRouter();
  const [expandedSections, setExpandedSections] = useState({
    productSummary: true,
    productDetails: true,
    keyFeatures: false,
    suitabilityQuestions: false,
    keyRisks: false,
    underlyingAssets: true,
    scenarios: false,
    coupons: false,
    earlyRedemption: false,
    redemptionFormulas: false,
    fixingLevels: false,
    generalInformation: true,
    actions: false,
    payoffChartData: false
  });
  
  // Form state based on API controller fields
  const [formData, setFormData] = useState({    
    // Product Summary
    productSummary: {
      productType: "",
      yield: {
        value: "",
        unit: "P.A.",
        isLive: true
      },
      minimumInvestment: {
        amount: "",
        currency: "USD"
      },
      barrier: "",
      issuer: "",
      duration: ""
    },
    
    // Product Details
    productDetails: {
      isin: "",
      valor: "",
      issuePrice: "",
      initialSize: "",
      denomination: "",
      productType: ""
    },
    
    // Key Features
    keyFeatures: [""],
    
    // Suitability Questions
    suitabilityQuestions: [""],
    
    // Key Risks
    keyRisks: [""],
    
    // Underlying Assets
    underlyingAssets: [{
      name: "",
      symbol: "",
      relatedExchange: "",
      bloombergTicker: "",
      referenceCurrency: "USD",
      initialFixingLevel: "",
      strikeLevel: ""
    }],
    
    // Payoff Chart Data
    payoffChartData: {
      payoffAtMaturity: [{ x: 0, payoff: 0, lossLimit: 0, barrier: 0 }],
      underlyingPerformances: [{ x: 0, payoff: 0, lossLimit: 0, barrier: 0 }]
    },
    
    // Scenarios
    scenarios: {
      quarterlyObservation: {
        guaranteedCoupon: {
          coupon: ""
        }
      },
      atMaturity: {
        aboveBarrier: {
          condition: "",
          result: ""
        },
        belowBarrier: {
          condition: "",
          result: ""
        }
      }
    },
    
    // Coupons
    coupons: {
      couponRate: "",
      nextCoupon: "",
      couponSchedule: [{ n: 1, couponRate: "", couponPaymentDate: "" }]
    },
    
    // Early Redemption
    earlyRedemption: {
      automaticEarlyRedemptionEvent: false,
      earlyRedemptionAmountAndDates: [{ n: 1, autocallLevel: "", earlyRedemptionAmount: "", earlyRedemptionObservationDate: "", earlyRedemptionDate: "" }],
      notes: [""]
    },
    
    // Redemption Formulas
    redemptionFormulas: {
      scenario1: {
        number: "1",
        condition: "",
        formula: ""
      },
      scenario2: {
        number: "2", 
        condition: "",
        formula: ""
      }
    },
    
    // Fixing Levels
    fixingLevels: {
      initialFixingLevel: "",
      relevantFixingLevel: "",
      finalFixingLevel: "",
      performance: "",
      worstPerformance: ""
    },
    
    // General Information
    generalInformation: {
      issuer: {
        name: "",
        address: ""
      },
      issuerRating: "",
      supervisionOfIssuer: "",
      calculationAgent: "",
      fiscalTransferAndPayingAgents: ""
    },
    
    // Actions
    actions: {
      KID_available: false,
      KID_doc_link: "",
      contactAdvisor_available: false,
      contactAdvisor_phone: ""
    }
  });

  const updateFormData = (section: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...(prev[section as keyof typeof prev] as any),
        [field]: value
      }
    }));
  };

  const updateNestedFormData = (section: string, subsection: string, field: string, value: any) => {
    if (field === '') {
      // For simple nested objects where subsection is the field name
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...(prev[section as keyof typeof prev] as any),
          [subsection]: value
        }
      }));
    } else {
      // For complex nested objects with further nesting
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...(prev[section as keyof typeof prev] as any),
          [subsection]: {
            ...(prev[section as keyof typeof prev] as any)[subsection],
            [field]: value
          }
        }
      }));
    }
  };

  const addArrayItem = (section: string, newItem: any) => {
    if (section === 'coupons') {
      setFormData(prev => ({
        ...prev,
        coupons: {
          ...prev.coupons,
          couponSchedule: [...prev.coupons.couponSchedule, newItem]
        }
      }));
    } else if (section === 'earlyRedemption') {
      setFormData(prev => ({
        ...prev,
        earlyRedemption: {
          ...prev.earlyRedemption,
          earlyRedemptionAmountAndDates: [...prev.earlyRedemption.earlyRedemptionAmountAndDates, newItem]
        }
      }));
    } else if (section === 'payoffChartData') {
      setFormData(prev => ({
        ...prev,
        payoffChartData: {
          ...prev.payoffChartData,
          payoffAtMaturity: [...prev.payoffChartData.payoffAtMaturity, newItem]
        }
      }));
    } else if (section === 'underlyingPerformances') {
      setFormData(prev => ({
        ...prev,
        payoffChartData: {
          ...prev.payoffChartData,
          underlyingPerformances: [...prev.payoffChartData.underlyingPerformances, newItem]
        }
      }));
    } else if (section === 'earlyRedemptionNotes') {
      setFormData(prev => ({
        ...prev,
        earlyRedemption: {
          ...prev.earlyRedemption,
          notes: [...prev.earlyRedemption.notes, newItem]
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [section]: [...(prev[section as keyof typeof prev] as any[]), newItem]
      }));
    }
  };

  const removeArrayItem = (section: string, index: number) => {
    if (section === 'coupons') {
      setFormData(prev => ({
        ...prev,
        coupons: {
          ...prev.coupons,
          couponSchedule: prev.coupons.couponSchedule.filter((_, i) => i !== index)
        }
      }));
    } else if (section === 'earlyRedemption') {
      setFormData(prev => ({
        ...prev,
        earlyRedemption: {
          ...prev.earlyRedemption,
          earlyRedemptionAmountAndDates: prev.earlyRedemption.earlyRedemptionAmountAndDates.filter((_, i) => i !== index)
        }
      }));
    } else if (section === 'payoffChartData') {
      setFormData(prev => ({
        ...prev,
        payoffChartData: {
          ...prev.payoffChartData,
          payoffAtMaturity: prev.payoffChartData.payoffAtMaturity.filter((_, i) => i !== index)
        }
      }));
    } else if (section === 'underlyingPerformances') {
      setFormData(prev => ({
        ...prev,
        payoffChartData: {
          ...prev.payoffChartData,
          underlyingPerformances: prev.payoffChartData.underlyingPerformances.filter((_, i) => i !== index)
        }
      }));
    } else if (section === 'earlyRedemptionNotes') {
      setFormData(prev => ({
        ...prev,
        earlyRedemption: {
          ...prev.earlyRedemption,
          notes: prev.earlyRedemption.notes.filter((_, i) => i !== index)
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [section]: (prev[section as keyof typeof prev] as any[]).filter((_, i) => i !== index)
      }));
    }
  };

  const updateArrayItem = (section: string, index: number, field: string, value: any) => {
    if (section === 'coupons') {
      setFormData(prev => ({
        ...prev,
        coupons: {
          ...prev.coupons,
          couponSchedule: prev.coupons.couponSchedule.map((item, i) => 
            i === index ? { ...item, [field]: value } : item
          )
        }
      }));
    } else if (section === 'earlyRedemption') {
      setFormData(prev => ({
        ...prev,
        earlyRedemption: {
          ...prev.earlyRedemption,
          earlyRedemptionAmountAndDates: prev.earlyRedemption.earlyRedemptionAmountAndDates.map((item, i) => 
            i === index ? { ...item, [field]: value } : item
          )
        }
      }));
    } else if (section === 'payoffChartData') {
      setFormData(prev => ({
        ...prev,
        payoffChartData: {
          ...prev.payoffChartData,
          payoffAtMaturity: prev.payoffChartData.payoffAtMaturity.map((item, i) => 
            i === index ? { ...item, [field]: value } : item
          )
        }
      }));
    } else if (section === 'underlyingPerformances') {
      setFormData(prev => ({
        ...prev,
        payoffChartData: {
          ...prev.payoffChartData,
          underlyingPerformances: prev.payoffChartData.underlyingPerformances.map((item, i) => 
            i === index ? { ...item, [field]: value } : item
          )
        }
      }));
    } else if (section === 'earlyRedemptionNotes') {
      setFormData(prev => ({
        ...prev,
        earlyRedemption: {
          ...prev.earlyRedemption,
          notes: prev.earlyRedemption.notes.map((item, i) => 
            i === index ? value : item
          )
        }
      }));
    } else {
      // Handle simple string arrays (keyFeatures, suitabilityQuestions, keyRisks)
      if (field === '') {
        setFormData(prev => ({
          ...prev,
          [section]: (prev[section as keyof typeof prev] as any[]).map((item, i) => 
            i === index ? value : item
          )
        }));
      } else {
        // Handle object arrays
        setFormData(prev => ({
          ...prev,
          [section]: (prev[section as keyof typeof prev] as any[]).map((item, i) => 
            i === index ? { ...item, [field]: value } : item
          )
        }));
      }
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const response: any = await createStructuredProduct(formData);

      if (response && response.statusCode === 201) {
        alert('Structured product created successfully!');
        setMode('form');
        router.push('/product-hub');
      } else if (response && response.id) {
        alert('Structured product created successfully!');
        setMode('form');
        router.push('/product-hub');
      } else {
        const error = response && response.message ? response : { message: 'Failed to create product' };
        alert(`Error: ${error.message}`);
      }
    } catch (error: any) {
      console.error('Network error:', error);
      alert(error.message || 'Network error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Convert form data to display format for review mode
  const getDisplayData = () => {
    return {
      name: formData.productSummary.productType,
      yield: `${formData.productSummary.yield.value}% ${formData.productSummary.yield.unit}`,
      minInvestment: `${formData.productSummary.minimumInvestment.currency} ${formData.productSummary.minimumInvestment.amount}`,
      barrier: `${formData.productSummary.barrier}%`,
      issuer: formData.productSummary.issuer,
      duration: formData.productSummary.duration,
      details: {
        ISIN: formData.productDetails.isin,
        Valor: formData.productDetails.valor,
        issuePrice: `${formData.productDetails.issuePrice}%`,
        initialSize: formData.productDetails.initialSize,
        denomination: `${formData.productSummary.minimumInvestment.currency} ${formData.productDetails.denomination}`,
        productType: formData.productDetails.productType,
      },
      keyFeatures: formData.keyFeatures.filter(f => f && typeof f === 'string' && f.trim() !== ""),
      underlyingAssets: formData.underlyingAssets.filter(a => a && a.name && a.name.trim() !== "").map(a => ({
        name: a.name,
        exchange: a.relatedExchange,
        ticker: a.bloombergTicker,
        currency: a.referenceCurrency,
        initialFixing: a.initialFixingLevel,
        strikeLevel: a.strikeLevel
      })),
      payoffData: formData.payoffChartData.payoffAtMaturity.map(p => ({ x: p.x, y: p.payoff })),
      performanceData: formData.payoffChartData.underlyingPerformances.map(p => ({ x: p.x, y: p.payoff })),
      scenarios: [
        {
          type: "observation",
          label: "At each quarterly observation",
          description: `Guaranteed Coupon\nCoupon: ${formData.scenarios.quarterlyObservation.guaranteedCoupon.coupon}%`,
        },
        {
          type: "maturity",
          label: "At maturity",
          description: `Above Barrier: ${formData.scenarios.atMaturity.aboveBarrier.result}\nBelow Barrier: ${formData.scenarios.atMaturity.belowBarrier.result}`,
        },
      ],
      coupons: formData.coupons.couponSchedule.filter(c => c && c.couponRate && c.couponPaymentDate).map(c => ({
        rate: c.couponRate,
        date: c.couponPaymentDate
      })),
      earlyRedemption: formData.earlyRedemption.earlyRedemptionAmountAndDates
        .filter(r => r && r.n)
        .map(r => ({ 
          n: r.n,
          level: r.autocallLevel,
          amount: r.earlyRedemptionAmount,
          obsDate: r.earlyRedemptionObservationDate,
          redDate: r.earlyRedemptionDate
        })),
      redemption: {
        formula: `${formData.redemptionFormulas.scenario1.condition}: ${formData.redemptionFormulas.scenario1.formula} | ${formData.redemptionFormulas.scenario2.condition}: ${formData.redemptionFormulas.scenario2.formula}`,
        info: [
          `Initial Fixing Level: ${formData.fixingLevels.initialFixingLevel}`,
          `Final Fixing Level: ${formData.fixingLevels.finalFixingLevel}`,
          `Relevant Fixing Level: ${formData.fixingLevels.relevantFixingLevel}`,
          `Worst Performance: ${formData.fixingLevels.worstPerformance}`,
        ],
      },
      generalInfo: {
        issuer: `${formData.generalInformation.issuer.name}, ${formData.generalInformation.issuer.address}`,
        rating: formData.generalInformation.issuerRating,
        supervision: formData.generalInformation.supervisionOfIssuer,
        agent: formData.generalInformation.calculationAgent,
        payingAgent: formData.generalInformation.fiscalTransferAndPayingAgents,
      },
      recommendations: [], // Can be populated based on business logic
    };
  };

  const validateForm = () => {
    const errors = [];
    
    // Check required fields
    if (!formData.productSummary.productType) errors.push("Product Type is required");
    if (!formData.productSummary.yield.value) errors.push("Yield Value is required");
    if (!formData.productSummary.minimumInvestment.amount) errors.push("Minimum Investment Amount is required");
    if (!formData.productSummary.barrier) errors.push("Barrier is required");
    if (!formData.productSummary.issuer) errors.push("Issuer is required");
    if (!formData.productSummary.duration) errors.push("Duration is required");
    
    // Check product details
    if (!formData.productDetails.isin) errors.push("ISIN is required");
    if (!formData.productDetails.productType) errors.push("Product Type (Details) is required");
    
    // Check at least one key feature
    if (formData.keyFeatures.filter(f => f && typeof f === 'string' && f.trim() !== "").length === 0) {
      errors.push("At least one Key Feature is required");
    }
    
    // Check at least one underlying asset with name
    if (formData.underlyingAssets.filter(a => a && a.name && a.name.trim() !== "").length === 0) {
      errors.push("At least one Underlying Asset is required");
    }
    
    // Check general information
    if (!formData.generalInformation.issuer.name) errors.push("Issuer Name is required");
    if (!formData.generalInformation.issuer.address) errors.push("Issuer Address is required");
    
    return errors;
  };

  const handleReview = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    setMode('review');
  };

  const handleSubmitWithValidation = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    await handleSubmit();
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section as keyof typeof prev]
    }));
  };

  const getFormProgress = () => {
    const errors = validateForm();
    const totalFields = 15; // Approximate number of required fields
    const filledFields = totalFields - errors.length;
    return Math.max(0, Math.min(100, (filledFields / totalFields) * 100));
  };

  if (mode === 'review') {
    const displayData = getDisplayData();
    const suitabilityPoints = formData.suitabilityQuestions.filter(q => q && typeof q === 'string' && q.trim() !== "");
    const riskPoints = formData.keyRisks.filter(r => r && typeof r === 'string' && r.trim() !== "");

    return (
      <div className="min-h-screen bg-gradient-to-br from-[#e8f5ee] to-[#f6fafd] p-4 flex flex-col gap-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className="flex items-center gap-2"
            onClick={() => setMode('form')}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Form
          </Button>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={() => setMode('form')}
            >
              <Eye className="h-4 w-4" /> Edit
            </Button>
            <Button 
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              onClick={handleSubmitWithValidation}
              disabled={isSubmitting}
            >
              <Send className="h-4 w-4" /> 
              {isSubmitting ? 'Submitting...' : 'Submit Product'}
            </Button>
          </div>
        </div>
        
        {/* Review Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar */}
          <div className="lg:col-span-1 flex flex-col gap-6">
            <StructuredProductSummary
              name={displayData.name}
              yieldValue={displayData.yield}
              minInvestment={displayData.minInvestment}
              barrier={displayData.barrier}
              issuer={displayData.issuer}
              duration={displayData.duration}
              details={displayData.details}
              keyFeatures={displayData.keyFeatures}
              onYieldChange={() => {}} // Disabled in review mode
            />
          </div>
          {/* Main Content */}
          <div className="lg:col-span-3 flex flex-col gap-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ProductSuitability points={suitabilityPoints} />
              <ProductKeyRisks risks={riskPoints} />
            </div>
            <UnderlyingAssets assets={displayData.underlyingAssets} />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <PayoffChart 
                data={displayData.payoffData} 
                barrier={displayData.barrier}
              />
              <UnderlyingPerformanceChart 
                data={displayData.performanceData} 
                barrier={displayData.barrier}
              />
            </div>
            <ProductScenarios scenarios={displayData.scenarios} />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ProductCoupons 
                coupons={displayData.coupons} 
                nextCouponDate={displayData.coupons[1]?.date || "-"} 
              />
              <EarlyRedemptionTable redemptions={displayData.earlyRedemption} />
            </div>
            <ProductRedemption 
              formula={displayData.redemption.formula} 
              info={displayData.redemption.info} 
            />
            <GeneralInformation
              issuer={displayData.generalInfo.issuer}
              rating={displayData.generalInfo.rating}
              supervision={displayData.generalInfo.supervision}
              agent={displayData.generalInfo.agent}
              payingAgent={displayData.generalInfo.payingAgent}
            />
            {displayData.recommendations.length > 0 && (
              <Recommendations recommendations={displayData.recommendations} />
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e8f5ee] to-[#f6fafd] p-4 flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <Button variant="ghost" size="sm" className="flex items-center gap-2" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={handleReview}
          >
            <Eye className="h-4 w-4" /> Review
          </Button>
        </div>
      </div>

      {/* Form Content */}
      <div className="max-w-6xl mx-auto w-full space-y-8">
        {/* Progress Bar */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Form Progress</span>
                <span>{Math.round(getFormProgress())}% Complete</span>
              </div>              <div className="w-full bg-gray-200 rounded-[30px] h-2">
                <div
                  className="bg-green-600 h-2 rounded-[30px] transition-all duration-300"
                  style={{ width: `${getFormProgress()}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Product Summary */}
        <Card>
          <CardHeader 
            className="cursor-pointer hover:bg-gray-50"
            onClick={() => toggleSection('productSummary')}
          >
            <CardTitle className="flex items-center justify-between">
              Product Summary
              <span>{expandedSections.productSummary ? '−' : '+'}</span>
            </CardTitle>
          </CardHeader>
          {expandedSections.productSummary && (
            <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Product Type</label>
              <Input
                value={formData.productSummary.productType}
                onChange={(e) => updateNestedFormData('productSummary', 'productType', '', e.target.value)}
                placeholder="e.g., Autocallable"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Yield Value (%)</label>
              <Input
                value={formData.productSummary.yield.value}
                onChange={(e) => updateNestedFormData('productSummary', 'yield', 'value', e.target.value)}
                placeholder="e.g., 13.01"
                type="number"
                step="0.01"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Minimum Investment Amount</label>
              <Input
                value={formData.productSummary.minimumInvestment.amount}
                onChange={(e) => updateNestedFormData('productSummary', 'minimumInvestment', 'amount', e.target.value)}
                placeholder="e.g., 100000"
                type="number"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Barrier (%)</label>
              <Input
                value={formData.productSummary.barrier}
                onChange={(e) => updateNestedFormData('productSummary', 'barrier', '', e.target.value)}
                placeholder="e.g., 55"
                type="number"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Issuer</label>
              <Input
                value={formData.productSummary.issuer}
                onChange={(e) => updateNestedFormData('productSummary', 'issuer', '', e.target.value)}
                placeholder="e.g., ESG"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Duration</label>
              <Input
                value={formData.productSummary.duration}
                onChange={(e) => updateNestedFormData('productSummary', 'duration', '', e.target.value)}
                placeholder="e.g., 13 Months"
              />
            </div>
          </CardContent>
          )}
        </Card>

        {/* Product Details */}
        <Card>
          <CardHeader>
            <CardTitle>Product Details</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">ISIN</label>
              <Input
                value={formData.productDetails.isin}
                onChange={(e) => updateNestedFormData('productDetails', 'isin', '', e.target.value)}
                placeholder="e.g., XS2844793609"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Valor</label>
              <Input
                value={formData.productDetails.valor}
                onChange={(e) => updateNestedFormData('productDetails', 'valor', '', e.target.value)}
                placeholder="e.g., TBD"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Issue Price (%)</label>
              <Input
                value={formData.productDetails.issuePrice}
                onChange={(e) => updateNestedFormData('productDetails', 'issuePrice', '', e.target.value)}
                placeholder="e.g., 100.00"
                type="number"
                step="0.01"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Initial Size</label>
              <Input
                value={formData.productDetails.initialSize}
                onChange={(e) => updateNestedFormData('productDetails', 'initialSize', '', e.target.value)}
                placeholder="e.g., Upto USD 500K"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Denomination</label>
              <Input
                value={formData.productDetails.denomination}
                onChange={(e) => updateNestedFormData('productDetails', 'denomination', '', e.target.value)}
                placeholder="e.g., 1000"
                type="number"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Product Type (Details)</label>
              <Input
                value={formData.productDetails.productType}
                onChange={(e) => updateNestedFormData('productDetails', 'productType', '', e.target.value)}
                placeholder="e.g., Autocallable"
              />
            </div>
          </CardContent>
        </Card>

        {/* Key Features */}
        <Card>
          <CardHeader>
            <CardTitle>Key Features</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.keyFeatures.map((feature, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={feature}
                  onChange={(e) => updateArrayItem('keyFeatures', index, '', e.target.value)}
                  placeholder="Enter key feature"
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  onClick={() => removeArrayItem('keyFeatures', index)}
                  disabled={formData.keyFeatures.length === 1}
                >
                  Remove
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              onClick={() => addArrayItem('keyFeatures', "")}
            >
              Add Feature
            </Button>
          </CardContent>
        </Card>

        {/* Suitability Questions */}
        <Card>
          <CardHeader>
            <CardTitle>Suitability Questions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.suitabilityQuestions.map((question, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={question}
                  onChange={(e) => updateArrayItem('suitabilityQuestions', index, '', e.target.value)}
                  placeholder="Enter suitability question"
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  onClick={() => removeArrayItem('suitabilityQuestions', index)}
                  disabled={formData.suitabilityQuestions.length === 1}
                >
                  Remove
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              onClick={() => addArrayItem('suitabilityQuestions', "")}
            >
              Add Question
            </Button>
          </CardContent>
        </Card>

        {/* Key Risks */}
        <Card>
          <CardHeader>
            <CardTitle>Key Risks</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.keyRisks.map((risk, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={risk}
                  onChange={(e) => updateArrayItem('keyRisks', index, '', e.target.value)}
                  placeholder="Enter key risk"
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  onClick={() => removeArrayItem('keyRisks', index)}
                  disabled={formData.keyRisks.length === 1}
                >
                  Remove
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              onClick={() => addArrayItem('keyRisks', "")}
            >
              Add Risk
            </Button>
          </CardContent>
        </Card>

        {/* Underlying Assets */}
        <Card>
          <CardHeader>
            <CardTitle>Underlying Assets</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.underlyingAssets.map((asset, index) => (
              <div key={index} className="p-4 border rounded-[30px] space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Asset {index + 1}</h4>
                  <Button
                    variant="outline"
                    onClick={() => removeArrayItem('underlyingAssets', index)}
                    disabled={formData.underlyingAssets.length === 1}
                  >
                    Remove Asset
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Name</label>
                    <Input
                      value={asset.name}
                      onChange={(e) => updateArrayItem('underlyingAssets', index, 'name', e.target.value)}
                      placeholder="e.g., TransMedics Group Inc."
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Symbol</label>
                    <Input
                      value={asset.symbol}
                      onChange={(e) => updateArrayItem('underlyingAssets', index, 'symbol', e.target.value)}
                      placeholder="e.g., TMDX"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Related Exchange</label>
                    <Input
                      value={asset.relatedExchange}
                      onChange={(e) => updateArrayItem('underlyingAssets', index, 'relatedExchange', e.target.value)}
                      placeholder="e.g., Nasdaq Stock"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Bloomberg Ticker</label>
                    <Input
                      value={asset.bloombergTicker}
                      onChange={(e) => updateArrayItem('underlyingAssets', index, 'bloombergTicker', e.target.value)}
                      placeholder="e.g., TMDX UQ Equity"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Reference Currency</label>
                    <Input
                      value={asset.referenceCurrency}
                      onChange={(e) => updateArrayItem('underlyingAssets', index, 'referenceCurrency', e.target.value)}
                      placeholder="e.g., USD"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Initial Fixing Level</label>
                    <Input
                      value={asset.initialFixingLevel}
                      onChange={(e) => updateArrayItem('underlyingAssets', index, 'initialFixingLevel', e.target.value)}
                      placeholder="e.g., TBD"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Strike Level</label>
                    <Input
                      value={asset.strikeLevel}
                      onChange={(e) => updateArrayItem('underlyingAssets', index, 'strikeLevel', e.target.value)}
                      placeholder="e.g., TBD"
                    />
                  </div>
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              onClick={() => addArrayItem('underlyingAssets', {
                name: "",
                symbol: "",
                relatedExchange: "",
                bloombergTicker: "",
                referenceCurrency: "USD",
                initialFixingLevel: "",
                strikeLevel: ""
              })}
            >
              Add Asset
            </Button>
          </CardContent>
        </Card>

        {/* Coupons */}
        <Card>
          <CardHeader>
            <CardTitle>Coupons</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Coupon Rate (%)</label>
                <Input
                  value={formData.coupons.couponRate}
                  onChange={(e) => updateNestedFormData('coupons', 'couponRate', '', e.target.value)}
                  placeholder="e.g., 8.26"
                  type="number"
                  step="0.01"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Next Coupon (%)</label>
                <Input
                  value={formData.coupons.nextCoupon}
                  onChange={(e) => updateNestedFormData('coupons', 'nextCoupon', '', e.target.value)}
                  placeholder="e.g., 8.26"
                  type="number"
                  step="0.01"
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Coupon Schedule</h4>
              {formData.coupons.couponSchedule.map((coupon, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    value={coupon.n}
                    onChange={(e) => updateArrayItem('coupons', index, 'n', parseInt(e.target.value) || 1)}
                    placeholder="N (e.g., 1)"
                    className="w-20"
                    type="number"
                  />
                  <Input
                    value={coupon.couponRate}
                    onChange={(e) => updateArrayItem('coupons', index, 'couponRate', e.target.value)}
                    placeholder="Rate (e.g., 8.26%)"
                    className="flex-1"
                  />
                  <Input
                    value={coupon.couponPaymentDate}
                    onChange={(e) => updateArrayItem('coupons', index, 'couponPaymentDate', e.target.value)}
                    placeholder="Payment Date"
                    className="flex-1"
                    type="date"
                  />
                  <Button
                    variant="outline"
                    onClick={() => removeArrayItem('coupons', index)}
                    disabled={formData.coupons.couponSchedule.length === 1}
                  >
                    Remove
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                onClick={() => addArrayItem('coupons', { n: formData.coupons.couponSchedule.length + 1, couponRate: "", couponPaymentDate: "" })}
              >
                Add Coupon
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Early Redemption */}
        <Card>
          <CardHeader>
            <CardTitle>Early Redemption</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.earlyRedemption.automaticEarlyRedemptionEvent}
                onChange={(e) => updateNestedFormData('earlyRedemption', 'automaticEarlyRedemptionEvent', '', e.target.checked)}
              />
              <label className="text-sm font-medium">Automatic Early Redemption Event</label>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Notes</h4>
              {formData.earlyRedemption.notes.map((note, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    value={note}
                    onChange={(e) => updateArrayItem('earlyRedemptionNotes', index, '', e.target.value)}
                    placeholder="Enter early redemption note"
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    onClick={() => removeArrayItem('earlyRedemptionNotes', index)}
                    disabled={formData.earlyRedemption.notes.length === 1}
                  >
                    Remove
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                onClick={() => addArrayItem('earlyRedemptionNotes', "")}
              >
                Add Note
              </Button>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Early Redemption Amount and Dates</h4>
              {formData.earlyRedemption.earlyRedemptionAmountAndDates.map((redemption, index) => (
                <div key={index} className="p-4 border rounded-[30px] space-y-4">
                  <div className="flex justify-between items-center">
                    <h5 className="font-medium">Redemption {index + 1}</h5>
                    <Button
                      variant="outline"
                      onClick={() => removeArrayItem('earlyRedemption', index)}
                      disabled={formData.earlyRedemption.earlyRedemptionAmountAndDates.length === 1}
                    >
                      Remove
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">N</label>
                      <Input
                        value={redemption.n}
                        onChange={(e) => updateArrayItem('earlyRedemption', index, 'n', e.target.value)}
                        placeholder="e.g., 2"
                        type="number"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Autocall Level</label>
                      <Input
                        value={redemption.autocallLevel}
                        onChange={(e) => updateArrayItem('earlyRedemption', index, 'autocallLevel', e.target.value)}
                        placeholder="e.g., 120.00%"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Early Redemption Amount</label>
                      <Input
                        value={redemption.earlyRedemptionAmount}
                        onChange={(e) => updateArrayItem('earlyRedemption', index, 'earlyRedemptionAmount', e.target.value)}
                        placeholder="e.g., 100.00%"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Observation Date</label>
                      <Input
                        value={redemption.earlyRedemptionObservationDate}
                        onChange={(e) => updateArrayItem('earlyRedemption', index, 'earlyRedemptionObservationDate', e.target.value)}
                        placeholder="Observation Date"
                        type="date"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Redemption Date</label>
                      <Input
                        value={redemption.earlyRedemptionDate}
                        onChange={(e) => updateArrayItem('earlyRedemption', index, 'earlyRedemptionDate', e.target.value)}
                        placeholder="Redemption Date"
                        type="date"
                      />
                    </div>
                  </div>
                </div>
              ))}
              <Button
                variant="outline"
                onClick={() => addArrayItem('earlyRedemption', { n: formData.earlyRedemption.earlyRedemptionAmountAndDates.length + 1, autocallLevel: "", earlyRedemptionAmount: "", earlyRedemptionObservationDate: "", earlyRedemptionDate: "" })}
              >
                Add Redemption
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Redemption Formulas */}
        <Card>
          <CardHeader>
            <CardTitle>Redemption Formulas</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <h4 className="font-medium">Scenario 1</h4>
              <div className="space-y-2">
                <label className="text-sm font-medium">Condition</label>
                <Input
                  value={formData.redemptionFormulas.scenario1.condition}
                  onChange={(e) => updateNestedFormData('redemptionFormulas', 'scenario1', 'condition', e.target.value)}
                  placeholder="Enter condition for scenario 1"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Formula</label>
                <Input
                  value={formData.redemptionFormulas.scenario1.formula}
                  onChange={(e) => updateNestedFormData('redemptionFormulas', 'scenario1', 'formula', e.target.value)}
                  placeholder="Enter redemption formula for scenario 1"
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Scenario 2</h4>
              <div className="space-y-2">
                <label className="text-sm font-medium">Condition</label>
                <Input
                  value={formData.redemptionFormulas.scenario2.condition}
                  onChange={(e) => updateNestedFormData('redemptionFormulas', 'scenario2', 'condition', e.target.value)}
                  placeholder="Enter condition for scenario 2"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Formula</label>
                <Input
                  value={formData.redemptionFormulas.scenario2.formula}
                  onChange={(e) => updateNestedFormData('redemptionFormulas', 'scenario2', 'formula', e.target.value)}
                  placeholder="Enter redemption formula for scenario 2"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fixing Levels */}
        <Card>
          <CardHeader>
            <CardTitle>Fixing Levels</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Initial Fixing Level</label>
              <Input
                value={formData.fixingLevels.initialFixingLevel}
                onChange={(e) => updateNestedFormData('fixingLevels', 'initialFixingLevel', '', e.target.value)}
                placeholder="e.g., 100"
                type="number"
                step="0.01"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Relevant Fixing Level</label>
              <Input
                value={formData.fixingLevels.relevantFixingLevel}
                onChange={(e) => updateNestedFormData('fixingLevels', 'relevantFixingLevel', '', e.target.value)}
                placeholder="e.g., 100"
                type="number"
                step="0.01"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Final Fixing Level</label>
              <Input
                value={formData.fixingLevels.finalFixingLevel}
                onChange={(e) => updateNestedFormData('fixingLevels', 'finalFixingLevel', '', e.target.value)}
                placeholder="e.g., 100"
                type="number"
                step="0.01"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Performance (%)</label>
              <Input
                value={formData.fixingLevels.performance}
                onChange={(e) => updateNestedFormData('fixingLevels', 'performance', '', e.target.value)}
                placeholder="e.g., 5.5"
                type="number"
                step="0.01"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Worst Performance (%)</label>
              <Input
                value={formData.fixingLevels.worstPerformance}
                onChange={(e) => updateNestedFormData('fixingLevels', 'worstPerformance', '', e.target.value)}
                placeholder="e.g., -10.5"
                type="number"
                step="0.01"
              />
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.actions.KID_available}
                  onChange={(e) => updateNestedFormData('actions', 'KID_available', '', e.target.checked)}
                />
                <label className="text-sm font-medium">KID Available</label>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">KID Document Link</label>
                <Input
                  value={formData.actions.KID_doc_link}
                  onChange={(e) => updateNestedFormData('actions', 'KID_doc_link', '', e.target.value)}
                  placeholder="Enter KID document link"
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.actions.contactAdvisor_available}
                  onChange={(e) => updateNestedFormData('actions', 'contactAdvisor_available', '', e.target.checked)}
                />
                <label className="text-sm font-medium">Contact Advisor Available</label>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Contact Advisor Phone</label>
                <Input
                  value={formData.actions.contactAdvisor_phone}
                  onChange={(e) => updateNestedFormData('actions', 'contactAdvisor_phone', '', e.target.value)}
                  placeholder="Enter advisor phone number"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payoff Chart Data */}
        <Card>
          <CardHeader>
            <CardTitle>Payoff Chart Data</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Payoff at Maturity</h4>
              {formData.payoffChartData.payoffAtMaturity.map((point, index) => (
                <div key={index} className="grid grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">X</label>
                    <Input
                      value={point.x}
                      onChange={(e) => updateArrayItem('payoffChartData', index, 'x', parseFloat(e.target.value) || 0)}
                      placeholder="X coordinate"
                      type="number"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Payoff</label>
                    <Input
                      value={point.payoff}
                      onChange={(e) => updateArrayItem('payoffChartData', index, 'payoff', parseFloat(e.target.value) || 0)}
                      placeholder="Payoff value"
                      type="number"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Loss Limit</label>
                    <Input
                      value={point.lossLimit}
                      onChange={(e) => updateArrayItem('payoffChartData', index, 'lossLimit', parseFloat(e.target.value) || 0)}
                      placeholder="Loss limit"
                      type="number"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Barrier</label>
                    <Input
                      value={point.barrier}
                      onChange={(e) => updateArrayItem('payoffChartData', index, 'barrier', parseFloat(e.target.value) || 0)}
                      placeholder="Barrier value"
                      type="number"
                    />
                  </div>
                </div>
              ))}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => addArrayItem('payoffChartData', { x: 0, payoff: 0, lossLimit: 0, barrier: 0 })}
                >
                  Add Payoff Point
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    // Clear existing data
                    setFormData(prev => ({
                      ...prev,
                      payoffChartData: {
                        ...prev.payoffChartData,
                        payoffAtMaturity: []
                      }
                    }));
                    
                    // Generate sample payoff data
                    const barrierLevel = parseFloat(formData.productSummary.barrier) || 55;
                    const yieldRate = parseFloat(formData.productSummary.yield.value) || 13;
                    const sampleData: Array<{ x: number; payoff: number; lossLimit: number; barrier: number }> = [];
                    
                    for (let i = 0; i <= 200; i += 20) {
                      const performance = i / 100;
                      let payoff;
                      
                      if (performance >= barrierLevel / 100) {
                        payoff = 100 + yieldRate;
                      } else {
                        payoff = performance * 100;
                      }
                      
                      sampleData.push({ x: i, payoff, lossLimit: 0, barrier: barrierLevel });
                    }
                    
                    setFormData(prev => ({
                      ...prev,
                      payoffChartData: {
                        ...prev.payoffChartData,
                        payoffAtMaturity: sampleData
                      }
                    }));
                  }}
                >
                  Generate Sample Data
                </Button>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Underlying Performances</h4>
              {formData.payoffChartData.underlyingPerformances.map((point, index) => (
                <div key={index} className="grid grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">X</label>
                    <Input
                      value={point.x}
                      onChange={(e) => updateArrayItem('underlyingPerformances', index, 'x', parseFloat(e.target.value) || 0)}
                      placeholder="X coordinate"
                      type="number"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Payoff</label>
                    <Input
                      value={point.payoff}
                      onChange={(e) => updateArrayItem('underlyingPerformances', index, 'payoff', parseFloat(e.target.value) || 0)}
                      placeholder="Payoff value"
                      type="number"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Loss Limit</label>
                    <Input
                      value={point.lossLimit}
                      onChange={(e) => updateArrayItem('underlyingPerformances', index, 'lossLimit', parseFloat(e.target.value) || 0)}
                      placeholder="Loss limit"
                      type="number"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Barrier</label>
                    <Input
                      value={point.barrier}
                      onChange={(e) => updateArrayItem('underlyingPerformances', index, 'barrier', parseFloat(e.target.value) || 0)}
                      placeholder="Barrier value"
                      type="number"
                    />
                  </div>
                </div>
              ))}
              <Button
                variant="outline"
                onClick={() => addArrayItem('underlyingPerformances', { x: 0, payoff: 0, lossLimit: 0, barrier: 0 })}
              >
                Add Performance Point
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* General Information */}
        <Card>
          <CardHeader>
            <CardTitle>General Information</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Issuer Name</label>
              <Input
                value={formData.generalInformation.issuer.name}
                onChange={(e) => updateNestedFormData('generalInformation', 'issuer', 'name', e.target.value)}
                placeholder="e.g., Marex Financial"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Issuer Address</label>
              <Input
                value={formData.generalInformation.issuer.address}
                onChange={(e) => updateNestedFormData('generalInformation', 'issuer', 'address', e.target.value)}
                placeholder="e.g., 155 Bishopsgate, London"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Issuer Rating</label>
              <Input
                value={formData.generalInformation.issuerRating}
                onChange={(e) => updateNestedFormData('generalInformation', 'issuerRating', '', e.target.value)}
                placeholder="e.g., BBB (S&P Global)"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Supervision of Issuer</label>
              <Input
                value={formData.generalInformation.supervisionOfIssuer}
                onChange={(e) => updateNestedFormData('generalInformation', 'supervisionOfIssuer', '', e.target.value)}
                placeholder="Enter supervision details"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Calculation Agent</label>
              <Input
                value={formData.generalInformation.calculationAgent}
                onChange={(e) => updateNestedFormData('generalInformation', 'calculationAgent', '', e.target.value)}
                placeholder="e.g., Marex Financial"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Fiscal Transfer and Paying Agents</label>
              <Input
                value={formData.generalInformation.fiscalTransferAndPayingAgents}
                onChange={(e) => updateNestedFormData('generalInformation', 'fiscalTransferAndPayingAgents', '', e.target.value)}
                placeholder="Enter agent details"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Floating Action Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg p-4 z-50">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div className="text-sm text-gray-600">
            Progress: {Math.round(getFormProgress())}% Complete
          </div>
          <div className="flex gap-4">
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={handleReview}
            >
              <Eye className="h-4 w-4" /> Preview
            </Button>
            <Button 
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              onClick={handleSubmitWithValidation}
              disabled={isSubmitting}
            >
              <Send className="h-4 w-4" /> 
              {isSubmitting ? 'Submitting...' : 'Submit Product'}
            </Button>
          </div>
        </div>
      </div>

      {/* Add padding to account for floating action bar */}
      <div className="h-20"></div>
    </div>
  );
}