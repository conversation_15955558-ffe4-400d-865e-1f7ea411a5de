import React from "react";

export interface ProductSuitabilityProps {
  points: string[];
}

const ProductSuitability: React.FC<ProductSuitabilityProps> = ({ points }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-green-700 mb-2">Is this product right for you?</div>
    <div className="flex flex-col gap-2">
      {points && points.length > 0 ? (
        points.map((point, index) => (
          <div key={index} className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
            {point}
          </div>
        ))
      ) : (
        <>
          <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
            You are looking to earn an income above the market rate
          </div>
          <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
            You are neutral, moderately bearish or moderately bullish on the underlying assets
          </div>
          <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
            You can tolerate the reinvestment risk arising from an early redemption (autocall)
          </div>
        </>
      )}
    </div>
    <button className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2 ml-0">Save Info</button>
  </div>
);

export default ProductSuitability; 