import React from "react";

export interface Recommendation {
  name: string;
  yield: string;
  minInvestment: string;
  barrier: string;
  issuer: string;
  duration: string;
}

export interface RecommendationsProps {
  recommendations: Recommendation[];
}

const Recommendations: React.FC<RecommendationsProps> = ({ recommendations }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-gray-700 mb-2">Similar Recommendations</div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {recommendations.map((rec, i) => (
        <div key={i} className="bg-[#e8f5ee] rounded-xl p-4">
          <div className="font-semibold text-gray-900">{rec.name}</div>
          <div className="text-green-700 font-bold">Yield {rec.yield}</div>
          <div className="text-xs text-gray-500">Min. Investment: {rec.minInvestment}</div>
          <div className="text-xs text-gray-500">Barrier: {rec.barrier}</div>
          <div className="text-xs text-gray-500">Issuer: {rec.issuer}</div>
          <div className="text-xs text-gray-500">Duration: {rec.duration}</div>
        </div>
      ))}
    </div>
  </div>
);

export default Recommendations; 