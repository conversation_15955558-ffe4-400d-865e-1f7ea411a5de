import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";

export interface ProductDetails {
  ISIN: string;
  Valor: string;
  issuePrice: string;
  initialSize: string;
  denomination: string;
  productType: string;
}

export interface StructuredProductSummaryProps {
  name: string;
  yieldValue: string;
  minInvestment: string;
  barrier: string;
  issuer: string;
  duration: string;
  details: ProductDetails;
  keyFeatures: string[];
  onYieldChange?: (newYield: string) => void;
}

const StructuredProductSummary: React.FC<StructuredProductSummaryProps> = ({
  name,
  yieldValue,
  minInvestment,
  barrier,
  issuer,
  duration,
  details,
  keyFeatures,
  onYieldChange,
}) => {
  const [editing, setEditing] = useState(false);
  const [localYield, setLocalYield] = useState(yieldValue);

  React.useEffect(() => {
    setLocalYield(yieldValue);
  }, [yieldValue]);

  const handleYieldClick = () => {
    if (onYieldChange) setEditing(true);
  };

  const handleYieldBlur = () => {
    setEditing(false);
    if (onYieldChange && localYield !== yieldValue) {
      onYieldChange(localYield);
    }
  };

  const handleYieldKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setEditing(false);
      if (onYieldChange && localYield !== yieldValue) {
        onYieldChange(localYield);
      }
    }
  };

  return (
    <div className="rounded-2xl p-6 bg-white/80 shadow flex flex-col gap-4">
      <div className="flex items-center gap-3 mb-2">
        <div className="w-10 h-10 rounded-full bg-[#e8f5ee] flex items-center justify-center">
          <span className="text-[#05A049] font-bold text-xl">U</span>
        </div>
        <div>
          <div className="text-lg font-bold text-gray-900">{name || "Product Name"}</div>
          <div className="text-green-700 font-bold text-xl flex items-center gap-2">
            Yield
            {editing ? (
              <input 
                className="border rounded px-2 py-1 w-28 text-green-700 font-bold text-xl"
                value={localYield}
                onChange={(e) => setLocalYield(e.target.value)}
                onBlur={handleYieldBlur}
                onKeyDown={handleYieldKeyDown}
                autoFocus
              />
            ) : (
              <span 
                className="px-2 py-1 w-28 text-green-700 font-bold text-xl cursor-pointer"
                onClick={handleYieldClick}
              >
                {yieldValue || "13.01% P.A."}
              </span>
            )}
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-2 mb-2">
        <div>
          <div className="text-xs text-gray-500">Min. Investment</div>
          <div className="text-sm font-medium text-gray-900">{minInvestment || "USD 100k"}</div>
        </div>
        <div>
          <div className="text-xs text-gray-500">Barrier</div>
          <div className="text-sm font-medium text-gray-900">{barrier || "55%"}</div>
        </div>
        <div>
          <div className="text-xs text-gray-500">Issuer</div>
          <div className="text-sm font-medium text-gray-900">{issuer || "ESG"}</div>
        </div>
        <div>
          <div className="text-xs text-gray-500">Duration</div>
          <div className="text-sm font-medium text-gray-900">{duration || "13 Months"}</div>
        </div>
      </div>
      <div className="text-xs text-gray-500 mb-2">Product Details:</div>
      <div className="text-xs text-gray-700 mb-2 flex flex-col gap-2">
        <div className="px-2 py-1 bg-gray-50 rounded">{details.ISIN || "XS2844793609"} (ISIN)</div>
        <div className="px-2 py-1 bg-gray-50 rounded">{details.Valor || "TBD"} (Valor)</div>
        <div className="px-2 py-1 bg-gray-50 rounded">{details.issuePrice || "100.00%"} (Issue Price)</div>
        <div className="px-2 py-1 bg-gray-50 rounded">{details.initialSize || "Upto USD 500K"} (Initial Size)</div>
        <div className="px-2 py-1 bg-gray-50 rounded">{details.denomination || "USD 1000"} (Denomination)</div>
        <div className="px-2 py-1 bg-gray-50 rounded">{details.productType || "Autocallable"} (Product Type)</div>
      </div>
      <div className="text-xs text-gray-500 mb-2">Key Features:</div>
      <div className="flex flex-col gap-2 mb-4">
        {keyFeatures && keyFeatures.length > 0 ? (
          keyFeatures.map((feature, index) => (
            <div key={index} className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
              {feature}
            </div>
          ))
        ) : (
          <>
            <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">Monthly coupon payments</div>
            <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">Autocall feature for early redemption</div>
            <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">Capital protection above barrier level</div>
          </>
        )}
      </div>
      <Button className="w-full bg-[#05A049] hover:bg-green-700 text-white py-3 rounded-full text-lg font-bold mb-2">Add Structure product</Button>
      <button className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-1 ml-0">Save Info</button>
    </div>
  );
};

export default StructuredProductSummary; 