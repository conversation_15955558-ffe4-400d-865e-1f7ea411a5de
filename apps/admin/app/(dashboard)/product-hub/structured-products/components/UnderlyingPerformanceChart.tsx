import React from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

export interface UnderlyingPerformanceChartProps {
  data: { x: number; y: number }[];
  barrier?: string;
}

const UnderlyingPerformanceChart: React.FC<UnderlyingPerformanceChartProps> = ({ data, barrier }) => {
  // Use only the data from the form
  const sortedData = (data || []).filter(p => !isNaN(p.x) && !isNaN(p.y)).sort((a, b) => a.x - b.x);
  const xs = sortedData.map(point => point.x);
  const ys = sortedData.map(point => point.y);

  // Low strike (y = x) line
  const lowStrikeYs = xs.map(x => x);

  // Barrier line
  const barrierValue = barrier ? parseFloat(barrier.replace('%', '')) : 0;
  const barrierYs = xs.map(() => barrierValue);

  const chartDataConfig = {
    labels: xs,
    datasets: [
      {
        label: 'Payoff',
        data: ys,
        borderColor: '#05A049',
        backgroundColor: '#05A049',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointBackgroundColor: '#05A049',
        showLine: true,
      },
      {
        label: 'Low strike',
        data: lowStrikeYs,
        borderColor: '#A52828',
        backgroundColor: '#A52828',
        borderWidth: 2,
        fill: false,
        borderDash: [],
        pointRadius: 0,
        showLine: true,
      },
      {
        label: 'Barrier',
        data: barrierYs,
        borderColor: '#1C355E',
        backgroundColor: '#1C355E',
        borderWidth: 2,
        fill: false,
        borderDash: [6, 6],
        pointRadius: 0,
        showLine: true,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          font: { size: 14 },
        },
      },
      title: {
        display: true,
        text: 'Underlying Performances',
        color: '#05A049',
        font: {
          size: 20,
          weight: 'bold' as const,
        },
        align: 'start' as const,
        padding: { bottom: 16 },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}`;
          },
        },
      },
    },
    scales: {
      x: {
        title: {
          display: false,
        },
        grid: {
          color: 'rgba(0,0,0,0.08)',
        },
        ticks: {
          font: { size: 12 },
        },
      },
      y: {
        title: {
          display: false,
        },
        grid: {
          color: 'rgba(0,0,0,0.08)',
        },
        ticks: {
          font: { size: 12 },
        },
        min: 0,
        max: Math.max(...ys, ...lowStrikeYs, ...barrierYs, 100, 160),
      },
    },
  };

  return (
    <div className="rounded-2xl p-6 bg-white/80 shadow">
      <div className="font-semibold text-[#05A049] text-lg mb-2 flex items-center gap-2">
        <span className="inline-block align-middle">📈</span> Underlying Performances
      </div>
      <div className="h-64">
        <Line options={options} data={chartDataConfig} />
      </div>
    </div>
  );
};

export default UnderlyingPerformanceChart; 