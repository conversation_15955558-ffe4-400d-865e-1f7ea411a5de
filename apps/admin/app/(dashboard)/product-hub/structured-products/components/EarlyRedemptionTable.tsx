import React from "react";

export interface EarlyRedemption {
  n: number;
  level: string;
  amount: string;
  obsDate: string;
  redDate: string;
}

export interface EarlyRedemptionTableProps {
  redemptions: EarlyRedemption[];
}

const EarlyRedemptionTable: React.FC<EarlyRedemptionTableProps> = ({ redemptions }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-gray-700 mb-2">Early Redemption</div>
    <table className="w-full text-xs">
      <thead>
        <tr className="text-gray-500">
          <th>n</th>
          <th>Autocall Level</th>
          <th>Early Redemption Amount</th>
          <th>Observation Date</th>
          <th>Redemption Date</th>
        </tr>
      </thead>
      <tbody>
        {redemptions && redemptions.length > 0 ? (
          redemptions.map((r, i) => (
            <tr key={i}>
              <td>{r.n}</td>
              <td>
                <div className="px-2 py-1 w-20 bg-gray-50 rounded text-xs">
                  {r.level || "120.00%"}
                </div>
              </td>
              <td>
                <div className="px-2 py-1 w-24 bg-gray-50 rounded text-xs">
                  {r.amount || "100.00%"}
                </div>
              </td>
              <td>
                <div className="px-2 py-1 w-28 bg-gray-50 rounded text-xs">
                  {r.obsDate || "Observation Date"}
                </div>
              </td>
              <td>
                <div className="px-2 py-1 w-28 bg-gray-50 rounded text-xs">
                  {r.redDate || "Redemption Date"}
                </div>
              </td>
            </tr>
          ))
        ) : (
          [
            { n: 2, level: "120.00%", amount: "100.00%", obsDate: "19 Mar 2026", redDate: "26 Mar 2026" },
            { n: 3, level: "120.00%", amount: "100.00%", obsDate: "21 Sept 2026", redDate: "28 Sept 2026" },
            { n: 4, level: "120.00%", amount: "100.00%", obsDate: "19 Mar 2027", redDate: "29 Mar 2027" },
          ].map((r, i) => (
            <tr key={i}>
              <td>{r.n}</td>
              <td>
                <div className="px-2 py-1 w-20 bg-gray-50 rounded text-xs">
                  {r.level}
                </div>
              </td>
              <td>
                <div className="px-2 py-1 w-24 bg-gray-50 rounded text-xs">
                  {r.amount}
                </div>
              </td>
              <td>
                <div className="px-2 py-1 w-28 bg-gray-50 rounded text-xs">
                  {r.obsDate}
                </div>
              </td>
              <td>
                <div className="px-2 py-1 w-28 bg-gray-50 rounded text-xs">
                  {r.redDate}
                </div>
              </td>
            </tr>
          ))
        )}
      </tbody>
    </table>
    <button className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2 ml-0">Save Info</button>
  </div>
);

export default EarlyRedemptionTable; 