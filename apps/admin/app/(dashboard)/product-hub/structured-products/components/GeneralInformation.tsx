import React from "react";

export interface GeneralInformationProps {
  issuer: string;
  rating: string;
  supervision: string;
  agent: string;
  payingAgent: string;
}

const GeneralInformation: React.FC<GeneralInformationProps> = ({ issuer, rating, supervision, agent, payingAgent }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-gray-700 mb-2">General Information</div>
    <div className="flex flex-col gap-2 text-xs text-gray-700">
      <div className="px-2 py-1 bg-gray-50 rounded">
        {issuer || "Marex Financial, 155 Bishopsgate, London, EC2M 3TQ, United Kingdom"} (Issuer)
      </div>
      <div className="px-2 py-1 bg-gray-50 rounded">
        {rating || "BBB (S&P Global)"} (Issuer Rating)
      </div>
      <div className="px-2 py-1 bg-gray-50 rounded">
        {supervision || "The Issuer is authorised and regulated in the United Kingdom by the Financial Conduct Authority (\"FCA\")"} (Supervision)
      </div>
      <div className="px-2 py-1 bg-gray-50 rounded">
        {agent || "Marex Financial"} (Calculation Agent)
      </div>
      <div className="px-2 py-1 bg-gray-50 rounded">
        {payingAgent || "Citibank, N.A., London Branch, 33 Canada Square, Canary Wharf, London E14 5LB, United Kingdom"} (Paying Agent)
      </div>
    </div>
    <button className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2 ml-0">Save Info</button>
  </div>
);

export default GeneralInformation; 