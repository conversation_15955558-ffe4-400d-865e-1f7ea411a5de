import React from "react";

export interface Scenario {
  type: string;
  label: string;
  description: string;
}

export interface ProductScenariosProps {
  scenarios: Scenario[];
}

const ProductScenarios: React.FC<ProductScenariosProps> = ({ scenarios }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-gray-700 mb-2">Understand the scenarios</div>
    <div className="flex flex-col md:flex-row gap-4">
      {scenarios.map((s, i) => (
        <div key={i} className="flex-1 bg-[#e8f5ee] rounded-xl p-4">
          <div className="font-semibold text-green-700">{s.label}</div>
          <div className="text-xs text-gray-700 whitespace-pre-line">{s.description}</div>
        </div>
      ))}
    </div>
  </div>
);

export default ProductScenarios; 