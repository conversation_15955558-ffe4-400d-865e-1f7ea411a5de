import React from "react";

export interface UnderlyingAsset {
  name: string;
  exchange: string;
  ticker: string;
  currency: string;
  initialFixing: string;
  strikeLevel: string;
}

export interface UnderlyingAssetsProps {
  assets: UnderlyingAsset[];
}

const UnderlyingAssets: React.FC<UnderlyingAssetsProps> = ({ assets }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-gray-700 mb-2">Underlying Assets</div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {assets && assets.length > 0 ? (
        assets.map((asset, i) => (
          <div key={i} className="bg-[#e8f5ee] rounded-xl p-4 flex flex-col gap-2">
            <div className="text-sm">
              <span className="text-gray-500 text-xs">Name:</span>
              <div className="font-medium text-gray-900">{asset.name || "Asset Name"}</div>
            </div>
            <div className="text-sm">
              <span className="text-gray-500 text-xs">Exchange:</span>
              <div className="font-medium text-gray-900">{asset.exchange || "Exchange"}</div>
            </div>
            <div className="text-sm">
              <span className="text-gray-500 text-xs">Bloomberg Ticker:</span>
              <div className="font-medium text-gray-900">{asset.ticker || "Ticker"}</div>
            </div>
            <div className="text-sm">
              <span className="text-gray-500 text-xs">Currency:</span>
              <div className="font-medium text-gray-900">{asset.currency || "USD"}</div>
            </div>
            <div className="text-sm">
              <span className="text-gray-500 text-xs">Initial Fixing:</span>
              <div className="font-medium text-gray-900">{asset.initialFixing || "TBD"}</div>
            </div>
            <div className="text-sm">
              <span className="text-gray-500 text-xs">Strike Level:</span>
              <div className="font-medium text-gray-900">{asset.strikeLevel || "TBD"}</div>
            </div>
          </div>
        ))
      ) : (
        <div className="bg-[#e8f5ee] rounded-xl p-4 flex flex-col gap-2">
          <div className="text-sm">
            <span className="text-gray-500 text-xs">Name:</span>
            <div className="font-medium text-gray-900">TransMedics Group Inc.</div>
          </div>
          <div className="text-sm">
            <span className="text-gray-500 text-xs">Exchange:</span>
            <div className="font-medium text-gray-900">Nasdaq Stock</div>
          </div>
          <div className="text-sm">
            <span className="text-gray-500 text-xs">Bloomberg Ticker:</span>
            <div className="font-medium text-gray-900">TMDX UQ Equity</div>
          </div>
          <div className="text-sm">
            <span className="text-gray-500 text-xs">Currency:</span>
            <div className="font-medium text-gray-900">USD</div>
          </div>
          <div className="text-sm">
            <span className="text-gray-500 text-xs">Initial Fixing:</span>
            <div className="font-medium text-gray-900">TBD</div>
          </div>
          <div className="text-sm">
            <span className="text-gray-500 text-xs">Strike Level:</span>
            <div className="font-medium text-gray-900">TBD</div>
          </div>
        </div>
      )}
    </div>
    <button className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2 ml-0">Save Info</button>
  </div>
);

export default UnderlyingAssets; 