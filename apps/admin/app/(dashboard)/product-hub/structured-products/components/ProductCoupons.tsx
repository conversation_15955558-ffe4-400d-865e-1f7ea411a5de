import React from "react";

export interface Coupon {
  rate: string;
  date: string;
}

export interface ProductCouponsProps {
  coupons: Coupon[];
  nextCouponDate: string;
}

const ProductCoupons: React.FC<ProductCouponsProps> = ({ coupons, nextCouponDate }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-gray-700 mb-2">Coupons</div>
    <div className="flex flex-col gap-2">
      <div className="flex justify-between text-xs text-gray-500">
        <span>Coupon Rate</span>
        <span>Next Coupon</span>
      </div>
      <div className="flex justify-between font-semibold gap-2">
        <div className="px-2 py-1 w-24 bg-gray-50 rounded text-sm">
          {coupons && coupons.length > 0 ? coupons[0]?.rate || "8.26%" : "8.26%"}
        </div>
        <div className="px-2 py-1 w-32 bg-gray-50 rounded text-sm">
          {nextCouponDate || "25 Sept 2025"}
        </div>
      </div>
      <table className="w-full mt-2 text-xs">
        <thead>
          <tr className="text-gray-500">
            <th className="text-left">n</th>
            <th className="text-left">Coupon Rate</th>
            <th className="text-left">Coupon Payment Date</th>
          </tr>
        </thead>
        <tbody>
          {coupons && coupons.length > 0 ? (
            coupons.map((coupon, index) => (
              <tr key={index}>
                <td>{index + 1}</td>
                <td>
                  <div className="px-2 py-1 w-20 bg-gray-50 rounded text-xs">
                    {coupon.rate || "8.26%"}
                  </div>
                </td>
                <td>
                  <div className="px-2 py-1 w-36 bg-gray-50 rounded text-xs">
                    {coupon.date || "Payment Date"}
                  </div>
                </td>
              </tr>
            ))
          ) : (
            [1, 2, 3, 4].map((n, i) => (
              <tr key={n}>
                <td>{n}</td>
                <td>
                  <div className="px-2 py-1 w-20 bg-gray-50 rounded text-xs">
                    8.26%
                  </div>
                </td>
                <td>
                  <div className="px-2 py-1 w-36 bg-gray-50 rounded text-xs">
                    {[
                      "26 September 2025",
                      "26 March 2026",
                      "28 September 2026",
                      "29 March 2027"
                    ][i]}
                  </div>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
    <button className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2 ml-0">Save Info</button>
  </div>
);

export default ProductCoupons; 