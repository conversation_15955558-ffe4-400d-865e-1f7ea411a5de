import React from "react";

export interface ProductKeyRisksProps {
  risks: string[];
}

const ProductKeyRisks: React.FC<ProductKeyRisksProps> = ({ risks }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-gray-700 mb-2">Key Risks:</div>
    <div className="flex flex-col gap-2">
      {risks && risks.length > 0 ? (
        risks.map((risk, index) => (
          <div key={index} className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
            {risk}
          </div>
        ))
      ) : (
        <>
          <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
            If underlying assets fall below barrier levels
          </div>
          <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
            Early redemption (autocall) may result in reinvestment risk
          </div>
          <div className="px-2 py-1 bg-gray-50 rounded text-xs text-gray-700">
            Credit risk of the issuer
          </div>
        </>
      )}
    </div>
    <button className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2 ml-0">Save Info</button>
  </div>
);

export default ProductKeyRisks; 