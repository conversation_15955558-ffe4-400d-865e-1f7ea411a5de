import React from "react";

export interface ProductRedemptionProps {
  formula: string;
  info: string[];
}

const ProductRedemption: React.FC<ProductRedemptionProps> = ({ formula, info }) => (
  <div className="rounded-2xl p-6 bg-white/80 shadow">
    <div className="font-semibold text-gray-700 mb-2">Redemption</div>
    <div className="text-xs text-gray-700 mb-2">{formula}</div>
    <ul className="list-disc pl-5 text-xs text-gray-700">
      {info.map((item, i) => (
        <li key={i}>{item}</li>
      ))}
    </ul>
  </div>
);

export default ProductRedemption; 