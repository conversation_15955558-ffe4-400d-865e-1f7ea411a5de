// Crypto Fund Product Types based on MongoDB Schema
export interface FundDetails {
  roi: number;
  status: "Active" | "Passive" | "Closed";
  type: string;
  totalProjects: number;
  projects_logos: string[];
  lastInvestment: Date;
}

export interface PortfolioEntry {
  name: string;
  logo: string;
  brokerScore: number | null;
  price: number | null;
  privatePrice: number | null;
  publicPrice: number | null;
  publicROI: number | null;
  privateROI: number | null;
  twitterScore: number | null;
}

export interface InvestmentEntry {
  name: string;
  logo: string;
  amountInvested: number;
}

export interface HistoricalEntry {
  month: string; // e.g., "2025-01"
  volume: number;
  closePrice: number;
}

export interface CryptoFundProduct {
  product_id: string;
  name: string;
  logo: string;
  fundDetails: FundDetails;
  portfolio: PortfolioEntry[];
  investment: InvestmentEntry[];
  historical_data: HistoricalEntry[];
}

// Legacy interfaces for backward compatibility
export interface BasicInfo {
  fundName: string;
  fundManager: string;
  logoUrl?: string;
  status: "PASSIVE" | "ACTIVE" | "CLOSED";
  projectCount: number;
  roi: number;
  type: "Crypto Capital" | "Non-Crypto Capital" | "Mixed Capital";
  lastInvestmentDate: string;
  minInvestment: string;
  vintage: string;
  tenor: string;
}

export interface Essentials {
  fundSize: string;
  inception: string;
  leverage: number;
  annualizedDistributionRate: number;
  shareClass: string;
  distributions: string;
  subscriptionTiming: string;
  repurchase: string;
  totalAUM: string;
  currentPrice: string;
  priceChange: string;
}

export interface AboutSection {
  description: string;
  assetsUnderManagement: string;
  experienceYears: number;
  employees: string;
  offices: string;
  continents: number;
  investmentStrategy: string;
  riskProfile: string;
}

export interface ReasonToConsider {
  id: number;
  title: string;
  description: string;
  icon: string;
}

export interface KeyConsideration {
  description: string;
}

export interface IdealInvestor {
  criteria: string[];
}

export interface PastPerformance {
  annualizedDistributionRate: number;
  totalReturn: string;
  performanceData: {
    year: string;
    value: number;
  }[];
  historicalData: {
    month: string;
    volume: number;
    close: number;
  }[];
}

export interface CashFlowSimulator {
  defaultInvestment: string;
  cashFlowData: {
    year: number;
    investmentPhase: number;
    distributionPhase: number;
  }[];
}

export interface InvestmentDueDate {
  step: string;
  date: string;
  description: string;
}

export interface Pricing {
  valuraFee: number;
  fundManagementFee: number;
  fundIncentiveFee: number;
  accessFundFee: number;
}

export interface CryptoProject {
  name: string;
  brokerScore: number;
  price: string;
  privatePrice: string;
  publicPrice: string;
  publicROI: string;
  privateROI: string;
  twitterScore: number;
  logoUrl?: string;
}

export interface RecentInvestment {
  name: string;
  amount: string;
  date: string;
}

export interface InvestmentTeam {
  name: string;
  title: string;
  photoUrl?: string;
}

export interface InvestmentComposition {
  portfolioStrategy: {
    defi: number;
    infrastructure: number;
    gaming: number;
    ai: number;
  };
  industries: {
    blockchain: number;
    fintech: number;
    gaming: number;
    ai: number;
  };
  tokenClass: {
    governance: number;
    utility: number;
    security: number;
  };
  geography: {
    northAmerica: number;
    europe: number;
    asia: number;
    other: number;
  };
}

export interface FAQ {
  question: string;
  answer: string;
}

export interface DataRoom {
  title: string;
  url: string;
}

// Sample data based on the "Slow Ventures" fund shown in the image
export const sampleCryptoFund: CryptoFundProduct = {
  product_id: "",
  name: "Crypto Fund",
  logo: "",
  fundDetails: {
    roi: 31.5,
    status: "Passive",
    type: "Non-Crypto Capital",
    totalProjects: 36,
    projects_logos: [],
    lastInvestment: new Date("2025-06-04"),
  },
  portfolio: [
    {
      name: "SUI",
      logo: "",
      brokerScore: 7.15,
      price: 2.77,
      privatePrice: 0.25,
      publicPrice: 0.1,
      publicROI: 27.7,
      privateROI: 10.6,
      twitterScore: 305,
    },
    {
      name: "DIMO",
      logo: "",
      brokerScore: 5.59,
      price: 0.0523,
      privatePrice: 0.1184,
      publicPrice: null,
      publicROI: null,
      privateROI: 0.376,
      twitterScore: 55,
    },
    {
      name: "ECO",
      logo: "",
      brokerScore: 6.18,
      price: 0.006811,
      privatePrice: null,
      publicPrice: null,
      publicROI: null,
      privateROI: null,
      twitterScore: 114,
    },
    {
      name: "PARA",
      logo: "",
      brokerScore: 0,
      price: 0.004964,
      privatePrice: 0.05,
      publicPrice: null,
      publicROI: null,
      privateROI: 0.0993,
      twitterScore: 92,
    },
    {
      name: "XCH",
      logo: "",
      brokerScore: 5.01,
      price: 9.96,
      privatePrice: null,
      publicPrice: null,
      publicROI: null,
      privateROI: null,
      twitterScore: 96,
    },
    {
      name: "TRU",
      logo: "",
      brokerScore: 6.38,
      price: 0.02977,
      privatePrice: 0.0516,
      publicPrice: 0.0516,
      publicROI: 0.577,
      privateROI: 0.549,
      twitterScore: 150,
    },
  ],
  investment: [
    { name: "Rails", logo: "", amountInvested: 14000000 },
    { name: "Rakurai", logo: "", amountInvested: 3000000 },
    { name: "Tapestry", logo: "", amountInvested: 5750000 },
    { name: "Chaos Labs", logo: "", amountInvested: 55000000 },
    { name: "Sling Money", logo: "", amountInvested: 15000000 },
  ],
  historical_data: [
    { month: "2025-01", volume: 1200000, closePrice: 25.5 },
    { month: "2025-02", volume: 1350000, closePrice: 27.2 },
    { month: "2025-03", volume: 1100000, closePrice: 26.8 },
    { month: "2025-04", volume: 1400000, closePrice: 28.5 },
    { month: "2025-05", volume: 1250000, closePrice: 29.2 },
    { month: "2025-06", volume: 1500000, closePrice: 30.0 },
  ],
}; 