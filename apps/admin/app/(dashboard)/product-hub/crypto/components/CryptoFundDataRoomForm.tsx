import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@admin/components/ui/card";
import { DataRoom } from "../data";

interface CryptoFundDataRoomFormProps {
  dataRoom: DataRoom[];
  onSave: (dataRoom: DataRoom[]) => void;
}

export default function CryptoFundDataRoomForm({ dataRoom, onSave }: CryptoFundDataRoomFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Data Room</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Data Room form component - to be implemented</p>
      </CardContent>
    </Card>
  );
} 