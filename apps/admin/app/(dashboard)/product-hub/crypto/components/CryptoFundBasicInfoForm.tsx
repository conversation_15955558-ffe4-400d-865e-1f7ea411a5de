import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Save, Edit } from "lucide-react";

interface CryptoFundBasicInfo {
  product_id: string;
  name: string;
  logo: string;
}

interface CryptoFundBasicInfoFormProps {
  basicInfo: CryptoFundBasicInfo;
  onSave: (basicInfo: CryptoFundBasicInfo) => void;
}

export default function CryptoFundBasicInfoForm({ basicInfo, onSave }: CryptoFundBasicInfoFormProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<CryptoFundBasicInfo>(basicInfo);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormD<PERSON>(basicInfo);
    setIsEditing(false);
  };

  const handleInputChange = (field: keyof CryptoFundBasicInfo, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Basic Information</CardTitle>
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  className="admin_green_gradient hover:admin_green_gradient_hover text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Product ID */}
          <div className="space-y-2">
            <label htmlFor="product_id" className="text-sm font-medium text-gray-700">Product ID</label>
            <Input
              id="product_id"
              value={formData.product_id}
              onChange={(e) => handleInputChange('product_id', e.target.value)}
              disabled={!isEditing}
              placeholder="Enter product ID"
            />
          </div>

          {/* Fund Name */}
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium text-gray-700">Fund Name</label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              disabled={!isEditing}
              placeholder="Enter fund name"
            />
          </div>

          {/* Logo URL */}
          <div className="space-y-2 md:col-span-2">
            <label htmlFor="logo" className="text-sm font-medium text-gray-700">Logo URL</label>
            <Input
              id="logo"
              value={formData.logo}
              onChange={(e) => handleInputChange('logo', e.target.value)}
              disabled={!isEditing}
              placeholder="Enter logo URL"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 