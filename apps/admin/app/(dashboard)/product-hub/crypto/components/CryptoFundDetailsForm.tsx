import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { CustomSelect } from "@admin/components/ui/CustomSelect";
import { Save, Edit, Plus, X } from "lucide-react";
import { FundDetails } from "../data";

interface CryptoFundDetailsFormProps {
  fundDetails: FundDetails;
  onSave: (fundDetails: FundDetails) => void;
}

export default function CryptoFundDetailsForm({ fundDetails, onSave }: CryptoFundDetailsFormProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<FundDetails>(fundDetails);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormD<PERSON>(fundDetails);
    setIsEditing(false);
  };

  const handleInputChange = (field: keyof FundDetails, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleProjectLogoAdd = () => {
    setFormData(prev => ({
      ...prev,
      projects_logos: [...prev.projects_logos, ""]
    }));
  };

  const handleProjectLogoChange = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      projects_logos: prev.projects_logos.map((logo, i) => i === index ? value : logo)
    }));
  };

  const handleProjectLogoRemove = (index: number) => {
    setFormData(prev => ({
      ...prev,
      projects_logos: prev.projects_logos.filter((_, i) => i !== index)
    }));
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Fund Details</CardTitle>
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  className="admin_green_gradient hover:admin_green_gradient_hover text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* ROI */}
          <div className="space-y-2">
            <label htmlFor="roi" className="text-sm font-medium text-gray-700">ROI (x)</label>
            <Input
              id="roi"
              type="number"
              step="0.1"
              value={formData.roi}
              onChange={(e) => handleInputChange('roi', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="Enter ROI"
            />
          </div>

          {/* Status */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Status</label>
            <CustomSelect
              options={["Active", "Passive", "Closed"]}
              value={formData.status}
              onChange={(value) => handleInputChange('status', value)}
              placeholder="Select status"
              disabled={!isEditing}
            />
          </div>

          {/* Type */}
          <div className="space-y-2">
            <label htmlFor="type" className="text-sm font-medium text-gray-700">Type</label>
            <Input
              id="type"
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value)}
              disabled={!isEditing}
              placeholder="Enter fund type"
            />
          </div>

          {/* Total Projects */}
          <div className="space-y-2">
            <label htmlFor="totalProjects" className="text-sm font-medium text-gray-700">Total Projects</label>
            <Input
              id="totalProjects"
              type="number"
              value={formData.totalProjects}
              onChange={(e) => handleInputChange('totalProjects', parseInt(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="Enter total projects"
            />
          </div>

          {/* Last Investment Date */}
          <div className="space-y-2">
            <label htmlFor="lastInvestment" className="text-sm font-medium text-gray-700">Last Investment Date</label>
            <Input
              id="lastInvestment"
              type="date"
              value={formData.lastInvestment ? new Date(formData.lastInvestment).toISOString().split('T')[0] : ''}
              onChange={(e) => handleInputChange('lastInvestment', new Date(e.target.value))}
              disabled={!isEditing}
            />
          </div>
        </div>

        {/* Project Logos */}
        <div className="mt-6 space-y-4">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700">Project Logos URLs</label>
            {isEditing && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleProjectLogoAdd}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Logo
              </Button>
            )}
          </div>
          
          <div className="space-y-2">
            {formData.projects_logos.map((logo, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Input
                  value={logo}
                  onChange={(e) => handleProjectLogoChange(index, e.target.value)}
                  disabled={!isEditing}
                  placeholder="Enter project logo URL"
                />
                {isEditing && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleProjectLogoRemove(index)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
            {formData.projects_logos.length === 0 && !isEditing && (
              <p className="text-gray-500 text-sm">No project logos added</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 