import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Save, Edit, Plus, X, Trash2 } from "lucide-react";
import { PortfolioEntry } from "../data";

interface CryptoFundPortfolioFormProps {
  portfolio: PortfolioEntry[];
  onSave: (portfolio: PortfolioEntry[]) => void;
}

export default function CryptoFundPortfolioForm({ portfolio, onSave }: CryptoFundPortfolioFormProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<PortfolioEntry[]>(portfolio);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(portfolio);
    setIsEditing(false);
  };

  const handleAddEntry = () => {
    const newEntry: PortfolioEntry = {
      name: "",
      logo: "",
      brokerScore: null,
      price: null,
      privatePrice: null,
      publicPrice: null,
      publicROI: null,
      privateROI: null,
      twitterScore: null,
    };
    setFormData(prev => [...prev, newEntry]);
  };

  const handleRemoveEntry = (index: number) => {
    setFormData(prev => prev.filter((_, i) => i !== index));
  };

  const handleEntryChange = (index: number, field: keyof PortfolioEntry, value: any) => {
    setFormData(prev => prev.map((entry, i) => 
      i === index ? { ...entry, [field]: value } : entry
    ));
  };

  const handleNumberInputChange = (index: number, field: keyof PortfolioEntry, value: string) => {
    const numValue = value === "" ? null : parseFloat(value);
    handleEntryChange(index, field, numValue);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Portfolio</CardTitle>
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  className="admin_green_gradient hover:admin_green_gradient_hover text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {formData.map((entry, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Portfolio Entry {index + 1}</h4>
                {isEditing && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveEntry(index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Name */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Name</label>
                  <Input
                    value={entry.name}
                    onChange={(e) => handleEntryChange(index, 'name', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter project name"
                  />
                </div>

                {/* Logo */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Logo URL</label>
                  <Input
                    value={entry.logo}
                    onChange={(e) => handleEntryChange(index, 'logo', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter logo URL"
                  />
                </div>

                {/* Broker Score */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Broker Score</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={entry.brokerScore || ""}
                    onChange={(e) => handleNumberInputChange(index, 'brokerScore', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter broker score"
                  />
                </div>

                {/* Price */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Price</label>
                  <Input
                    type="number"
                    step="0.000001"
                    value={entry.price || ""}
                    onChange={(e) => handleNumberInputChange(index, 'price', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter price"
                  />
                </div>

                {/* Private Price */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Private Price</label>
                  <Input
                    type="number"
                    step="0.000001"
                    value={entry.privatePrice || ""}
                    onChange={(e) => handleNumberInputChange(index, 'privatePrice', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter private price"
                  />
                </div>

                {/* Public Price */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Public Price</label>
                  <Input
                    type="number"
                    step="0.000001"
                    value={entry.publicPrice || ""}
                    onChange={(e) => handleNumberInputChange(index, 'publicPrice', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter public price"
                  />
                </div>

                {/* Public ROI */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Public ROI</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={entry.publicROI || ""}
                    onChange={(e) => handleNumberInputChange(index, 'publicROI', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter public ROI"
                  />
                </div>

                {/* Private ROI */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Private ROI</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={entry.privateROI || ""}
                    onChange={(e) => handleNumberInputChange(index, 'privateROI', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter private ROI"
                  />
                </div>

                {/* Twitter Score */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Twitter Score</label>
                  <Input
                    type="number"
                    value={entry.twitterScore || ""}
                    onChange={(e) => handleNumberInputChange(index, 'twitterScore', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter Twitter score"
                  />
                </div>
              </div>
            </div>
          ))}
          
          {isEditing && (
            <Button
              type="button"
              variant="outline"
              onClick={handleAddEntry}
              className="w-full"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Portfolio Entry
            </Button>
          )}
          
          {formData.length === 0 && !isEditing && (
            <p className="text-gray-500 text-center py-4">No portfolio entries added</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 