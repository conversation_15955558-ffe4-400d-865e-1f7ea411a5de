import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@admin/components/ui/card";
import { PastPerformance } from "../data";

interface CryptoFundPerformanceFormProps {
  pastPerformance: PastPerformance;
  onSave: (pastPerformance: PastPerformance) => void;
}

export default function CryptoFundPerformanceForm({ pastPerformance, onSave }: CryptoFundPerformanceFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Performance</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Performance form component - to be implemented</p>
      </CardContent>
    </Card>
  );
} 