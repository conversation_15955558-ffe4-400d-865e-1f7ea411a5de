import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@admin/components/ui/card";
import { But<PERSON> } from "@admin/components/ui/button";
import { ArrowLeft, Save } from "lucide-react";
import { CryptoFundProduct } from "../data";

interface CryptoFundPreviewProps {
  cryptoFund: CryptoFundProduct;
  onBack: () => void;
  onSubmit: () => void;
  isSubmitting: boolean;
}

export default function CryptoFundPreview({ cryptoFund, onBack, onSubmit, isSubmitting }: CryptoFundPreviewProps) {
  return (
    <div className="w-full h-full overflow-y-auto bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Form</span>
            </Button>
            <div className="h-6 w-px bg-gray-300" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Crypto Fund Preview</h1>
              <p className="text-sm text-gray-600">Review your crypto fund product before saving</p>
            </div>
          </div>
          <Button
            size="sm"
            onClick={onSubmit}
            disabled={isSubmitting}
            className="admin_green_gradient hover:admin_green_gradient_hover text-white flex items-center space-x-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Save Product</span>
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="p-6 space-y-6">
        {/* Basic Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm font-medium text-gray-500">Product ID:</span>
                <p className="text-lg font-semibold text-green-600">{cryptoFund.product_id}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Fund Name:</span>
                <p className="text-lg font-semibold text-green-600">{cryptoFund.name}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Logo URL:</span>
                <p className="text-lg break-all">{cryptoFund.logo || "Not provided"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fund Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Fund Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm font-medium text-gray-500">ROI:</span>
                <p className="text-lg font-semibold text-green-600">{cryptoFund.fundDetails.roi}x</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Status:</span>
                <p className="text-lg">{cryptoFund.fundDetails.status}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Type:</span>
                <p className="text-lg">{cryptoFund.fundDetails.type}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Total Projects:</span>
                <p className="text-lg">{cryptoFund.fundDetails.totalProjects} projects</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Last Investment:</span>
                <p className="text-lg">{cryptoFund.fundDetails.lastInvestment ? new Date(cryptoFund.fundDetails.lastInvestment).toLocaleDateString() : "Not set"}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Project Logos:</span>
                <p className="text-lg">{cryptoFund.fundDetails.projects_logos.length} logos</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Portfolio */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Portfolio</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Name</th>
                    <th className="text-left py-2">Broker Score</th>
                    <th className="text-left py-2">Price</th>
                    <th className="text-left py-2">Private Price</th>
                    <th className="text-left py-2">Public Price</th>
                    <th className="text-left py-2">Private ROI</th>
                    <th className="text-left py-2">Public ROI</th>
                    <th className="text-left py-2">Twitter Score</th>
                  </tr>
                </thead>
                <tbody>
                  {cryptoFund.portfolio.slice(0, 5).map((entry, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-2 font-medium">{entry.name}</td>
                      <td className="py-2">{entry.brokerScore || "N/A"}</td>
                      <td className="py-2">{entry.price || "N/A"}</td>
                      <td className="py-2">{entry.privatePrice || "N/A"}</td>
                      <td className="py-2">{entry.publicPrice || "N/A"}</td>
                      <td className="py-2">{entry.privateROI || "N/A"}</td>
                      <td className="py-2">{entry.publicROI || "N/A"}</td>
                      <td className="py-2">{entry.twitterScore || "N/A"}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {cryptoFund.portfolio.length > 5 && (
                <p className="text-sm text-gray-500 mt-2">
                  Showing first 5 entries of {cryptoFund.portfolio.length} total
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Investments */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Recent Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {cryptoFund.investment.map((investment, index) => (
                <div key={index} className="bg-green-50 p-4 rounded-lg">
                  <p className="font-semibold text-green-800">{investment.name}</p>
                  <p className="text-green-600">${investment.amountInvested.toLocaleString()}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Historical Data */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Historical Data</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Month</th>
                    <th className="text-left py-2">Volume</th>
                    <th className="text-left py-2">Close Price</th>
                  </tr>
                </thead>
                <tbody>
                  {cryptoFund.historical_data.slice(0, 6).map((entry, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-2 font-medium">{entry.month}</td>
                      <td className="py-2">{entry.volume.toLocaleString()}</td>
                      <td className="py-2">${entry.closePrice}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {cryptoFund.historical_data.length > 6 && (
                <p className="text-sm text-gray-500 mt-2">
                  Showing first 6 entries of {cryptoFund.historical_data.length} total
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 