import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { InvestmentComposition } from "../data";

interface CryptoFundCompositionFormProps {
  investmentComposition: InvestmentComposition;
  onSave: (investmentComposition: InvestmentComposition) => void;
}

export default function CryptoFundCompositionForm({ investmentComposition, onSave }: CryptoFundCompositionFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Investment Composition</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Composition form component - to be implemented</p>
      </CardContent>
    </Card>
  );
} 