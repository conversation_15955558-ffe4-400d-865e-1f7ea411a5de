import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Save, Edit, Plus, Trash2 } from "lucide-react";
import { HistoricalEntry } from "../data";

interface CryptoFundHistoricalFormProps {
  historical_data: HistoricalEntry[];
  onSave: (historical_data: HistoricalEntry[]) => void;
}

export default function CryptoFundHistoricalForm({ historical_data, onSave }: CryptoFundHistoricalFormProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<HistoricalEntry[]>(historical_data);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(historical_data);
    setIsEditing(false);
  };

  const handleAddEntry = () => {
    const newEntry: HistoricalEntry = {
      month: "",
      volume: 0,
      closePrice: 0,
    };
    setFormData(prev => [...prev, newEntry]);
  };

  const handleRemoveEntry = (index: number) => {
    setFormData(prev => prev.filter((_, i) => i !== index));
  };

  const handleEntryChange = (index: number, field: keyof HistoricalEntry, value: any) => {
    setFormData(prev => prev.map((entry, i) => 
      i === index ? { ...entry, [field]: value } : entry
    ));
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Historical Data</CardTitle>
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  className="admin_green_gradient hover:admin_green_gradient_hover text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {formData.map((entry, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Historical Entry {index + 1}</h4>
                {isEditing && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveEntry(index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Month */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Month (YYYY-MM)</label>
                  <Input
                    value={entry.month}
                    onChange={(e) => handleEntryChange(index, 'month', e.target.value)}
                    disabled={!isEditing}
                    placeholder="e.g., 2025-01"
                  />
                </div>

                {/* Volume */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Volume</label>
                  <Input
                    type="number"
                    value={entry.volume}
                    onChange={(e) => handleEntryChange(index, 'volume', parseFloat(e.target.value) || 0)}
                    disabled={!isEditing}
                    placeholder="Enter volume"
                  />
                </div>

                {/* Close Price */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Close Price</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={entry.closePrice}
                    onChange={(e) => handleEntryChange(index, 'closePrice', parseFloat(e.target.value) || 0)}
                    disabled={!isEditing}
                    placeholder="Enter close price"
                  />
                </div>
              </div>
            </div>
          ))}
          
          {isEditing && (
            <Button
              type="button"
              variant="outline"
              onClick={handleAddEntry}
              className="w-full"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Historical Entry
            </Button>
          )}
          
          {formData.length === 0 && !isEditing && (
            <p className="text-gray-500 text-center py-4">No historical data entries added</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 