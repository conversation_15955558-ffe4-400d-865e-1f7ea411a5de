import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@admin/components/ui/card";
import { AboutSection } from "../data";

interface CryptoFundAboutFormProps {
  aboutSection: AboutSection;
  onSave: (aboutSection: AboutSection) => void;
}

export default function CryptoFundAboutForm({ aboutSection, onSave }: CryptoFundAboutFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">About</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">About form component - to be implemented</p>
      </CardContent>
    </Card>
  );
} 