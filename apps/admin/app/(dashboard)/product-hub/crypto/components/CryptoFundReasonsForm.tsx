import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { ReasonToConsider } from "../data";

interface CryptoFundReasonsFormProps {
  reasonsToConsider: ReasonToConsider[];
  onSave: (reasonsToConsider: ReasonToConsider[]) => void;
}

export default function CryptoFundReasonsForm({ reasonsToConsider, onSave }: CryptoFundReasonsFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Reasons to Consider</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Reasons form component - to be implemented</p>
      </CardContent>
    </Card>
  );
} 