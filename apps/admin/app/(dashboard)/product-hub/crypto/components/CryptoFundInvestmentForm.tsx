import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Save, Edit, Plus, Trash2 } from "lucide-react";
import { InvestmentEntry } from "../data";

interface CryptoFundInvestmentFormProps {
  investment: InvestmentEntry[];
  onSave: (investment: InvestmentEntry[]) => void;
}

export default function CryptoFundInvestmentForm({ investment, onSave }: CryptoFundInvestmentFormProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<InvestmentEntry[]>(investment);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(investment);
    setIsEditing(false);
  };

  const handleAddEntry = () => {
    const newEntry: InvestmentEntry = {
      name: "",
      logo: "",
      amountInvested: 0,
    };
    setFormData(prev => [...prev, newEntry]);
  };

  const handleRemoveEntry = (index: number) => {
    setFormData(prev => prev.filter((_, i) => i !== index));
  };

  const handleEntryChange = (index: number, field: keyof InvestmentEntry, value: any) => {
    setFormData(prev => prev.map((entry, i) => 
      i === index ? { ...entry, [field]: value } : entry
    ));
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Recent Investments</CardTitle>
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  className="admin_green_gradient hover:admin_green_gradient_hover text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {formData.map((entry, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Investment {index + 1}</h4>
                {isEditing && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveEntry(index)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Name */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Company Name</label>
                  <Input
                    value={entry.name}
                    onChange={(e) => handleEntryChange(index, 'name', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter company name"
                  />
                </div>

                {/* Logo */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Logo URL</label>
                  <Input
                    value={entry.logo}
                    onChange={(e) => handleEntryChange(index, 'logo', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Enter logo URL"
                  />
                </div>

                {/* Amount Invested */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Amount Invested ($)</label>
                  <Input
                    type="number"
                    value={entry.amountInvested}
                    onChange={(e) => handleEntryChange(index, 'amountInvested', parseFloat(e.target.value) || 0)}
                    disabled={!isEditing}
                    placeholder="Enter amount invested"
                  />
                </div>
              </div>
            </div>
          ))}
          
          {isEditing && (
            <Button
              type="button"
              variant="outline"
              onClick={handleAddEntry}
              className="w-full"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Investment Entry
            </Button>
          )}
          
          {formData.length === 0 && !isEditing && (
            <p className="text-gray-500 text-center py-4">No investment entries added</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 