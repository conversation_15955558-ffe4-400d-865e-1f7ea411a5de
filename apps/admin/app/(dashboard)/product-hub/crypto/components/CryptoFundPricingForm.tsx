import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { Pricing } from "../data";

interface CryptoFundPricingFormProps {
  pricing: Pricing;
  onSave: (pricing: Pricing) => void;
}

export default function CryptoFundPricingForm({ pricing, onSave }: CryptoFundPricingFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Pricing</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Pricing form component - to be implemented</p>
      </CardContent>
    </Card>
  );
} 