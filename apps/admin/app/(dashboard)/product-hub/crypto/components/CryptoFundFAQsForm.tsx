import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@admin/components/ui/card";
import { FAQ } from "../data";

interface CryptoFundFAQsFormProps {
  faqs: FAQ[];
  onSave: (faqs: FAQ[]) => void;
}

export default function CryptoFundFAQsForm({ faqs, onSave }: CryptoFundFAQsFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">FAQs</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">FAQs form component - to be implemented</p>
      </CardContent>
    </Card>
  );
} 