import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { InvestmentTeam } from "../data";

interface CryptoFundTeamFormProps {
  investmentTeam: InvestmentTeam[];
  onSave: (investmentTeam: InvestmentTeam[]) => void;
}

export default function CryptoFundTeamForm({ investmentTeam, onSave }: CryptoFundTeamFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Investment Team</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Team form component - to be implemented</p>
      </CardContent>
    </Card>
  );
} 