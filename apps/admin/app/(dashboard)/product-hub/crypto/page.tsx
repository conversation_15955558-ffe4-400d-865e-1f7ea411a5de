"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from "@admin/components/ui/button";
import { ArrowLeft, Download, MessageCircle, Save, Eye, Send, Play, ExternalLink, ChevronDown, ChevronUp } from "lucide-react";
import { sampleCryptoFund, CryptoFundProduct } from "./data";
import {
  CryptoFundPreview,
  CryptoFundBasicInfoForm,
  CryptoFundDetailsForm,
  CryptoFundPortfolioForm,
  CryptoFundInvestmentForm,
  CryptoFundHistoricalForm,
  CryptoFundEssentialsForm,
  CryptoFundAboutForm,
  CryptoFundReasonsForm,
  CryptoFundPerformanceForm,
  CryptoFundPricingForm,
  CryptoFundTeamForm,
  CryptoFundCompositionForm,
  CryptoFundFAQsForm,
  CryptoFundDataRoomForm
} from "./components";
import axios from 'axios';
import { createCryptoFund } from '@admin/app/lib/productApiService';

export default function CryptoFundDetailPage() {
  const searchParams = useSearchParams();
  const [mode, setMode] = useState<'form' | 'review'>('form');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cryptoFund, setCryptoFund] = useState<CryptoFundProduct>(sampleCryptoFund);
  const [vendor_id, setVendorId] = useState("");
  const router = useRouter();

  useEffect(() => {
    const vendorParam = searchParams.get('vendor');
    if (vendorParam) {
      setVendorId(vendorParam);
      setCryptoFund(prev => ({
        ...prev,
        vendor_id: vendorParam
      }));
    }
  }, [searchParams]);

  const handleBasicInfoSave = (basicInfo: { product_id: string; name: string; logo: string }) => {
    setCryptoFund(prev => ({ 
      ...prev, 
      product_id: basicInfo.product_id,
      name: basicInfo.name,
      logo: basicInfo.logo
    }));
  };

  const handleFundDetailsSave = (fundDetails: CryptoFundProduct['fundDetails']) => {
    setCryptoFund(prev => ({ ...prev, fundDetails }));
  };

  const handlePortfolioSave = (portfolio: CryptoFundProduct['portfolio']) => {
    setCryptoFund(prev => ({ ...prev, portfolio }));
  };

  const handleInvestmentSave = (investment: CryptoFundProduct['investment']) => {
    setCryptoFund(prev => ({ ...prev, investment }));
  };

  const handleHistoricalDataSave = (historical_data: CryptoFundProduct['historical_data']) => {
    setCryptoFund(prev => ({ ...prev, historical_data }));
  };

  // Legacy handlers for backward compatibility
  const handleEssentialsSave = (essentials: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const handleAboutSave = (aboutSection: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const handleReasonsSave = (reasonsToConsider: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const handlePerformanceSave = (pastPerformance: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const handlePricingSave = (pricing: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const handleTeamSave = (investmentTeam: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const handleCompositionSave = (investmentComposition: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const handleFAQsSave = (faqs: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const handleDataRoomSave = (dataRoom: any) => {
    // This is kept for backward compatibility but not used in new schema
  };

  const validateForm = () => {
    const errors = [];
    
    // Check required fields from new schema
    if (!cryptoFund.product_id) errors.push("Product ID is required");
    if (!cryptoFund.name) errors.push("Fund Name is required");
    if (!cryptoFund.fundDetails?.roi) errors.push("ROI is required");
    if (!cryptoFund.fundDetails?.status) errors.push("Status is required");
    if (!cryptoFund.fundDetails?.type) errors.push("Type is required");
    if (!cryptoFund.fundDetails?.totalProjects) errors.push("Total Projects is required");
    
    return errors;
  };

  const getFormProgress = () => {
    const errors = validateForm();
    const totalFields = 6; // Number of required fields in new schema
    const filledFields = totalFields - errors.length;
    return Math.max(0, Math.min(100, (filledFields / totalFields) * 100));
  };

  const handleReview = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    setMode('review');
  };

  const handleSubmit = async () => {
    console.log('Starting submission...');
    setIsSubmitting(true);
    try {
      if (!vendor_id) {
        throw new Error('Vendor ID is required');
      }

      console.log('Preparing data for submission...');
      const cryptoFundDataToSave = {
        vendor_id,
        name: cryptoFund.name,
        logo: cryptoFund.logo,
        fundDetails: {
          roi: cryptoFund.fundDetails?.roi,
          status: cryptoFund.fundDetails?.status,
          type: cryptoFund.fundDetails?.type,
          totalProjects: cryptoFund.fundDetails?.totalProjects,
          projects_logos: cryptoFund.fundDetails?.projects_logos || [],
          lastInvestment: cryptoFund.fundDetails?.lastInvestment
        },
        portfolio: cryptoFund.portfolio?.map(item => ({
          name: item.name,
          logo: item.logo,
          brokerScore: item.brokerScore,
          price: item.price,
          privatePrice: item.privatePrice,
          publicPrice: item.publicPrice,
          publicROI: item.publicROI,
          privateROI: item.privateROI,
          twitterScore: item.twitterScore
        })) || [],
        investment: cryptoFund.investment?.map(item => ({
          name: item.name,
          logo: item.logo,
          amountInvested: item.amountInvested
        })) || [],
        historical_data: cryptoFund.historical_data?.map(item => ({
          month: item.month,
          volume: item.volume,
          closePrice: item.closePrice
        })) || []
      };

      console.log('Sending data to API:', cryptoFundDataToSave);
      const response: any = await createCryptoFund(cryptoFundDataToSave);
      console.log('API Response:', response);

      if (response && response.statusCode === 201) {
        alert('Crypto Fund product created successfully!');
        setMode('form');
        router.push('/product-hub/');
      } else if (response && response.id) {
        alert('Crypto Fund product created successfully!');
        setMode('form');
        router.push('/product-hub/');
      } else {
        throw new Error('Failed to create crypto fund product');
      }
    } catch (error: any) {
      console.error('Error saving Crypto Fund product:', error);
      alert(error.message || 'Failed to save Crypto Fund product. Please try again.');
    } finally {
      console.log('Submission completed');
      setIsSubmitting(false);
    }
  };

  const handleSubmitWithValidation = async () => {
    console.log('Starting validation...');
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    if (!vendor_id) {
      alert('Vendor ID is required. Please ensure you have accessed this page with a valid vendor parameter.');
      return;
    }
    console.log('Validation passed, proceeding with submission...');
    await handleSubmit();
  };

  if (mode === 'review') {
    return (
      <div className="w-full h-full overflow-y-auto bg-gray-50">
        <CryptoFundPreview 
          cryptoFund={cryptoFund}
          onBack={() => setMode('form')}
          onSubmit={handleSubmitWithValidation}
          isSubmitting={isSubmitting}
        />
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/product-hub/')}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Product Hub
            </Button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">Crypto Fund</h1>
              <p className="text-sm text-gray-500">Create and manage crypto fund products</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getFormProgress()}%` }}
                />
              </div>
              <span className="text-sm text-gray-500">{Math.round(getFormProgress())}%</span>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleReview}
              disabled={validateForm().length > 0}
            >
              <Eye className="w-4 h-4 mr-2" />
              Review
            </Button>
            
            <Button
              size="sm"
              onClick={handleSubmitWithValidation}
              disabled={isSubmitting || validateForm().length > 0}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Fund
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6 space-y-6">
          <CryptoFundBasicInfoForm 
            basicInfo={{
              product_id: cryptoFund.product_id,
              name: cryptoFund.name,
              logo: cryptoFund.logo
            }}
            onSave={handleBasicInfoSave}
          />
          
          <CryptoFundDetailsForm 
            fundDetails={cryptoFund.fundDetails}
            onSave={handleFundDetailsSave}
          />
          
          <CryptoFundPortfolioForm 
            portfolio={cryptoFund.portfolio}
            onSave={handlePortfolioSave}
          />
          
          <CryptoFundInvestmentForm 
            investment={cryptoFund.investment}
            onSave={handleInvestmentSave}
          />
          
          <CryptoFundHistoricalForm 
            historical_data={cryptoFund.historical_data}
            onSave={handleHistoricalDataSave}
          />
        </div>
      </div>
    </div>
  );
} 