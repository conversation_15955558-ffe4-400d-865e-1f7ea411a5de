# Crypto Fund Form - MongoDB Schema Integration

This directory contains the crypto fund editing form that has been updated to match the MongoDB schema structure. The form now properly aligns with the database schema for creating and editing crypto fund products.

## Schema Structure

The crypto fund form follows this MongoDB schema structure:

```typescript
interface CryptoFundProduct {
  product_id: string;           // Required, unique identifier
  name: string;                 // Fund name
  logo: string;                 // Logo URL
  fundDetails: FundDetails;     // Fund-specific details
  portfolio: PortfolioEntry[];  // Portfolio entries
  investment: InvestmentEntry[]; // Investment entries
  historical_data: HistoricalEntry[]; // Historical data
}
```

### Fund Details Schema
```typescript
interface FundDetails {
  roi: number;                    // Return on Investment
  status: "Active" | "Passive" | "Closed";
  type: string;                   // Fund type
  totalProjects: number;          // Total number of projects
  projects_logos: string[];       // Array of project logo URLs
  lastInvestment: Date;           // Last investment date
}
```

### Portfolio Entry Schema
```typescript
interface PortfolioEntry {
  name: string;                   // Project name
  logo: string;                   // Project logo URL
  brokerScore: number | null;     // Broker score
  price: number | null;           // Current price
  privatePrice: number | null;    // Private price
  publicPrice: number | null;     // Public price
  publicROI: number | null;       // Public ROI
  privateROI: number | null;      // Private ROI
  twitterScore: number | null;    // Twitter score
}
```

### Investment Entry Schema
```typescript
interface InvestmentEntry {
  name: string;                   // Company name
  logo: string;                   // Company logo URL
  amountInvested: number;         // Amount invested in USD
}
```

### Historical Entry Schema
```typescript
interface HistoricalEntry {
  month: string;                  // Month in YYYY-MM format
  volume: number;                 // Trading volume
  closePrice: number;             // Closing price
}
```

## Form Components

### 1. CryptoFundBasicInfoForm
- **Purpose**: Edit basic fund information
- **Fields**: Product ID, Fund Name, Logo URL
- **Location**: `components/CryptoFundBasicInfoForm.tsx`

### 2. CryptoFundDetailsForm
- **Purpose**: Edit fund details and specifications
- **Fields**: ROI, Status, Type, Total Projects, Last Investment Date, Project Logos
- **Location**: `components/CryptoFundDetailsForm.tsx`

### 3. CryptoFundPortfolioForm
- **Purpose**: Manage portfolio entries
- **Features**: Add/remove portfolio entries, edit all portfolio fields
- **Location**: `components/CryptoFundPortfolioForm.tsx`

### 4. CryptoFundInvestmentForm
- **Purpose**: Manage investment entries
- **Features**: Add/remove investment entries, edit investment details
- **Location**: `components/CryptoFundInvestmentForm.tsx`

### 5. CryptoFundHistoricalForm
- **Purpose**: Manage historical data entries
- **Features**: Add/remove historical entries, edit volume and price data
- **Location**: `components/CryptoFundHistoricalForm.tsx`

## Form Features

### Edit Mode
- Each form component has an edit mode that can be toggled
- In edit mode, all fields become editable
- Save/Cancel buttons appear when editing

### Validation
- Required field validation
- Progress tracking (percentage complete)
- Form submission validation

### Data Management
- Add/remove entries for arrays (portfolio, investments, historical data)
- Real-time form state management
- Data persistence between form sections

## API Integration

The form submits data to the following endpoint:
```
POST http://localhost:4000/api/admin/products/crypto-funds
```

### Request Payload
```typescript
{
  product_id: string,
  name: string,
  logo: string,
  fundDetails: FundDetails,
  portfolio: PortfolioEntry[],
  investment: InvestmentEntry[],
  historical_data: HistoricalEntry[]
}
```

## Usage

1. Navigate to the crypto fund form page
2. Fill in the basic information (Product ID, Name, Logo)
3. Configure fund details (ROI, Status, Type, etc.)
4. Add portfolio entries with project information
5. Add investment entries with company details
6. Add historical data entries
7. Review the complete form
8. Submit to create the crypto fund product

## File Structure

```
crypto/
├── page.tsx                    # Main form page
├── data.ts                     # Type definitions and sample data
├── README.md                   # This documentation
└── components/
    ├── index.ts               # Component exports
    ├── CryptoFundBasicInfoForm.tsx
    ├── CryptoFundDetailsForm.tsx
    ├── CryptoFundPortfolioForm.tsx
    ├── CryptoFundInvestmentForm.tsx
    ├── CryptoFundHistoricalForm.tsx
    ├── CryptoFundPreview.tsx
    └── [legacy components]    # Other form components
```

## Migration Notes

The form has been migrated from a complex multi-section structure to a simplified MongoDB-aligned structure. Legacy components are still available for backward compatibility but are not actively used in the new schema.

### Key Changes
- Simplified data structure matching MongoDB schema
- Removed complex nested objects
- Streamlined form validation
- Improved data type safety
- Better API integration

## Development

To add new fields or modify the form:

1. Update the type definitions in `data.ts`
2. Modify the corresponding form component
3. Update the validation logic in `page.tsx`
4. Test the form submission
5. Update this documentation

## Validation Rules

- Product ID: Required, unique
- Fund Name: Required
- ROI: Required, numeric
- Status: Required, must be "Active", "Passive", or "Closed"
- Type: Required
- Total Projects: Required, numeric
- Portfolio entries: Optional, but if present must have valid data
- Investment entries: Optional, but if present must have valid data
- Historical data: Optional, but if present must have valid data 