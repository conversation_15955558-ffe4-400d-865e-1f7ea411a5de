export const productTypeRoutes = {
  'Structure Products': '/product-hub/structured-products/',
  'Mutual Funds': '/product-hub/mutual-funds/',
  'ETFs': '/product-hub/etfs/',
  'Private Market': '/product-hub/private-market/',
  'REITs': '/product-hub/reits/',
  'Crypto': '/product-hub/crypto/',
  'Crypto ETF': '/product-hub/crypto-etf/',
  'Bonds': '/product-hub/bonds/',
  'Equity': '/product-hub/equity/'
} as const;

export const getProductTypePath = (productType: string): string => {
  return productTypeRoutes[productType as keyof typeof productTypeRoutes] || '/product-hub';
};
