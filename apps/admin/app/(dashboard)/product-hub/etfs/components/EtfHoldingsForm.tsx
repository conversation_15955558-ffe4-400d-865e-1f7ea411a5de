"use client";
import React from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@admin/components/ui/card";
import { Input } from "@admin/components/ui/input";
import { Button } from "@admin/components/ui/button";
import { Holding } from "../data";

interface EtfHoldingsFormProps {
  holdings: Holding[];
  onSave: (holdings: Holding[]) => void;
}

export default function EtfHoldingsForm({ holdings, onSave }: EtfHoldingsFormProps) {
  const [localHoldings, setLocalHoldings] = React.useState<Holding[]>(holdings);

  React.useEffect(() => { 
    setLocalHoldings(holdings); 
  }, [holdings]);

  const handleChange = (idx: number, field: keyof Holding, value: any) => {
    const updated = localHoldings.map((h, i) =>
      i === idx ? { ...h, [field]: value } : h
    );
    setLocalHoldings(updated);
    onSave(updated);
  };

  const handleAdd = () => {
    const newHolding: Holding = {
      companyName: "",
      ticker: "",
      percentage: 0,
      shares: 0,
      marketValue: 0,
      sector: "",
      logo: "",
    };
    const updated = [...localHoldings, newHolding];
    setLocalHoldings(updated);
    onSave(updated);
  };

  const handleRemove = (idx: number) => {
    const updated = localHoldings.filter((_, i) => i !== idx);
    setLocalHoldings(updated);
    onSave(updated);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-sm font-medium">Holdings</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {localHoldings.map((holding, idx) => (
          <div key={idx} className="grid grid-cols-1 md:grid-cols-7 gap-2 items-end">
            <Input placeholder="Company Name" value={holding.companyName} onChange={e => handleChange(idx, "companyName", e.target.value)} />
            <Input placeholder="Ticker" value={holding.ticker} onChange={e => handleChange(idx, "ticker", e.target.value)} />
            <Input placeholder="%" type="number" value={holding.percentage} onChange={e => handleChange(idx, "percentage", parseFloat(e.target.value) || 0)} />
            <Input placeholder="Shares" type="number" value={holding.shares} onChange={e => handleChange(idx, "shares", parseInt(e.target.value) || 0)} />
            <Input placeholder="Market Value" type="number" value={holding.marketValue} onChange={e => handleChange(idx, "marketValue", parseFloat(e.target.value) || 0)} />
            <Input placeholder="Sector" value={holding.sector} onChange={e => handleChange(idx, "sector", e.target.value)} />
            <Input placeholder="Logo URL" value={holding.logo} onChange={e => handleChange(idx, "logo", e.target.value)} />
            <Button variant="destructive" onClick={() => handleRemove(idx)}>Remove</Button>
          </div>
        ))}
        <Button onClick={handleAdd}>Add Holding</Button>
      </CardContent>
    </Card>
  );
} 