"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { BasicInfo } from "../data";

interface EtfBasicInfoFormProps {
  basicInfo: BasicInfo;
  onSave: (basicInfo: BasicInfo) => void;
}

export default function EtfBasicInfoForm({ basicInfo, onSave }: EtfBasicInfoFormProps) {
  const [formData, setFormData] = useState<BasicInfo>(basicInfo);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when props change
  React.useEffect(() => {
    setFormData(basicInfo);
  }, [basicInfo]);

  const handleInputChange = (field: keyof BasicInfo, value: string | Date) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    // Update parent state immediately for real-time preview
    onSave(updatedData);
  };

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(basicInfo);
    setIsEditing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Basic Information</CardTitle>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="etfName" className="text-sm font-medium">ETF Name *</label>
            <Input
              id="etfName"
              value={formData.etfName}
              onChange={(e) => handleInputChange('etfName', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., XLE"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="fullName" className="text-sm font-medium">Full Name *</label>
            <Input
              id="fullName"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Energy Select Sector SPDR Fund"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="ticker" className="text-sm font-medium">Ticker *</label>
            <Input
              id="ticker"
              value={formData.ticker}
              onChange={(e) => handleInputChange('ticker', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., XLE"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="logo" className="text-sm font-medium">Logo URL</label>
            <Input
              id="logo"
              value={formData.logo}
              onChange={(e) => handleInputChange('logo', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., /icons/xle.svg"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="currency" className="text-sm font-medium">Currency *</label>
            <Input
              id="currency"
              value={formData.currency}
              onChange={(e) => handleInputChange('currency', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., USD"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="exchange" className="text-sm font-medium">Exchange *</label>
            <Input
              id="exchange"
              value={formData.exchange}
              onChange={(e) => handleInputChange('exchange', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., NYSE ARCA"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="etfListingDate" className="text-sm font-medium">ETF Listing Date</label>
            <Input
              id="etfListingDate"
              type="date"
              value={formData.etfListingDate ? formData.etfListingDate.toISOString().split('T')[0] : ''}
              onChange={(e) => handleInputChange('etfListingDate', new Date(e.target.value))}
              disabled={!isEditing}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="totalAUM" className="text-sm font-medium">Total AUM</label>
            <Input
              id="totalAUM"
              value={formData.totalAUM}
              onChange={(e) => handleInputChange('totalAUM', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., $15B"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="totalNAVReturn" className="text-sm font-medium">Total NAV Return</label>
            <Input
              id="totalNAVReturn"
              value={formData.totalNAVReturn}
              onChange={(e) => handleInputChange('totalNAVReturn', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., 70.00%"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="oneDnavChange" className="text-sm font-medium">1D NAV Change</label>
            <Input
              id="oneDnavChange"
              value={formData.oneDnavChange}
              onChange={(e) => handleInputChange('oneDnavChange', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., 1.00%"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 