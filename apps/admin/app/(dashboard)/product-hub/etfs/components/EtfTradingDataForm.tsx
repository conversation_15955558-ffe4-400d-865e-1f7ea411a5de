"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { TradingData } from "../data";

interface EtfTradingDataFormProps {
  tradingData: TradingData;
  onSave: (tradingData: TradingData) => void;
}

export default function EtfTradingDataForm({ tradingData, onSave }: EtfTradingDataFormProps) {
  const [formData, setFormData] = useState<TradingData>(tradingData);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when props change
  React.useEffect(() => {
    setFormData(tradingData);
  }, [tradingData]);

  const handleInputChange = (field: keyof TradingData, value: string | number) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    // Update parent state immediately for real-time preview
    onSave(updatedData);
  };

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(tradingData);
    setIsEditing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Trading Data</CardTitle>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="open" className="text-sm font-medium">Open *</label>
            <Input
              id="open"
              type="number"
              step="0.01"
              value={formData.open}
              onChange={(e) => handleInputChange('open', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 89.37"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="volume" className="text-sm font-medium">Volume *</label>
            <Input
              id="volume"
              type="number"
              value={formData.volume}
              onChange={(e) => handleInputChange('volume', parseInt(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 52844098"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="dayLow" className="text-sm font-medium">Day Low *</label>
            <Input
              id="dayLow"
              type="number"
              step="0.01"
              value={formData.dayLow}
              onChange={(e) => handleInputChange('dayLow', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 89.43"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="dayHigh" className="text-sm font-medium">Day High *</label>
            <Input
              id="dayHigh"
              type="number"
              step="0.01"
              value={formData.dayHigh}
              onChange={(e) => handleInputChange('dayHigh', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 89.43"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="week52Low" className="text-sm font-medium">52 Week Low *</label>
            <Input
              id="week52Low"
              type="number"
              step="0.01"
              value={formData.week52Low}
              onChange={(e) => handleInputChange('week52Low', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 74.49"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="week52High" className="text-sm font-medium">52 Week High *</label>
            <Input
              id="week52High"
              type="number"
              step="0.01"
              value={formData.week52High}
              onChange={(e) => handleInputChange('week52High', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 96.36"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="aum" className="text-sm font-medium">AUM *</label>
            <Input
              id="aum"
              value={formData.aum}
              onChange={(e) => handleInputChange('aum', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., $28,939.5M"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="averageDailyVolume" className="text-sm font-medium">Average Daily Volume</label>
            <Input
              id="averageDailyVolume"
              type="number"
              value={formData.averageDailyVolume}
              onChange={(e) => handleInputChange('averageDailyVolume', parseInt(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 45000000"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="bidAskSpread" className="text-sm font-medium">Bid-Ask Spread</label>
            <Input
              id="bidAskSpread"
              type="number"
              step="0.01"
              value={formData.bidAskSpread}
              onChange={(e) => handleInputChange('bidAskSpread', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 0.02"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 