"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { TaxAnalysis } from "../data";

interface EtfTaxAnalysisFormProps {
  taxAnalysis: TaxAnalysis;
  onSave: (taxAnalysis: TaxAnalysis) => void;
}

export default function EtfTaxAnalysisForm({ taxAnalysis, onSave }: EtfTaxAnalysisFormProps) {
  const [formData, setFormData] = useState<TaxAnalysis>(taxAnalysis);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when props change
  React.useEffect(() => {
    setFormData(taxAnalysis);
  }, [taxAnalysis]);

  const handleInputChange = (field: keyof TaxAnalysis, value: string | number | boolean) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    // Update parent state immediately for real-time preview
    onSave(updatedData);
  };

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(taxAnalysis);
    setIsEditing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Tax Analysis</CardTitle>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="maxSTCapitalGainsRate" className="text-sm font-medium">Max ST Capital Gains Rate (%) *</label>
            <Input
              id="maxSTCapitalGainsRate"
              type="number"
              step="0.01"
              value={formData.maxSTCapitalGainsRate}
              onChange={(e) => handleInputChange('maxSTCapitalGainsRate', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 39.60"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="maxLTCapitalGainsRate" className="text-sm font-medium">Max LT Capital Gains Rate (%) *</label>
            <Input
              id="maxLTCapitalGainsRate"
              type="number"
              step="0.01"
              value={formData.maxLTCapitalGainsRate}
              onChange={(e) => handleInputChange('maxLTCapitalGainsRate', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 20.00"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="taxOnDistributions" className="text-sm font-medium">Tax on Distributions *</label>
            <Input
              id="taxOnDistributions"
              value={formData.taxOnDistributions}
              onChange={(e) => handleInputChange('taxOnDistributions', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Qualifies dividends"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Distributes K1</label>
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="distributesK1"
                  checked={formData.distributesK1 === true}
                  onChange={() => handleInputChange('distributesK1', true)}
                  disabled={!isEditing}
                />
                <span className="text-sm">Yes</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="distributesK1"
                  checked={formData.distributesK1 === false}
                  onChange={() => handleInputChange('distributesK1', false)}
                  disabled={!isEditing}
                />
                <span className="text-sm">No</span>
              </label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 