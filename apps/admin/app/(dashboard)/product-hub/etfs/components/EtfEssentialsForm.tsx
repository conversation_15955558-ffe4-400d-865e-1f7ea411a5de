"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { Essentials } from "../data";

interface EtfEssentialsFormProps {
  essentials: Essentials;
  onSave: (essentials: Essentials) => void;
}

export default function EtfEssentialsForm({ essentials, onSave }: EtfEssentialsFormProps) {
  const [formData, setFormData] = useState<Essentials>(essentials);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when props change
  React.useEffect(() => {
    setFormData(essentials);
  }, [essentials]);

  const handleInputChange = (field: keyof Essentials, value: string | number | boolean | Date) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    // Update parent state immediately for real-time preview
    onSave(updatedData);
  };

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(essentials);
    setIsEditing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Essentials</CardTitle>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="price" className="text-sm font-medium">Price *</label>
            <Input
              id="price"
              type="number"
              step="0.01"
              value={formData.price}
              onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 88.98"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="priceChange" className="text-sm font-medium">Price Change *</label>
            <Input
              id="priceChange"
              type="number"
              step="0.01"
              value={formData.priceChange}
              onChange={(e) => handleInputChange('priceChange', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 0.90"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="priceChangePercent" className="text-sm font-medium">Price Change Percent *</label>
            <Input
              id="priceChangePercent"
              type="number"
              step="0.01"
              value={formData.priceChangePercent}
              onChange={(e) => handleInputChange('priceChangePercent', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 1.02"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="category" className="text-sm font-medium">Category *</label>
            <Input
              id="category"
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Energy Equities"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="lastUpdated" className="text-sm font-medium">Last Updated</label>
            <Input
              id="lastUpdated"
              type="date"
              value={formData.lastUpdated ? formData.lastUpdated.toISOString().split('T')[0] : ''}
              onChange={(e) => handleInputChange('lastUpdated', new Date(e.target.value))}
              disabled={!isEditing}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 