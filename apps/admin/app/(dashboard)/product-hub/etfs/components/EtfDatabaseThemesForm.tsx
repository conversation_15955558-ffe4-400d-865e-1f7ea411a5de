"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { ETFDatabaseThemes } from "../data";

interface EtfDatabaseThemesFormProps {
  etfDatabaseThemes: ETFDatabaseThemes;
  onSave: (etfDatabaseThemes: ETFDatabaseThemes) => void;
}

export default function EtfDatabaseThemesForm({ etfDatabaseThemes, onSave }: EtfDatabaseThemesFormProps) {
  const [formData, setFormData] = useState<ETFDatabaseThemes>(etfDatabaseThemes);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when props change
  React.useEffect(() => {
    setFormData(etfDatabaseThemes);
  }, [etfDatabaseThemes]);

  const handleInputChange = (field: keyof ETFDatabaseThemes, value: string) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    // Update parent state immediately for real-time preview
    onSave(updatedData);
  };

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(etfDatabaseThemes);
    setIsEditing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">ETF Database Themes</CardTitle>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="category" className="text-sm font-medium">Category *</label>
            <Input
              id="category"
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Energy Equities"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="assetClass" className="text-sm font-medium">Asset Class *</label>
            <Input
              id="assetClass"
              value={formData.assetClass}
              onChange={(e) => handleInputChange('assetClass', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Equity"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="assetClassSize" className="text-sm font-medium">Asset Class Size *</label>
            <Input
              id="assetClassSize"
              value={formData.assetClassSize}
              onChange={(e) => handleInputChange('assetClassSize', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Large-Cap"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="assetClassStyle" className="text-sm font-medium">Asset Class Style *</label>
            <Input
              id="assetClassStyle"
              value={formData.assetClassStyle}
              onChange={(e) => handleInputChange('assetClassStyle', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Value"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="sectorGeneral" className="text-sm font-medium">Sector (General) *</label>
            <Input
              id="sectorGeneral"
              value={formData.sectorGeneral}
              onChange={(e) => handleInputChange('sectorGeneral', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Energy"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="sectorSpecific" className="text-sm font-medium">Sector (Specific) *</label>
            <Input
              id="sectorSpecific"
              value={formData.sectorSpecific}
              onChange={(e) => handleInputChange('sectorSpecific', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Oil & Gas"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="regionGeneral" className="text-sm font-medium">Region (General) *</label>
            <Input
              id="regionGeneral"
              value={formData.regionGeneral}
              onChange={(e) => handleInputChange('regionGeneral', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., North America"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="regionSpecific" className="text-sm font-medium">Region (Specific) *</label>
            <Input
              id="regionSpecific"
              value={formData.regionSpecific}
              onChange={(e) => handleInputChange('regionSpecific', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., U.S."
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 