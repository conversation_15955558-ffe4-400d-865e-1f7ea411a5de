"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { EtfProduct } from "../data";

interface EtfPreviewProps {
  etf: EtfProduct;
}

export default function EtfPreview({ etf }: EtfPreviewProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* GBTC NAV Change */}
          <Card className="bg-white">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold">GBTC 1D NAV Change</CardTitle>
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">G</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold text-green-600">
                  {etf.basicInfo.oneDnavChange}
                </div>
                <div className="text-sm text-gray-600">Exchange: {etf.basicInfo.exchange}</div>
                <div className="text-sm text-gray-600">Total AUM: {etf.basicInfo.totalAUM}</div>
                <div className="text-sm text-gray-600">Total NAV return: {etf.basicInfo.totalNAVReturn}</div>
                <div className="text-sm text-gray-600">ETF Listing Date: {etf.basicInfo.etfListingDate.toLocaleDateString()}</div>
              </div>
            </CardContent>
          </Card>

          {/* Essentials */}
          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">Essentials:</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">${etf.essentials.price.toFixed(2)}</div>
                <div className={`text-lg font-semibold ${etf.essentials.priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${etf.essentials.priceChange.toFixed(2)} ({formatPercentage(etf.essentials.priceChangePercent)})
                </div>
                <div className="text-sm text-gray-600">Category: {etf.essentials.category}</div>
                <div className="text-sm text-gray-600">Last Updated: {etf.essentials.lastUpdated.toLocaleDateString()}</div>
              </div>
            </CardContent>
          </Card>

          {/* Vitals */}
          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">Vitals:</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Issuer:</span> {etf.vitals.issuer}</div>
                <div><span className="font-medium">Brand:</span> {etf.vitals.brand}</div>
                <div><span className="font-medium">Business Sector:</span> {etf.vitals.businessSector}</div>
                <div><span className="font-medium">Expense Ratio:</span> {formatPercentage(etf.vitals.expenseRatio)}</div>
                <div><span className="font-medium">Inception:</span> {etf.vitals.inceptionDate.toLocaleDateString()}</div>
                <div><span className="font-medium">Index Tracked:</span> {etf.vitals.indexTracked}</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analysis Report */}
        <Card className="bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold">Analysis Report</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button variant="outline" className="px-6 py-3">
                FA Report
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* ETF Database Themes and Trading Data */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">ETF Database Themes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Category:</span> {etf.ETFDatabaseThemes.category}</div>
                <div><span className="font-medium">Asset Class:</span> {etf.ETFDatabaseThemes.assetClass}</div>
                <div><span className="font-medium">Asset Class Size:</span> {etf.ETFDatabaseThemes.assetClassSize}</div>
                <div><span className="font-medium">Asset Class Style:</span> {etf.ETFDatabaseThemes.assetClassStyle}</div>
                <div><span className="font-medium">Sector (General):</span> {etf.ETFDatabaseThemes.sectorGeneral}</div>
                <div><span className="font-medium">Sector (Specific):</span> {etf.ETFDatabaseThemes.sectorSpecific}</div>
                <div><span className="font-medium">Region (General):</span> {etf.ETFDatabaseThemes.regionGeneral}</div>
                <div><span className="font-medium">Region (Specific):</span> {etf.ETFDatabaseThemes.regionSpecific}</div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">Trading Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Open:</span> ${etf.tradingData.open.toFixed(2)}</div>
                <div><span className="font-medium">Volume:</span> {formatNumber(etf.tradingData.volume)}</div>
                <div><span className="font-medium">Day Lo:</span> ${etf.tradingData.dayLow.toFixed(2)}</div>
                <div><span className="font-medium">Day Hi:</span> ${etf.tradingData.dayHigh.toFixed(2)}</div>
                <div><span className="font-medium">52 Week Lo:</span> ${etf.tradingData.week52Low.toFixed(2)}</div>
                <div><span className="font-medium">52 Week Hi:</span> ${etf.tradingData.week52High.toFixed(2)}</div>
                <div><span className="font-medium">AUM:</span> {etf.tradingData.aum}</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* FactSet Classifications */}
        <Card className="bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold">FactSet Classifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
              <div><span className="font-medium">Segment:</span> {etf.factSetClassifications.segment}</div>
              <div><span className="font-medium">Category:</span> {etf.factSetClassifications.category}</div>
              <div><span className="font-medium">Focus:</span> {etf.factSetClassifications.focus}</div>
              <div><span className="font-medium">Niche:</span> {etf.factSetClassifications.niche}</div>
              <div><span className="font-medium">Strategy:</span> {etf.factSetClassifications.strategy}</div>
              <div><span className="font-medium">Weighting Scheme:</span> {etf.factSetClassifications.weightingScheme}</div>
            </div>
          </CardContent>
        </Card>

        {/* Valuation and Expenses */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">XLE Valuation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">XLE:</span> {etf.peRatio.xle.toFixed(2)}</div>
                <div><span className="font-medium">ETF Database Category Average:</span> {etf.peRatio.etfDatabaseCategoryAverage.toFixed(2)}</div>
                <div><span className="font-medium">FactSet Segment Average:</span> {etf.peRatio.factSetSegmentAverage.toFixed(2)}</div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">XLE Expenses & Fees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">XLE:</span> {formatPercentage(etf.expensesRatioAnalysis.xle)}</div>
                <div><span className="font-medium">ETF Database Category Average:</span> {formatPercentage(etf.expensesRatioAnalysis.etfDatabaseCategoryAverage)}</div>
                <div><span className="font-medium">FactSet Segment Average:</span> {formatPercentage(etf.expensesRatioAnalysis.factSetSegmentAverage)}</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Dividend and Tax Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">XLE Dividend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-medium">Dividend:</span>
                  <div className="ml-4 space-y-1">
                    <div>XLE: ${etf.XLE.dividend.toFixed(2)}</div>
                    <div>ETF Database Category Average: ${etf.XLE.ETFDatabaseCategoryAverage.dividend.toFixed(2)}</div>
                    <div>FactSet Segment Average: ${etf.XLE.factSetSegmentAverage.dividend.toFixed(2)}</div>
                  </div>
                </div>
                <div>
                  <span className="font-medium">Annual Dividend Rate:</span>
                  <div className="ml-4 space-y-1">
                    <div>XLE: ${etf.XLE.annualDividendRate.toFixed(2)}</div>
                    <div>ETF Database Category Average: ${etf.XLE.ETFDatabaseCategoryAverage.annualDividendRate.toFixed(2)}</div>
                    <div>FactSet Segment Average: ${etf.XLE.factSetSegmentAverage.annualDividendRate.toFixed(2)}</div>
                  </div>
                </div>
                <div>
                  <span className="font-medium">Annual Dividend Yield:</span>
                  <div className="ml-4 space-y-1">
                    <div>XLE: {formatPercentage(etf.XLE.annualDividendYield)}</div>
                    <div>ETF Database Category Average: {formatPercentage(etf.XLE.ETFDatabaseCategoryAverage.annualDividendYield)}</div>
                    <div>FactSet Segment Average: {formatPercentage(etf.XLE.factSetSegmentAverage.annualDividendYield)}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">Tax Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Max ST Capital Gains Rate:</span> {formatPercentage(etf.taxAnalysis.maxSTCapitalGainsRate)}</div>
                <div><span className="font-medium">Tax on Distributions:</span> {etf.taxAnalysis.taxOnDistributions}</div>
                <div><span className="font-medium">Max LT Capital Gains Rate:</span> {formatPercentage(etf.taxAnalysis.maxLTCapitalGainsRate)}</div>
                <div><span className="font-medium">Distributes k1:</span> {etf.taxAnalysis.distributesK1 ? 'Yes' : 'No'}</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Price and Volume Charts Placeholder */}
        <Card className="bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold">XLE Price and Volume Charts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2 mb-4">
              {['1D', '5D', '1M', '3M', '6M', '1Y', '3Y', '5Y', '10Y', '20Y'].map((period) => (
                <Button
                  key={period}
                  variant={period === '3M' ? 'default' : 'outline'}
                  size="sm"
                >
                  {period}
                </Button>
              ))}
            </div>
            <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Chart visualization would go here</p>
            </div>
          </CardContent>
        </Card>

        {/* Holdings */}
        <Card className="bg-white">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold">Holdings</CardTitle>
              <Button variant="outline" size="sm">
                Download CSV
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {etf.holdings && etf.holdings.length > 0 ? (
                etf.holdings.map((holding, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      {holding.logo ? (
                        <img src={holding.logo} alt={holding.companyName} className="w-6 h-6" />
                      ) : (
                        <span className="text-xs font-bold">{holding.companyName.charAt(0)}</span>
                      )}
                    </div>
                    <span className="font-medium">{holding.companyName}</span>
                  </div>
                  <span className="font-semibold">{holding.percentage.toFixed(2)}%</span>
                </div>
              ))
              ) : (
                <div className="text-center text-gray-500 py-4">
                  <p>No holdings data available</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Performance */}
        <Card className="bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold">XLE Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { label: '1 Month Return', xle: etf.benchmarkComparison.xle.oneMonth, category: etf.benchmarkComparison.etfDatabaseCategoryAverage.oneMonth, segment: etf.benchmarkComparison.factSetSegmentAverage.oneMonth },
                { label: '3 Month Return', xle: etf.benchmarkComparison.xle.threeMonth, category: etf.benchmarkComparison.etfDatabaseCategoryAverage.threeMonth, segment: etf.benchmarkComparison.factSetSegmentAverage.threeMonth },
                { label: 'YTD Return', xle: etf.benchmarkComparison.xle.ytd, category: etf.benchmarkComparison.etfDatabaseCategoryAverage.ytd, segment: etf.benchmarkComparison.factSetSegmentAverage.ytd },
                { label: '1 Year Return', xle: etf.benchmarkComparison.xle.oneYear, category: etf.benchmarkComparison.etfDatabaseCategoryAverage.oneYear, segment: etf.benchmarkComparison.factSetSegmentAverage.oneYear },
                { label: '3 Year Return', xle: etf.benchmarkComparison.xle.threeYear, category: etf.benchmarkComparison.etfDatabaseCategoryAverage.threeYear, segment: etf.benchmarkComparison.factSetSegmentAverage.threeYear },
                { label: '5 Year Return', xle: etf.benchmarkComparison.xle.fiveYear, category: etf.benchmarkComparison.etfDatabaseCategoryAverage.fiveYear, segment: etf.benchmarkComparison.factSetSegmentAverage.fiveYear },
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium w-32">{item.label}</span>
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-4 bg-green-600 rounded"></div>
                    <span className="text-sm font-semibold">{formatPercentage(item.xle)}</span>
                    <div className="w-16 h-4 bg-green-400 rounded"></div>
                    <span className="text-sm">{formatPercentage(item.category)}</span>
                    <div className="w-16 h-4 bg-green-300 rounded"></div>
                    <span className="text-sm">{formatPercentage(item.segment)}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Concentration Analysis and Size Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">Concentration Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-medium">XLE:</span> {etf.concentrationAnalysis.holdingsComparison.xle.totalHoldings} holdings
                  <div className="ml-4 space-y-1">
                    <div>Top 50: {etf.concentrationAnalysis.holdingsComparison.xle.top50Percentage.toFixed(2)}%</div>
                    <div>Top 15: {etf.concentrationAnalysis.holdingsComparison.xle.top15Percentage.toFixed(2)}%</div>
                    <div>Top 10: {etf.concentrationAnalysis.holdingsComparison.xle.top10Percentage.toFixed(2)}%</div>
                  </div>
                </div>
                <div>
                  <span className="font-medium">ETF Database Category Average:</span> {etf.concentrationAnalysis.holdingsComparison.etfDatabaseCategoryAverage.totalHoldings} holdings
                  <div className="ml-4 space-y-1">
                    <div>Top 50: {etf.concentrationAnalysis.holdingsComparison.etfDatabaseCategoryAverage.top50Percentage.toFixed(2)}%</div>
                    <div>Top 15: {etf.concentrationAnalysis.holdingsComparison.etfDatabaseCategoryAverage.top15Percentage.toFixed(2)}%</div>
                    <div>Top 10: {etf.concentrationAnalysis.holdingsComparison.etfDatabaseCategoryAverage.top10Percentage.toFixed(2)}%</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">Size Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-medium">XLE:</span>
                  <div className="ml-4 space-y-1">
                    <div>Large ({'>'}12.9B): {etf.concentrationAnalysis.sizeComparison.xle.large.toFixed(2)}%</div>
                    <div>Small (&lt;600M): {etf.concentrationAnalysis.sizeComparison.xle.small.toFixed(2)}%</div>
                    <div>Micro (&lt;600M): {etf.concentrationAnalysis.sizeComparison.xle.micro.toFixed(2)}%</div>
                  </div>
                </div>
                <div>
                  <span className="font-medium">ETF Database Category Average:</span>
                  <div className="ml-4 space-y-1">
                    <div>Large ({'>'}12.9B): {etf.concentrationAnalysis.sizeComparison.etfDatabaseCategoryAverage.large.toFixed(2)}%</div>
                    <div>Mid ({'>'}2.7B): {etf.concentrationAnalysis.sizeComparison.etfDatabaseCategoryAverage.mid.toFixed(2)}%</div>
                    <div>Small (&lt;600M): {etf.concentrationAnalysis.sizeComparison.etfDatabaseCategoryAverage.small.toFixed(2)}%</div>
                    <div>Micro (&lt;600M): {etf.concentrationAnalysis.sizeComparison.etfDatabaseCategoryAverage.micro.toFixed(2)}%</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Similar Recommendations - Placeholder */}
        <Card className="bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold">Similar Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-gray-500 py-8">
              <p>Similar recommendations will be displayed here</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 