"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { Documents } from "../data";

interface EtfDocumentsFormProps {
  documents: Documents;
  onSave: (documents: Documents) => void;
}

export default function EtfDocumentsForm({ documents, onSave }: EtfDocumentsFormProps) {
  const [formData, setFormData] = useState<Documents>(documents);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when props change
  React.useEffect(() => {
    setFormData(documents);
  }, [documents]);

  const handleInputChange = (field: keyof Documents, value: string) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    // Update parent state immediately for real-time preview
    onSave(updatedData);
  };

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(documents);
    setIsEditing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Documents</CardTitle>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="kid" className="text-sm font-medium">KID URL</label>
            <Input
              id="kid"
              value={formData.kid}
              onChange={(e) => handleInputChange('kid', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., https://example.com/kid.pdf"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="gid" className="text-sm font-medium">GID URL</label>
            <Input
              id="gid"
              value={formData.gid}
              onChange={(e) => handleInputChange('gid', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., https://example.com/gid.pdf"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="ratingRationale" className="text-sm font-medium">Rating Rationale URL</label>
            <Input
              id="ratingRationale"
              value={formData.ratingRationale}
              onChange={(e) => handleInputChange('ratingRationale', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., https://example.com/rating.pdf"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="financialAdvisorReport" className="text-sm font-medium">Financial Advisor Report URL</label>
            <Input
              id="financialAdvisorReport"
              value={formData.financialAdvisorReport}
              onChange={(e) => handleInputChange('financialAdvisorReport', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., https://example.com/fa-report.pdf"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="holdingsCSV" className="text-sm font-medium">Holdings CSV URL</label>
            <Input
              id="holdingsCSV"
              value={formData.holdingsCSV}
              onChange={(e) => handleInputChange('holdingsCSV', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., https://example.com/holdings.csv"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="factSheet" className="text-sm font-medium">Fact Sheet URL</label>
            <Input
              id="factSheet"
              value={formData.factSheet}
              onChange={(e) => handleInputChange('factSheet', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., https://example.com/fact-sheet.pdf"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 