"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { FactSetClassifications } from "../data";

interface EtfFactSetClassificationsFormProps {
  factSetClassifications: FactSetClassifications;
  onSave: (factSetClassifications: FactSetClassifications) => void;
}

export default function EtfFactSetClassificationsForm({ factSetClassifications, onSave }: EtfFactSetClassificationsFormProps) {
  const [formData, setFormData] = useState<FactSetClassifications>(factSetClassifications);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when props change
  React.useEffect(() => {
    setFormData(factSetClassifications);
  }, [factSetClassifications]);

  const handleInputChange = (field: keyof FactSetClassifications, value: string) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    // Update parent state immediately for real-time preview
    onSave(updatedData);
  };

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(factSetClassifications);
    setIsEditing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">FactSet Classifications</CardTitle>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="segment" className="text-sm font-medium">Segment *</label>
            <Input
              id="segment"
              value={formData.segment}
              onChange={(e) => handleInputChange('segment', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Equity: U.S. Energy"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="category" className="text-sm font-medium">Category *</label>
            <Input
              id="category"
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Sector"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="focus" className="text-sm font-medium">Focus *</label>
            <Input
              id="focus"
              value={formData.focus}
              onChange={(e) => handleInputChange('focus', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Energy"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="niche" className="text-sm font-medium">Niche *</label>
            <Input
              id="niche"
              value={formData.niche}
              onChange={(e) => handleInputChange('niche', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Broad-based"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="strategy" className="text-sm font-medium">Strategy *</label>
            <Input
              id="strategy"
              value={formData.strategy}
              onChange={(e) => handleInputChange('strategy', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Vanilla"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="weightingScheme" className="text-sm font-medium">Weighting Scheme *</label>
            <Input
              id="weightingScheme"
              value={formData.weightingScheme}
              onChange={(e) => handleInputChange('weightingScheme', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Market Cap"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 