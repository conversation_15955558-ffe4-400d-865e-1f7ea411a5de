"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { Vitals } from "../data";

interface EtfVitalsFormProps {
  vitals: Vitals;
  onSave: (vitals: Vitals) => void;
}

export default function EtfVitalsForm({ vitals, onSave }: EtfVitalsFormProps) {
  const [formData, setFormData] = useState<Vitals>(vitals);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when props change
  React.useEffect(() => {
    setFormData(vitals);
  }, [vitals]);

  const handleInputChange = (field: keyof Vitals, value: string | number | Date) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    // Update parent state immediately for real-time preview
    onSave(updatedData);
  };

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(vitals);
    setIsEditing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Vitals</CardTitle>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
            >
              Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="issuer" className="text-sm font-medium">Issuer *</label>
            <Input
              id="issuer"
              value={formData.issuer}
              onChange={(e) => handleInputChange('issuer', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., State Street"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="brand" className="text-sm font-medium">Brand *</label>
            <Input
              id="brand"
              value={formData.brand}
              onChange={(e) => handleInputChange('brand', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., SPDR"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="businessSector" className="text-sm font-medium">Business Sector *</label>
            <Input
              id="businessSector"
              value={formData.businessSector}
              onChange={(e) => handleInputChange('businessSector', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., Finance"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="expenseRatio" className="text-sm font-medium">Expense Ratio (%) *</label>
            <Input
              id="expenseRatio"
              type="number"
              step="0.01"
              value={formData.expenseRatio}
              onChange={(e) => handleInputChange('expenseRatio', parseFloat(e.target.value) || 0)}
              disabled={!isEditing}
              placeholder="e.g., 0.09"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="inceptionDate" className="text-sm font-medium">Inception Date *</label>
            <Input
              id="inceptionDate"
              type="date"
              value={formData.inceptionDate ? formData.inceptionDate.toISOString().split('T')[0] : ''}
              onChange={(e) => handleInputChange('inceptionDate', new Date(e.target.value))}
              disabled={!isEditing}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="indexTracked" className="text-sm font-medium">Index Tracked *</label>
            <Input
              id="indexTracked"
              value={formData.indexTracked}
              onChange={(e) => handleInputChange('indexTracked', e.target.value)}
              disabled={!isEditing}
              placeholder="e.g., S&P Energy Select"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 