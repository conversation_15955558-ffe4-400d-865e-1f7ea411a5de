// ETF Product Types based on the MongoDB Schema
export interface HistoricalPrice {
  date: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjustedClose: number;
}

export interface Holding {
  companyName: string;
  ticker: string;
  percentage: number;
  shares: number;
  marketValue: number;
  sector: string;
  logo: string;
}

export interface Benchmark {
  oneMonth: number;
  threeMonth: number;
  ytd: number;
  oneYear: number;
  threeYear: number;
  fiveYear: number;
}

export interface BenchmarkComparison {
  xle: Benchmark;
  etfDatabaseCategoryAverage: Benchmark;
  factSetSegmentAverage: Benchmark;
}

export interface Concentration {
  totalHoldings: number;
  top50Percentage: number;
  top15Percentage: number;
  top10Percentage: number;
}

export interface SizeComparison {
  large: number;
  mid: number;
  small: number;
  micro: number;
}

export interface ConcentrationAnalysis {
  holdingsComparison: {
    xle: Concentration;
    etfDatabaseCategoryAverage: Concentration;
    factSetSegmentAverage: Concentration;
  };
  sizeComparison: {
    xle: SizeComparison;
    etfDatabaseCategoryAverage: SizeComparison;
    factSetSegmentAverage: SizeComparison;
  };
}

export interface PeRatio {
  xle: number;
  etfDatabaseCategoryAverage: number;
  factSetSegmentAverage: number;
}

export interface ExpensesRatio {
  xle: number;
  etfDatabaseCategoryAverage: number;
  factSetSegmentAverage: number;
}

export interface Dividend {
  dividend: number;
  dividendDate: Date;
  annualDividendRate: number;
  annualDividendYield: number;
}

export interface XLE {
  dividend: number;
  dividendDate: Date;
  annualDividendRate: number;
  annualDividendYield: number;
  ETFDatabaseCategoryAverage: Dividend;
  factSetSegmentAverage: Dividend;
}

export interface TaxAnalysis {
  maxSTCapitalGainsRate: number;
  taxOnDistributions: string;
  maxLTCapitalGainsRate: number;
  distributesK1: boolean;
}

export interface BasicInfo {
  etfName: string;
  fullName: string;
  ticker: string;
  logo: string;
  currency: string;
  exchange: string;
  etfListingDate: Date;
  totalAUM: string;
  totalNAVReturn: string;
  oneDnavChange: string;
}

export interface Essentials {
  price: number;
  priceChange: number;
  priceChangePercent: number;
  category: string;
  lastUpdated: Date;
}

export interface Vitals {
  issuer: string;
  brand: string;
  businessSector: string;
  expenseRatio: number;
  inceptionDate: Date;
  indexTracked: string;
}

export interface ETFDatabaseThemes {
  category: string;
  assetClass: string;
  assetClassSize: string;
  assetClassStyle: string;
  sectorGeneral: string;
  sectorSpecific: string;
  regionGeneral: string;
  regionSpecific: string;
}

export interface FactSetClassifications {
  segment: string;
  category: string;
  focus: string;
  niche: string;
  strategy: string;
  weightingScheme: string;
}

export interface TradingData {
  open: number;
  volume: number;
  dayLow: number;
  dayHigh: number;
  week52Low: number;
  week52High: number;
  aum: string;
  averageDailyVolume: number;
  bidAskSpread: number;
}

export interface Documents {
  kid: string;
  gid: string;
  ratingRationale: string;
  financialAdvisorReport: string;
  holdingsCSV: string;
  factSheet: string;
}

export interface PriceVolumeData {
  historicalPrices: HistoricalPrice[];
}

export interface EtfProduct {
  productId: string;
  basicInfo: BasicInfo;
  essentials: Essentials;
  vitals: Vitals;
  ETFDatabaseThemes: ETFDatabaseThemes;
  factSetClassifications: FactSetClassifications;
  tradingData: TradingData;
  peRatio: PeRatio;
  expensesRatioAnalysis: ExpensesRatio;
  XLE: XLE;
  taxAnalysis: TaxAnalysis;
  holdings: Holding[];
  benchmarkComparison: BenchmarkComparison;
  concentrationAnalysis: ConcentrationAnalysis;
  priceVolumeData: PriceVolumeData;
  documents: Documents;
}

// Sample data based on the MongoDB schema
export const sampleEtf: EtfProduct = {
  productId: "",
  basicInfo: {
    etfName: "",
    fullName: "",
    ticker: "",
    logo: "",
    currency: "",
    exchange: "",
    etfListingDate: new Date(),
    totalAUM: "",
    totalNAVReturn: "",
    oneDnavChange: "",
  },
  essentials: {
    price: 0,
    priceChange: 0,
    priceChangePercent: 0,
    category: "",
    lastUpdated: new Date(),
  },
  vitals: {
    issuer: "",
    brand: "",
    businessSector: "",
    expenseRatio: 0,
    inceptionDate: new Date(),
    indexTracked: "",
  },
  ETFDatabaseThemes: {
    category: "",
    assetClass: "",
    assetClassSize: "",
    assetClassStyle: "",
    sectorGeneral: "",
    sectorSpecific: "",
    regionGeneral: "",
    regionSpecific: "",
  },
  factSetClassifications: {
    segment: "",
    category: "",
    focus: "",
    niche: "",
    strategy: "",
    weightingScheme: "",
  },
  tradingData: {
    open: 0,
    volume: 0,
    dayLow: 0,
    dayHigh: 0,
    week52Low: 0,
    week52High: 0,
    aum: "",
    averageDailyVolume: 0,
    bidAskSpread: 0,
  },
  peRatio: {
    xle: 0,
    etfDatabaseCategoryAverage: 0,
    factSetSegmentAverage: 0,
  },
  expensesRatioAnalysis: {
    xle: 0,
    etfDatabaseCategoryAverage: 0,
    factSetSegmentAverage: 0,
  },
  XLE: {
    dividend: 0,
    dividendDate: new Date(),
    annualDividendRate: 0,
    annualDividendYield: 0,
    ETFDatabaseCategoryAverage: {
      dividend: 0,
      dividendDate: new Date(),
      annualDividendRate: 0,
      annualDividendYield: 0,
    },
    factSetSegmentAverage: {
      dividend: 0,
      dividendDate: new Date(),
      annualDividendRate: 0,
      annualDividendYield: 0,
    },
  },
  taxAnalysis: {
    maxSTCapitalGainsRate: 0,
    taxOnDistributions: "",
    maxLTCapitalGainsRate: 0,
    distributesK1: false,
  },
  holdings: [],
  benchmarkComparison: {
    xle: {
      oneMonth: 0,
      threeMonth: 0,
      ytd: 0,
      oneYear: 0,
      threeYear: 0,
      fiveYear: 0,
    },
    etfDatabaseCategoryAverage: {
      oneMonth: 0,
      threeMonth: 0,
      ytd: 0,
      oneYear: 0,
      threeYear: 0,
      fiveYear: 0,
    },
    factSetSegmentAverage: {
      oneMonth: 0,
      threeMonth: 0,
      ytd: 0,
      oneYear: 0,
      threeYear: 0,
      fiveYear: 0,
    },
  },
  concentrationAnalysis: {
    holdingsComparison: {
      xle: {
        totalHoldings: 0,
        top50Percentage: 0,
        top15Percentage: 0,
        top10Percentage: 0,
      },
      etfDatabaseCategoryAverage: {
        totalHoldings: 0,
        top50Percentage: 0,
        top15Percentage: 0,
        top10Percentage: 0,
      },
      factSetSegmentAverage: {
        totalHoldings: 0,
        top50Percentage: 0,
        top15Percentage: 0,
        top10Percentage: 0,
      },
    },
    sizeComparison: {
      xle: {
        large: 0,
        mid: 0,
        small: 0,
        micro: 0,
      },
      etfDatabaseCategoryAverage: {
        large: 0,
        mid: 0,
        small: 0,
        micro: 0,
      },
      factSetSegmentAverage: {
        large: 0,
        mid: 0,
        small: 0,
        micro: 0,
      },
    },
  },
  priceVolumeData: {
    historicalPrices: [],
  },
  documents: {
    kid: "",
    gid: "",
    ratingRationale: "",
    financialAdvisorReport: "",
    holdingsCSV: "",
    factSheet: "",
  },
}; 