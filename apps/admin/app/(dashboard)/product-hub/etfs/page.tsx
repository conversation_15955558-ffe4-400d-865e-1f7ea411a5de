"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from "@admin/components/ui/button";
import { ArrowLeft, Download, MessageCircle, Save, Eye, Send } from "lucide-react";
import { sampleEtf, EtfProduct } from "./data";
import { toast } from "react-hot-toast";
import EtfPreview from "./components/EtfPreview";
import axios from 'axios';
import EtfBasicInfoForm from "./components/EtfBasicInfoForm";
import EtfEssentialsForm from "./components/EtfEssentialsForm";
import EtfVitalsForm from "./components/EtfVitalsForm";
import EtfTradingDataForm from "./components/EtfTradingDataForm";
import EtfDatabaseThemesForm from "./components/EtfDatabaseThemesForm";
import EtfFactSetClassificationsForm from "./components/EtfFactSetClassificationsForm";
import EtfTaxAnalysisForm from "./components/EtfTaxAnalysisForm";
import EtfDocumentsForm from "./components/EtfDocumentsForm";
import EtfHoldingsForm from "./components/EtfHoldingsForm";
import { createEtf, updateEtf } from '@admin/app/lib/productApiService';

export default function EtfDetailPage() {
  const searchParams = useSearchParams();
  const [mode, setMode] = useState<'form' | 'review'>('form');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [etf, setEtf] = useState<EtfProduct>(sampleEtf);
  const [vendor_id, setVendorId] = useState<string>("");
  const router = useRouter();

  useEffect(() => {
    const vendorParam = searchParams.get('vendor');
    if (vendorParam) {
      setVendorId(vendorParam);
      setEtf(prev => ({
        ...prev,
        vendor_id: vendorParam
      }));
    }
  }, [searchParams]);
  const [kdUrl, setKdUrl] = useState("");
  const [showKdInput, setShowKdInput] = useState(false);
  const [contactNumber, setContactNumber] = useState("");
  const [showContactInput, setShowContactInput] = useState(false);

  const handleBasicInfoSave = (basicInfo: EtfProduct['basicInfo']) => {
    setEtf(prev => ({ ...prev, basicInfo }));
  };

  const handleEssentialsSave = (essentials: EtfProduct['essentials']) => {
    setEtf(prev => ({ ...prev, essentials }));
  };

  const handleVitalsSave = (vitals: EtfProduct['vitals']) => {
    setEtf(prev => ({ ...prev, vitals }));
  };

  const handleTradingDataSave = (tradingData: EtfProduct['tradingData']) => {
    setEtf(prev => ({ ...prev, tradingData }));
  };

  const handleETFDatabaseThemesSave = (ETFDatabaseThemes: EtfProduct['ETFDatabaseThemes']) => {
    setEtf(prev => ({ ...prev, ETFDatabaseThemes }));
  };

  const handleFactSetClassificationsSave = (factSetClassifications: EtfProduct['factSetClassifications']) => {
    setEtf(prev => ({ ...prev, factSetClassifications }));
  };

  const handleTaxAnalysisSave = (taxAnalysis: EtfProduct['taxAnalysis']) => {
    setEtf(prev => ({ ...prev, taxAnalysis }));
  };

  const handleDocumentsSave = (documents: EtfProduct['documents']) => {
    setEtf(prev => ({ ...prev, documents }));
  };

  const handleHoldingsSave = (holdings: EtfProduct['holdings']) => {
    setEtf(prev => ({ ...prev, holdings }));
  };

  const validateForm = () => {
    const errors = [];
    
    // Check required fields from basicInfo
    if (!etf.basicInfo?.etfName) errors.push("ETF Name is required");
    if (!etf.basicInfo?.fullName) errors.push("Full Name is required");
    if (!etf.basicInfo?.ticker) errors.push("Ticker is required");
    if (!etf.basicInfo?.exchange) errors.push("Exchange is required");
    if (!etf.basicInfo?.currency) errors.push("Currency is required");
    
    // Check required fields from essentials
    if (!etf.essentials?.price) errors.push("Price is required");
    if (!etf.essentials?.priceChange) errors.push("Price Change is required");
    if (!etf.essentials?.priceChangePercent) errors.push("Price Change Percent is required");
    if (!etf.essentials?.category) errors.push("Category is required");
    
    // Check required fields from vitals
    if (!etf.vitals?.issuer) errors.push("Issuer is required");
    if (!etf.vitals?.brand) errors.push("Brand is required");
    if (!etf.vitals?.businessSector) errors.push("Business Sector is required");
    if (!etf.vitals?.expenseRatio) errors.push("Expense Ratio is required");
    if (!etf.vitals?.inceptionDate) errors.push("Inception Date is required");
    if (!etf.vitals?.indexTracked) errors.push("Index Tracked is required");
    
    return errors;
  };

  const getFormProgress = () => {
    const sections = [
      {
        name: 'Basic Info',
        isComplete: !!(etf.basicInfo?.etfName && etf.basicInfo?.fullName && etf.basicInfo?.ticker && etf.basicInfo?.exchange && etf.basicInfo?.currency)
      },
      {
        name: 'Essentials',
        isComplete: !!(etf.essentials?.price && etf.essentials?.priceChange !== undefined && etf.essentials?.priceChangePercent !== undefined && etf.essentials?.category)
      },
      {
        name: 'Vitals',
        isComplete: !!(etf.vitals?.issuer && etf.vitals?.brand && etf.vitals?.businessSector && etf.vitals?.expenseRatio !== undefined && etf.vitals?.indexTracked)
      },
      {
        name: 'Trading Data',
        isComplete: !!(etf.tradingData?.open !== undefined && etf.tradingData?.volume !== undefined && etf.tradingData?.dayLow !== undefined && etf.tradingData?.dayHigh !== undefined)
      },
      {
        name: 'ETF Database Themes',
        isComplete: !!(etf.ETFDatabaseThemes?.category && etf.ETFDatabaseThemes?.assetClass && etf.ETFDatabaseThemes?.assetClassSize && etf.ETFDatabaseThemes?.assetClassStyle)
      },
      {
        name: 'FactSet Classifications',
        isComplete: !!(etf.factSetClassifications?.segment && etf.factSetClassifications?.category && etf.factSetClassifications?.focus && etf.factSetClassifications?.strategy)
      },
      {
        name: 'Tax Analysis',
        isComplete: !!(etf.taxAnalysis?.maxSTCapitalGainsRate !== undefined && etf.taxAnalysis?.maxLTCapitalGainsRate !== undefined && etf.taxAnalysis?.taxOnDistributions)
      },
      {
        name: 'Documents',
        isComplete: !!(etf.documents?.kid || etf.documents?.gid || etf.documents?.factSheet)
      },
      {
        name: 'Holdings',
        isComplete: !!(etf.holdings && etf.holdings.length > 0)
      }
    ];

    const completedSections = sections.filter(section => section.isComplete).length;
    const totalSections = sections.length;
    
    return Math.max(0, Math.min(100, (completedSections / totalSections) * 100));
  };

  const handleReview = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    setMode('review');
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const etfDataToSave = {
        ...etf,
        kdUrl,
        contactNumber,
        vendor_id,
        type: "ETF"
      };

      let response: any;
      if (etf.productId) {
        response = await updateEtf(etf.productId, etfDataToSave);
        if (response && response.statusCode === 200) {
          toast.success("ETF updated successfully");
          setEtf(prev => ({ ...prev, ...response.data }));
          setMode('form');
        }
      } else {
        response = await createEtf(etfDataToSave);
        if (response && response.statusCode === 201) {
          router.push('/product-hub');
          toast.success("ETF created successfully");
          setEtf(prev => ({ ...prev, productId: response.data?.productId || response.id }));
          setMode('form');
        }
      }
      if (!response || (!response.statusCode && !response.id)) {
        throw new Error('Failed to save ETF');
      }
    } catch (error: any) {
      console.error('Error saving ETF:', error);
      toast.error(error.message || "Failed to save ETF");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitWithValidation = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    await handleSubmit();
  };

  const handleKdUrlSave = () => {
    if (kdUrl.trim()) {
      setShowKdInput(false);
      console.log('KD URL saved:', kdUrl);
      alert('KD URL saved successfully!');
    } else {
      alert('Please enter a valid KD URL');
    }
  };

  const handleKdUrlCancel = () => {
    setKdUrl("");
    setShowKdInput(false);
  };

  const handleContactSave = () => {
    if (contactNumber.trim()) {
      setShowContactInput(false);
      console.log('Contact number saved:', contactNumber);
      alert('Contact number saved successfully!');
    } else {
      alert('Please enter a valid contact number');
    }
  };

  const handleContactCancel = () => {
    setContactNumber("");
    setShowContactInput(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {mode === 'form' ? 'Edit ETF Product' : 'Review ETF Product'}
              </h1>
              <p className="text-sm text-gray-600">
                {etf.basicInfo.ticker || 'New ETF Product'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {mode === 'form' && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowKdInput(true)}
                  className="flex items-center space-x-2"
                >
                  <Download className="h-4 w-4" />
                  <span>KD URL</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowContactInput(true)}
                  className="flex items-center space-x-2"
                >
                  <MessageCircle className="h-4 w-4" />
                  <span>Contact</span>
                </Button>
                <Button
                  onClick={handleReview}
                  className="flex items-center space-x-2"
                >
                  <Eye className="h-4 w-4" />
                  <span>Review</span>
                </Button>
              </>
            )}
            {mode === 'review' && (
              <>
                <Button
                  variant="outline"
                  onClick={() => setMode('form')}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Edit</span>
                </Button>
                <Button
                  onClick={handleSubmitWithValidation}
                  disabled={isSubmitting}
                  className="flex items-center space-x-2"
                >
                  <Send className="h-4 w-4" />
                  <span>{isSubmitting ? 'Saving...' : 'Save & Publish'}</span>
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        {mode === 'form' && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
              <span>Form Progress</span>
              <span>{Math.round(getFormProgress())}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getFormProgress()}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* KD URL Modal */}
      {showKdInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Enter KD URL</h3>
            <input
              type="text"
              value={kdUrl}
              onChange={(e) => setKdUrl(e.target.value)}
              placeholder="Enter KD URL"
              className="w-full p-2 border border-gray-300 rounded mb-4"
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleKdUrlCancel}>
                Cancel
              </Button>
              <Button onClick={handleKdUrlSave}>
                Save
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Contact Number Modal */}
      {showContactInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Enter Contact Number</h3>
            <input
              type="text"
              value={contactNumber}
              onChange={(e) => setContactNumber(e.target.value)}
              placeholder="Enter contact number"
              className="w-full p-2 border border-gray-300 rounded mb-4"
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleContactCancel}>
                Cancel
              </Button>
              <Button onClick={handleContactSave}>
                Save
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        {mode === 'form' ? (
          <div className="space-y-6">
            <EtfBasicInfoForm
              basicInfo={etf.basicInfo}
              onSave={handleBasicInfoSave}
            />
            <EtfEssentialsForm
              essentials={etf.essentials}
              onSave={handleEssentialsSave}
            />
            <EtfVitalsForm
              vitals={etf.vitals}
              onSave={handleVitalsSave}
            />
            <EtfTradingDataForm
              tradingData={etf.tradingData}
              onSave={handleTradingDataSave}
            />
            <EtfDatabaseThemesForm
              etfDatabaseThemes={etf.ETFDatabaseThemes}
              onSave={handleETFDatabaseThemesSave}
            />
            <EtfFactSetClassificationsForm
              factSetClassifications={etf.factSetClassifications}
              onSave={handleFactSetClassificationsSave}
            />
            <EtfTaxAnalysisForm
              taxAnalysis={etf.taxAnalysis}
              onSave={handleTaxAnalysisSave}
            />
            <EtfDocumentsForm
              documents={etf.documents}
              onSave={handleDocumentsSave}
            />
            <EtfHoldingsForm
              holdings={etf.holdings}
              onSave={handleHoldingsSave}
            />
          </div>
        ) : (
          <EtfPreview etf={etf} />
        )}
      </div>
    </div>
  );
} 