"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from "@admin/components/ui/button";
import { ArrowLeft, Download, MessageCircle, Save, Eye, Send } from "lucide-react";
import { toast } from "react-hot-toast";
import axios from 'axios';
import REITSummary from "./components/REITSummary";
import REITAbout from "./components/REITAbout";
import REITPerformance from "./components/REITPerformance";
import REITHoldings from "./components/REITHoldings";
import PropertyAnalysis from "./components/PropertyAnalysis";
import AdvancedRatios from "./components/AdvancedRatios";
import MinimumInvestment from "./components/MinimumInvestment";
import DividendYield from "./components/DividendYield";
import ReturnsRankings from "./components/ReturnsRankings";
import REITManagement from "./components/REITManagement";
import PeerComparison from "./components/PeerComparison";
import FAQs from "./components/FAQs";
import SimilarRecommendations from "./components/SimilarRecommendations";
import EssentialsCard from "./components/EssentialsCard";
import { sampleREIT, REIT } from "./data";
import REITPreview from "./components/REITPreview";
import { createReit, updateReit } from '@admin/app/lib/productApiService';

export default function REITDetailPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [mode, setMode] = useState<'form' | 'review'>('form');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [reit, setReit] = useState<REIT>(sampleREIT);
  const [vendor_id, setVendorId] = useState<string>("");
  const [kdUrl, setKdUrl] = useState("");
  const [showKdInput, setShowKdInput] = useState(false);
  const [contactNumber, setContactNumber] = useState("");
  const [showContactInput, setShowContactInput] = useState(false);

  useEffect(() => {
    const vendorParam = searchParams.get('vendor');
    if (vendorParam) {
      setVendorId(vendorParam);
      setReit(prev => ({
        ...prev,
        vendor_id: vendorParam
      }));
    }
  }, [searchParams]);

  const handleBasicInfoSave = (basicInfo: REIT['basicInfo']) => {
    setReit(prev => ({ ...prev, basicInfo }));
  };

  const handleEssentialsSave = (essentials: REIT['essentials']) => {
    setReit(prev => ({ ...prev, essentials }));
  };

  const handleAboutSave = (about: REIT['about']) => {
    setReit(prev => ({ ...prev, about }));
  };

  const handleManagementSave = (managementTeam: REIT['managementTeam']) => {
    setReit(prev => ({ ...prev, managementTeam }));
  };

  const handleFeesSave = (fees: REIT['fees']) => {
    setReit(prev => ({ ...prev, fees }));
  };

  const handleInvestmentLimitsSave = (investmentLimits: REIT['investmentLimits']) => {
    setReit(prev => ({ ...prev, investmentLimits }));
  };

  const handleRatingsSave = (ratings: REIT['ratings']) => {
    setReit(prev => ({ ...prev, ratings }));
  };

  const handleHoldingsSave = (holdings: REIT['holdings']) => {
    setReit(prev => ({ ...prev, holdings }));
  };

  const handleReturnsSave = (returnsAndRankings: REIT['returnsAndRankings']) => {
    setReit(prev => ({ ...prev, returnsAndRankings }));
  };

  const handlePeerComparisonSave = (peerComparison: REIT['peerComparison']) => {
    setReit(prev => ({ ...prev, peerComparison }));
  };

  const handleFaqsSave = (faqs: REIT['faqs']) => {
    setReit(prev => ({ ...prev, faqs }));
  };

  const validateForm = () => {
    const errors = [];
    
    // Check required fields from basicInfo
    if (!reit.basicInfo?.reitName) errors.push("REIT Name is required");
    if (!reit.basicInfo?.marketCap) errors.push("Market Cap is required");
    if (!reit.basicInfo?.currency) errors.push("Currency is required");
    if (!reit.basicInfo?.current_price) errors.push("Current Price is required");
    if (!reit.basicInfo?.dividend_yield) errors.push("Dividend Yield is required");
    if (!reit.basicInfo?.minimum_investment) errors.push("Minimum Investment is required");
    
    // Check required fields from essentials
    if (!reit.essentials?.netAssetValue) errors.push("Net Asset Value is required");
    if (!reit.essentials?.date_of_nav) errors.push("Date of NAV is required");
    if (!reit.essentials?.rating) errors.push("Rating is required");
    if (!reit.essentials?.total_assets) errors.push("Total Assets is required");
    
    // Check required fields from about
    if (!reit.about?.description) errors.push("REIT Description is required");
    if (!reit.about?.investment_strategy) errors.push("Investment Strategy is required");
    if (!reit.about?.property_count) errors.push("Property Count is required");
    if (!reit.about?.geographic_focus) errors.push("Geographic Focus is required");
    
    // Check required fields from management
    if (!reit.managementTeam || reit.managementTeam.length === 0) {
      errors.push("At least one Management Team member is required");
    }
    
    // Check required fields from fees
    if (reit.fees?.managementFee === undefined || reit.fees?.managementFee === 0) {
      errors.push("Management Fee is required");
    }
    
    // Check required fields from investment limits
    if (!reit.investmentLimits?.firstInvestment) errors.push("First Investment Limit is required");
    if (!reit.investmentLimits?.secondInvestment) errors.push("Second Investment Limit is required");
    if (!reit.investmentLimits?.thirdInvestment) errors.push("Third Investment Limit is required");
    
    // Check required fields from ratings
    if (reit.ratings?.Top5 === undefined) errors.push("Top 5 Rating is required");
    if (reit.ratings?.Top25 === undefined) errors.push("Top 25 Rating is required");
    
    // Check required fields from holdings
    if (!reit.holdings || reit.holdings.length === 0) {
      errors.push("At least one Holding is required");
    }
    
    // Check required fields from returns
    if (!reit.returnsAndRankings?.sector) errors.push("Sector is required");
    
    return errors;
  };

  const getFormProgress = () => {
    const errors = validateForm();
    const totalFields = 20; // Approximate number of required fields
    const filledFields = totalFields - errors.length;
    return Math.max(0, Math.min(100, (filledFields / totalFields) * 100));
  };

  const handleReview = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    setMode('review');
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const reitDataToSave = {
        ...reit,
        kdUrl,
        contactNumber,
        vendor_id,
        type: "REIT"
      };

      let response: any;
      if (reit.productId) {
        response = await updateReit(reit.productId, reitDataToSave);
        if (response && response.statusCode === 200) {
          toast.success("REIT updated successfully");
          setReit(prev => ({ ...prev, ...response.data }));
          setMode('form');
        }
      } else {
        response = await createReit(reitDataToSave);
        if (response && response.statusCode === 201) {
          toast.success("REIT created successfully");
          router.push('/product-hub');
        }
      }
      if (!response || (!response.statusCode && !response.id)) {
        throw new Error('Failed to save REIT');
      }
    } catch (error: any) {
      console.error('Error saving REIT:', error);
      toast.error(error.message || "Failed to save REIT");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitWithValidation = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    await handleSubmit();
  };

  const handleKdUrlSave = () => {
    if (kdUrl.trim()) {
      setShowKdInput(false);
      console.log('KD URL saved:', kdUrl);
      alert('KD URL saved successfully!');
    } else {
      alert('Please enter a valid KD URL');
    }
  };

  const handleKdUrlCancel = () => {
    setKdUrl("");
    setShowKdInput(false);
  };

  const handleContactSave = () => {
    if (contactNumber.trim()) {
      setShowContactInput(false);
      console.log('Contact number saved:', contactNumber);
      alert('Contact number saved successfully!');
    } else {
      alert('Please enter a valid contact number');
    }
  };

  const handleContactCancel = () => {
    setContactNumber("");
    setShowContactInput(false);
  };

  if (mode === 'review') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => setMode('form')}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Form</span>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">REIT Preview</h1>
                <p className="text-gray-600">Review your REIT before publishing</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowKdInput(true)}
                className="flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>KD URL</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowContactInput(true)}
                className="flex items-center space-x-2"
              >
                <MessageCircle className="w-4 h-4" />
                <span>Contact</span>
              </Button>
              <Button
                onClick={handleSubmitWithValidation}
                disabled={isSubmitting}
                className="admin_green_gradient hover:admin_green_gradient_hover"
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Saving...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Save className="w-4 h-4" />
                    <span>Save REIT</span>
                  </div>
                )}
              </Button>
            </div>
          </div>

          <REITPreview reit={reit} />

          {/* KD URL Modal */}
          {showKdInput && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-6 rounded-lg w-96">
                <h3 className="text-lg font-semibold mb-4">Enter KD URL</h3>
                <input
                  type="text"
                  value={kdUrl}
                  onChange={(e) => setKdUrl(e.target.value)}
                  placeholder="Enter KD URL"
                  className="w-full p-2 border border-gray-300 rounded mb-4"
                />
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={handleKdUrlCancel}>
                    Cancel
                  </Button>
                  <Button onClick={handleKdUrlSave}>Save</Button>
                </div>
              </div>
            </div>
          )}

          {/* Contact Number Modal */}
          {showContactInput && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-6 rounded-lg w-96">
                <h3 className="text-lg font-semibold mb-4">Enter Contact Number</h3>
                <input
                  type="text"
                  value={contactNumber}
                  onChange={(e) => setContactNumber(e.target.value)}
                  placeholder="Enter contact number"
                  className="w-full p-2 border border-gray-300 rounded mb-4"
                />
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={handleContactCancel}>
                    Cancel
                  </Button>
                  <Button onClick={handleContactSave}>Save</Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => window.history.back()}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Add New REIT</h1>
              <p className="text-gray-600">Create a new Real Estate Investment Trust</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-500">
              Progress: {getFormProgress().toFixed(0)}%
            </div>
            <Button
              onClick={handleReview}
              className="admin_green_gradient hover:admin_green_gradient_hover"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            <REITSummary reit={reit} onSave={handleBasicInfoSave} />
            <EssentialsCard reit={reit} onSave={handleEssentialsSave} />
            <REITAbout reit={reit} onSave={handleAboutSave} />
            <REITManagement reit={reit} onSave={handleManagementSave} />
            <AdvancedRatios reit={reit} onSave={handleRatingsSave} />
            <MinimumInvestment reit={reit} onSave={handleInvestmentLimitsSave} />
            <DividendYield reit={reit} onSave={handleFeesSave} />
            <REITPerformance reit={reit} />
            <REITHoldings reit={reit} onSave={handleHoldingsSave} />
            <PropertyAnalysis reit={reit} />
            <ReturnsRankings reit={reit} onSave={handleReturnsSave} />
            <PeerComparison reit={reit} onSave={handlePeerComparisonSave} />
            <FAQs reit={reit} onSave={handleFaqsSave} />
            <SimilarRecommendations reit={reit} />
          </div>

          {/* Progress Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Form Progress</h3>
                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-2">
                    <span>Completion</span>
                    <span>{getFormProgress().toFixed(0)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${getFormProgress()}%` }}
                    ></div>
                  </div>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Basic Info</span>
                    <span className={reit.basicInfo?.reitName ? "text-green-600" : "text-gray-400"}>
                      {reit.basicInfo?.reitName ? "✓" : "○"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Essentials</span>
                    <span className={reit.essentials?.netAssetValue ? "text-green-600" : "text-gray-400"}>
                      {reit.essentials?.netAssetValue ? "✓" : "○"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>About</span>
                    <span className={reit.about?.description ? "text-green-600" : "text-gray-400"}>
                      {reit.about?.description ? "✓" : "○"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Management</span>
                    <span className={reit.managementTeam?.length ? "text-green-600" : "text-gray-400"}>
                      {reit.managementTeam?.length ? "✓" : "○"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fees</span>
                    <span className={reit.fees?.managementFee ? "text-green-600" : "text-gray-400"}>
                      {reit.fees?.managementFee ? "✓" : "○"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Holdings</span>
                    <span className={reit.holdings?.length ? "text-green-600" : "text-gray-400"}>
                      {reit.holdings?.length ? "✓" : "○"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 