export interface Property {
  name: string;
  type: string;
  location: string;
  value: string;
  occupancy: string;
  yield: string;
}

export interface REITManager {
  name: string;
  position: string;
  experience: string;
  bio: string;
}

export interface REITFees {
  managementFee: number;
  performanceFee: number;
  acquisitionFee: number;
  dispositionFee: number;
}

export interface ChartPoint {
  date: Date;
  value: number;
}

export interface REITHolding {
  propertyName: string;
  propertyType: string;
  location: string;
  percentage: string;
  value: string;
  yield: string;
}

export interface ReturnSet {
  "1y": number;
  "3y": number;
  "5y": number;
  "All": number;
}

export interface ReturnComparison {
  reit_returns: ReturnSet;
  sector_average: ReturnSet;
  rank_within_sector: ReturnSet;
}

export interface PeerREIT {
  name: string;
  rating: number;
  returns1Y: number;
  returns3Y: number;
  marketCap: string;
  dividendYield: string;
}

export interface Faq {
  question: string;
  answer: string;
}

export interface REIT {
  productId: string;
  basicInfo: {
    reitName: string;
    logo_url: string;
    flags: string[];
    marketCap: string;
    currency: string;
    current_price: string;
    dividend_yield: string;
    minimum_investment: string;
  };
  kdUrl?: string;
  contactNumber?: string;
  essentials: {
    netAssetValue: number;
    date_of_nav: string;
    rating: string;
    min_sp_amount: string;
    total_assets: string;
  };
  about: {
    description: string;
    investment_strategy: string;
    property_count: string;
    geographic_focus: string;
  };
  managementTeam: REITManager[];
  fees: REITFees;
  investmentLimits: {
    firstInvestment: number;
    secondInvestment: number;
    thirdInvestment: number;
  };
  ratings: {
    Top5: number;
    Top25: number;
    OccupancyRate: number;
    DebtToEquity: number;
    Alpha: number;
    Beta: number;
    Sharpe: number;
    Sortino: number;
  };
  performanceChart: {
    chartData: ChartPoint[];
  };
  holdings: REITHolding[];
  returnsAndRankings: {
    sector: string;
    annualised_returns: ReturnComparison;
    absolute_returns: ReturnComparison;
  };
  peerComparison: {
    reits: PeerREIT[];
  };
  faqs: {
    questions: Faq[];
  };
  properties: Property[];
}

// Sample data with empty/default values
export const sampleREIT: REIT = {
  productId: "",
  basicInfo: {
    reitName: "",
    logo_url: "",
    flags: [],
    marketCap: "",
    currency: "",
    current_price: "",
    dividend_yield: "",
    minimum_investment: "",
  },
  essentials: {
    netAssetValue: 0,
    date_of_nav: "",
    rating: "",
    min_sp_amount: "",
    total_assets: "",
  },
  about: {
    description: "",
    investment_strategy: "",
    property_count: "",
    geographic_focus: "",
  },
  managementTeam: [],
  fees: {
    managementFee: 0,
    performanceFee: 0,
    acquisitionFee: 0,
    dispositionFee: 0,
  },
  investmentLimits: {
    firstInvestment: 0,
    secondInvestment: 0,
    thirdInvestment: 0,
  },
  ratings: {
    Top5: 0,
    Top25: 0,
    OccupancyRate: 0,
    DebtToEquity: 0,
    Alpha: 0,
    Beta: 0,
    Sharpe: 0,
    Sortino: 0,
  },
  performanceChart: {
    chartData: [],
  },
  holdings: [],
  returnsAndRankings: {
    sector: "",
    annualised_returns: {
      reit_returns: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
      sector_average: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
      rank_within_sector: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
    },
    absolute_returns: {
      reit_returns: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
      sector_average: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
      rank_within_sector: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
    },
  },
  peerComparison: {
    reits: [],
  },
  faqs: {
    questions: [],
  },
  properties: [],
}; 