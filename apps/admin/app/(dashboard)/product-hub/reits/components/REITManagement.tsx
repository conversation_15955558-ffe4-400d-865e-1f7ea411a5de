"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT, REITManager } from "../data";

interface REITManagementProps {
  reit: REIT;
  onSave: (managementTeam: REIT['managementTeam']) => void;
}

export default function REITManagement({ reit, onSave }: REITManagementProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [managementTeam, setManagementTeam] = useState<REITManager[]>(reit.managementTeam || []);

  const handleSave = () => {
    onSave(managementTeam);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setManagementTeam(reit.managementTeam || []);
    setIsEditing(false);
  };

  const addManager = () => {
    setManagementTeam([...managementTeam, { name: "", position: "", experience: "", bio: "" }]);
  };

  const removeManager = (index: number) => {
    setManagementTeam(managementTeam.filter((_, i) => i !== index));
  };

  const updateManager = (index: number, field: keyof REITManager, value: string) => {
    const updated = [...managementTeam];
    updated[index] = { ...updated[index], [field]: value };
    setManagementTeam(updated);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Management Team</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            {managementTeam.map((manager, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Manager {index + 1}</h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeManager(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    Remove
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                    <Input
                      value={manager.name}
                      onChange={(e) => updateManager(index, 'name', e.target.value)}
                      placeholder="Enter name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
                    <Input
                      value={manager.position}
                      onChange={(e) => updateManager(index, 'position', e.target.value)}
                      placeholder="Enter position"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                    <Input
                      value={manager.experience}
                      onChange={(e) => updateManager(index, 'experience', e.target.value)}
                      placeholder="e.g., 15 years"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                    <textarea
                      value={manager.bio}
                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => updateManager(index, 'bio', e.target.value)}
                      placeholder="Enter bio"
                      rows={3}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              onClick={addManager}
              className="w-full"
            >
              Add Manager
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {managementTeam.length === 0 ? (
              <p className="text-gray-500">No management team members added yet.</p>
            ) : (
              managementTeam.map((manager, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium mb-2">{manager.name || `Manager ${index + 1}`}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Position:</span>
                      <p className="text-gray-900">{manager.position || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Experience:</span>
                      <p className="text-gray-900">{manager.experience || "Not specified"}</p>
                    </div>
                    <div className="md:col-span-2">
                      <span className="text-gray-600">Bio:</span>
                      <p className="text-gray-900">{manager.bio || "Not specified"}</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 