"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface REITPreviewProps {
  reit: REIT;
}

export default function REITPreview({ reit }: REITPreviewProps) {
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>REIT Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {reit.basicInfo.reitName || "REIT Name"}
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Market Cap:</span>
                  <span className="font-medium">{reit.basicInfo.marketCap || "Not specified"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Current Price:</span>
                  <span className="font-medium">{reit.basicInfo.current_price || "Not specified"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Dividend Yield:</span>
                  <span className="font-medium text-green-600">{reit.basicInfo.dividend_yield || "Not specified"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Minimum Investment:</span>
                  <span className="font-medium">{reit.basicInfo.minimum_investment || "Not specified"}</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Essentials</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Net Asset Value:</span>
                  <span className="font-medium">{reit.essentials.netAssetValue || "Not specified"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rating:</span>
                  <span className="font-medium">{reit.essentials.rating || "Not specified"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Assets:</span>
                  <span className="font-medium">{reit.essentials.total_assets || "Not specified"}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* About Section */}
      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Description</h4>
              <p className="text-gray-700">{reit.about.description || "No description available"}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <span className="text-gray-600">Investment Strategy:</span>
                <p className="font-medium">{reit.about.investment_strategy || "Not specified"}</p>
              </div>
              <div>
                <span className="text-gray-600">Property Count:</span>
                <p className="font-medium">{reit.about.property_count || "Not specified"}</p>
              </div>
              <div>
                <span className="text-gray-600">Geographic Focus:</span>
                <p className="font-medium">{reit.about.geographic_focus || "Not specified"}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Management Team */}
      <Card>
        <CardHeader>
          <CardTitle>Management Team</CardTitle>
        </CardHeader>
        <CardContent>
          {reit.managementTeam.length === 0 ? (
            <p className="text-gray-500">No management team members added yet.</p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {reit.managementTeam.map((manager, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium mb-2">{manager.name || `Manager ${index + 1}`}</h4>
                  <div className="space-y-1 text-sm">
                    <div><span className="text-gray-600">Position:</span> {manager.position || "Not specified"}</div>
                    <div><span className="text-gray-600">Experience:</span> {manager.experience || "Not specified"}</div>
                    <div><span className="text-gray-600">Bio:</span> {manager.bio || "Not specified"}</div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Fees */}
      <Card>
        <CardHeader>
          <CardTitle>Fees & Charges</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Management Fee:</span>
              <span className="font-medium">{reit.fees.managementFee ? `${reit.fees.managementFee}%` : "Not specified"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Performance Fee:</span>
              <span className="font-medium">{reit.fees.performanceFee ? `${reit.fees.performanceFee}%` : "Not specified"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Acquisition Fee:</span>
              <span className="font-medium">{reit.fees.acquisitionFee ? `${reit.fees.acquisitionFee}%` : "Not specified"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Disposition Fee:</span>
              <span className="font-medium">{reit.fees.dispositionFee ? `${reit.fees.dispositionFee}%` : "Not specified"}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Property Holdings */}
      <Card>
        <CardHeader>
          <CardTitle>Property Holdings</CardTitle>
        </CardHeader>
        <CardContent>
          {reit.holdings.length === 0 ? (
            <p className="text-gray-500">No property holdings added yet.</p>
          ) : (
            <div className="space-y-4">
              {reit.holdings.map((holding, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium mb-2">{holding.propertyName || `Property ${index + 1}`}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div><span className="text-gray-600">Type:</span> {holding.propertyType || "Not specified"}</div>
                    <div><span className="text-gray-600">Location:</span> {holding.location || "Not specified"}</div>
                    <div><span className="text-gray-600">Percentage:</span> {holding.percentage || "Not specified"}</div>
                    <div><span className="text-gray-600">Value:</span> {holding.value || "Not specified"}</div>
                    <div><span className="text-gray-600">Yield:</span> {holding.yield || "Not specified"}</div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 