"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface MinimumInvestmentProps {
  reit: REIT;
  onSave: (investmentLimits: REIT['investmentLimits']) => void;
}

export default function MinimumInvestment({ reit, onSave }: MinimumInvestmentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(reit.investmentLimits);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(reit.investmentLimits);
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Investment Limits</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                First Investment *
              </label>
              <Input
                type="number"
                value={formData.firstInvestment}
                onChange={(e) => setFormData({ ...formData, firstInvestment: parseFloat(e.target.value) || 0 })}
                placeholder="Enter amount"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Second Investment *
              </label>
              <Input
                type="number"
                value={formData.secondInvestment}
                onChange={(e) => setFormData({ ...formData, secondInvestment: parseFloat(e.target.value) || 0 })}
                placeholder="Enter amount"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Third Investment *
              </label>
              <Input
                type="number"
                value={formData.thirdInvestment}
                onChange={(e) => setFormData({ ...formData, thirdInvestment: parseFloat(e.target.value) || 0 })}
                placeholder="Enter amount"
              />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">First Investment</label>
              <p className="text-gray-900">${reit.investmentLimits.firstInvestment?.toLocaleString() || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Second Investment</label>
              <p className="text-gray-900">${reit.investmentLimits.secondInvestment?.toLocaleString() || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Third Investment</label>
              <p className="text-gray-900">${reit.investmentLimits.thirdInvestment?.toLocaleString() || "Not specified"}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 