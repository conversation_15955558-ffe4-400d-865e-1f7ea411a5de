"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface EssentialsCardProps {
  reit: REIT;
  onSave: (essentials: REIT['essentials']) => void;
}

export default function EssentialsCard({ reit, onSave }: EssentialsCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(reit.essentials);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(reit.essentials);
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Essentials</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Net Asset Value *
              </label>
              <Input
                type="number"
                value={formData.netAssetValue}
                onChange={(e) => setFormData({ ...formData, netAssetValue: parseFloat(e.target.value) || 0 })}
                placeholder="Enter NAV"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date of NAV *
              </label>
              <Input
                type="date"
                value={formData.date_of_nav}
                onChange={(e) => setFormData({ ...formData, date_of_nav: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rating *
              </label>
              <Input
                value={formData.rating}
                onChange={(e) => setFormData({ ...formData, rating: e.target.value })}
                placeholder="e.g., AAA"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Min SP Amount
              </label>
              <Input
                value={formData.min_sp_amount}
                onChange={(e) => setFormData({ ...formData, min_sp_amount: e.target.value })}
                placeholder="e.g., $500"
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Total Assets *
              </label>
              <Input
                value={formData.total_assets}
                onChange={(e) => setFormData({ ...formData, total_assets: e.target.value })}
                placeholder="e.g., $2.5B"
              />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Net Asset Value</label>
              <p className="text-gray-900">{reit.essentials.netAssetValue || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date of NAV</label>
              <p className="text-gray-900">{reit.essentials.date_of_nav || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Rating</label>
              <p className="text-gray-900">{reit.essentials.rating || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Min SP Amount</label>
              <p className="text-gray-900">{reit.essentials.min_sp_amount || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Total Assets</label>
              <p className="text-gray-900">{reit.essentials.total_assets || "Not specified"}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 