"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface AdvancedRatiosProps {
  reit: REIT;
  onSave: (ratings: REIT['ratings']) => void;
}

export default function AdvancedRatios({ reit, onSave }: AdvancedRatiosProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(reit.ratings);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(reit.ratings);
    setIsEditing(false);
  };

  const updateField = (field: keyof REIT['ratings'], value: number) => {
    setFormData({ ...formData, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Advanced Ratios</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Top 5 Rating *</label>
              <Input
                type="number"
                step="0.01"
                value={formData.Top5}
                onChange={(e) => updateField('Top5', parseFloat(e.target.value) || 0)}
                placeholder="Enter rating"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Top 25 Rating *</label>
              <Input
                type="number"
                step="0.01"
                value={formData.Top25}
                onChange={(e) => updateField('Top25', parseFloat(e.target.value) || 0)}
                placeholder="Enter rating"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Occupancy Rate (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.OccupancyRate}
                onChange={(e) => updateField('OccupancyRate', parseFloat(e.target.value) || 0)}
                placeholder="Enter occupancy rate"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Debt to Equity</label>
              <Input
                type="number"
                step="0.01"
                value={formData.DebtToEquity}
                onChange={(e) => updateField('DebtToEquity', parseFloat(e.target.value) || 0)}
                placeholder="Enter debt to equity ratio"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Alpha</label>
              <Input
                type="number"
                step="0.01"
                value={formData.Alpha}
                onChange={(e) => updateField('Alpha', parseFloat(e.target.value) || 0)}
                placeholder="Enter alpha"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Beta</label>
              <Input
                type="number"
                step="0.01"
                value={formData.Beta}
                onChange={(e) => updateField('Beta', parseFloat(e.target.value) || 0)}
                placeholder="Enter beta"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sharpe Ratio</label>
              <Input
                type="number"
                step="0.01"
                value={formData.Sharpe}
                onChange={(e) => updateField('Sharpe', parseFloat(e.target.value) || 0)}
                placeholder="Enter Sharpe ratio"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sortino Ratio</label>
              <Input
                type="number"
                step="0.01"
                value={formData.Sortino}
                onChange={(e) => updateField('Sortino', parseFloat(e.target.value) || 0)}
                placeholder="Enter Sortino ratio"
              />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Top 5 Rating</label>
              <p className="text-gray-900">{reit.ratings.Top5 || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Top 25 Rating</label>
              <p className="text-gray-900">{reit.ratings.Top25 || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Occupancy Rate</label>
              <p className="text-gray-900">{reit.ratings.OccupancyRate ? `${reit.ratings.OccupancyRate}%` : "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Debt to Equity</label>
              <p className="text-gray-900">{reit.ratings.DebtToEquity || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Alpha</label>
              <p className="text-gray-900">{reit.ratings.Alpha || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Beta</label>
              <p className="text-gray-900">{reit.ratings.Beta || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sharpe Ratio</label>
              <p className="text-gray-900">{reit.ratings.Sharpe || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sortino Ratio</label>
              <p className="text-gray-900">{reit.ratings.Sortino || "Not specified"}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 