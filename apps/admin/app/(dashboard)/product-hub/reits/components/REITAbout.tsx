"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface REITAboutProps {
  reit: REIT;
  onSave: (about: REIT['about']) => void;
}

export default function REITAbout({ reit, onSave }: REITAboutProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(reit.about);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(reit.about);
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>About</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter REIT description"
                rows={4}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Investment Strategy *
              </label>
              <textarea
                value={formData.investment_strategy}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, investment_strategy: e.target.value })}
                placeholder="Enter investment strategy"
                rows={3}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Property Count *
                </label>
                <Input
                  value={formData.property_count}
                  onChange={(e) => setFormData({ ...formData, property_count: e.target.value })}
                  placeholder="e.g., 25 properties"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Geographic Focus *
                </label>
                <Input
                  value={formData.geographic_focus}
                  onChange={(e) => setFormData({ ...formData, geographic_focus: e.target.value })}
                  placeholder="e.g., US East Coast"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <p className="text-gray-900">{reit.about.description || "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Investment Strategy</label>
              <p className="text-gray-900">{reit.about.investment_strategy || "Not specified"}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Property Count</label>
                <p className="text-gray-900">{reit.about.property_count || "Not specified"}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Geographic Focus</label>
                <p className="text-gray-900">{reit.about.geographic_focus || "Not specified"}</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 