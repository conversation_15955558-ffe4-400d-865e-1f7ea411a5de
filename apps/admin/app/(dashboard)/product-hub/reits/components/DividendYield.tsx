"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface DividendYieldProps {
  reit: REIT;
  onSave: (fees: REIT['fees']) => void;
}

export default function DividendYield({ reit, onSave }: DividendYieldProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(reit.fees);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(reit.fees);
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Fees & Dividend Information</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Management Fee (%) *
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.managementFee}
                onChange={(e) => setFormData({ ...formData, managementFee: parseFloat(e.target.value) || 0 })}
                placeholder="Enter management fee"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Performance Fee (%)
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.performanceFee}
                onChange={(e) => setFormData({ ...formData, performanceFee: parseFloat(e.target.value) || 0 })}
                placeholder="Enter performance fee"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Acquisition Fee (%)
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.acquisitionFee}
                onChange={(e) => setFormData({ ...formData, acquisitionFee: parseFloat(e.target.value) || 0 })}
                placeholder="Enter acquisition fee"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Disposition Fee (%)
              </label>
              <Input
                type="number"
                step="0.01"
                value={formData.dispositionFee}
                onChange={(e) => setFormData({ ...formData, dispositionFee: parseFloat(e.target.value) || 0 })}
                placeholder="Enter disposition fee"
              />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Management Fee</label>
              <p className="text-gray-900">{reit.fees.managementFee ? `${reit.fees.managementFee}%` : "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Performance Fee</label>
              <p className="text-gray-900">{reit.fees.performanceFee ? `${reit.fees.performanceFee}%` : "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Acquisition Fee</label>
              <p className="text-gray-900">{reit.fees.acquisitionFee ? `${reit.fees.acquisitionFee}%` : "Not specified"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Disposition Fee</label>
              <p className="text-gray-900">{reit.fees.dispositionFee ? `${reit.fees.dispositionFee}%` : "Not specified"}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 