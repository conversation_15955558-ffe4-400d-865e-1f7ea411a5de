"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT, Faq } from "../data";

interface FAQsProps {
  reit: REIT;
  onSave: (faqs: REIT['faqs']) => void;
}

export default function FAQs({ reit, onSave }: FAQsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [faqs, setFaqs] = useState<Faq[]>(reit.faqs.questions || []);

  const handleSave = () => {
    onSave({ questions: faqs });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFaqs(reit.faqs.questions || []);
    setIsEditing(false);
  };

  const addFaq = () => {
    setFaqs([...faqs, { question: "", answer: "" }]);
  };

  const removeFaq = (index: number) => {
    setFaqs(faqs.filter((_, i) => i !== index));
  };

  const updateFaq = (index: number, field: keyof Faq, value: string) => {
    const updated = [...faqs];
    updated[index] = { ...updated[index], [field]: value };
    setFaqs(updated);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>FAQs</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">FAQ {index + 1}</h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeFaq(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    Remove
                  </Button>
                </div>
                <div className="mb-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Question</label>
                  <Input
                    value={faq.question}
                    onChange={e => updateFaq(index, 'question', e.target.value)}
                    placeholder="Enter question"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Answer</label>
                  <Input
                    value={faq.answer}
                    onChange={e => updateFaq(index, 'answer', e.target.value)}
                    placeholder="Enter answer"
                  />
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              onClick={addFaq}
              className="w-full"
            >
              Add FAQ
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {faqs.length === 0 ? (
              <p className="text-gray-500">No FAQs added yet.</p>
            ) : (
              faqs.map((faq, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium mb-2">Q: {faq.question || `FAQ ${index + 1}`}</h4>
                  <p className="text-gray-900">A: {faq.answer || "Not specified"}</p>
                </div>
              ))
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 