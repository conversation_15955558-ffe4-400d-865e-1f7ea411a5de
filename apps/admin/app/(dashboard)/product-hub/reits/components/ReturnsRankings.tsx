"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface ReturnsRankingsProps {
  reit: REIT;
  onSave: (returnsAndRankings: REIT['returnsAndRankings']) => void;
}

export default function ReturnsRankings({ reit, onSave }: ReturnsRankingsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(reit.returnsAndRankings);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(reit.returnsAndRankings);
    setIsEditing(false);
  };

  const updateReturn = (type: 'annualised_returns' | 'absolute_returns', field: keyof typeof formData.annualised_returns.reit_returns, value: number) => {
    setFormData({
      ...formData,
      [type]: {
        ...formData[type],
        reit_returns: {
          ...formData[type].reit_returns,
          [field]: value,
        },
      },
    });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Returns & Rankings</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Sector *</label>
          <Input
            value={formData.sector}
            onChange={e => setFormData({ ...formData, sector: e.target.value })}
            placeholder="e.g., Commercial Real Estate"
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold mb-2">Annualised Returns (%)</h4>
            {Object.keys(formData.annualised_returns.reit_returns).map((period) => (
              <div key={period} className="mb-2">
                <label className="block text-xs text-gray-600 mb-1">{period}</label>
                <Input
                  type="number"
                  value={formData.annualised_returns.reit_returns[period as keyof typeof formData.annualised_returns.reit_returns]}
                  onChange={e => updateReturn('annualised_returns', period as keyof typeof formData.annualised_returns.reit_returns, parseFloat(e.target.value) || 0)}
                  placeholder={`Enter ${period} return`}
                />
              </div>
            ))}
          </div>
          <div>
            <h4 className="font-semibold mb-2">Absolute Returns (%)</h4>
            {Object.keys(formData.absolute_returns.reit_returns).map((period) => (
              <div key={period} className="mb-2">
                <label className="block text-xs text-gray-600 mb-1">{period}</label>
                <Input
                  type="number"
                  value={formData.absolute_returns.reit_returns[period as keyof typeof formData.absolute_returns.reit_returns]}
                  onChange={e => updateReturn('absolute_returns', period as keyof typeof formData.absolute_returns.reit_returns, parseFloat(e.target.value) || 0)}
                  placeholder={`Enter ${period} return`}
                />
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 