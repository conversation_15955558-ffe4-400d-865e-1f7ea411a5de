"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT, PeerREIT } from "../data";

interface PeerComparisonProps {
  reit: REIT;
  onSave: (peerComparison: REIT['peerComparison']) => void;
}

export default function PeerComparison({ reit, onSave }: PeerComparisonProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [peers, setPeers] = useState<PeerREIT[]>(reit.peerComparison.reits || []);

  const handleSave = () => {
    onSave({ reits: peers });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setPeers(reit.peerComparison.reits || []);
    setIsEditing(false);
  };

  const addPeer = () => {
    setPeers([...peers, { name: "", rating: 0, returns1Y: 0, returns3Y: 0, marketCap: "", dividendYield: "" }]);
  };

  const removePeer = (index: number) => {
    setPeers(peers.filter((_, i) => i !== index));
  };

  const updatePeer = (index: number, field: keyof PeerREIT, value: string | number) => {
    const updated = [...peers];
    updated[index] = { ...updated[index], [field]: value };
    setPeers(updated);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Peer Comparison</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            {peers.map((peer, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Peer REIT {index + 1}</h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removePeer(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    Remove
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                    <Input
                      value={peer.name}
                      onChange={e => updatePeer(index, 'name', e.target.value)}
                      placeholder="Enter REIT name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                    <Input
                      type="number"
                      value={peer.rating}
                      onChange={e => updatePeer(index, 'rating', parseFloat(e.target.value) || 0)}
                      placeholder="Enter rating"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">1Y Returns (%)</label>
                    <Input
                      type="number"
                      value={peer.returns1Y}
                      onChange={e => updatePeer(index, 'returns1Y', parseFloat(e.target.value) || 0)}
                      placeholder="Enter 1Y returns"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">3Y Returns (%)</label>
                    <Input
                      type="number"
                      value={peer.returns3Y}
                      onChange={e => updatePeer(index, 'returns3Y', parseFloat(e.target.value) || 0)}
                      placeholder="Enter 3Y returns"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Market Cap</label>
                    <Input
                      value={peer.marketCap}
                      onChange={e => updatePeer(index, 'marketCap', e.target.value)}
                      placeholder="e.g., $2.5B"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Dividend Yield</label>
                    <Input
                      value={peer.dividendYield}
                      onChange={e => updatePeer(index, 'dividendYield', e.target.value)}
                      placeholder="e.g., 4.5%"
                    />
                  </div>
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              onClick={addPeer}
              className="w-full"
            >
              Add Peer REIT
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {peers.length === 0 ? (
              <p className="text-gray-500">No peer REITs added yet.</p>
            ) : (
              peers.map((peer, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium mb-2">{peer.name || `Peer REIT ${index + 1}`}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Rating:</span>
                      <p className="text-gray-900">{peer.rating || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">1Y Returns:</span>
                      <p className="text-gray-900">{peer.returns1Y || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">3Y Returns:</span>
                      <p className="text-gray-900">{peer.returns3Y || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Market Cap:</span>
                      <p className="text-gray-900">{peer.marketCap || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Dividend Yield:</span>
                      <p className="text-gray-900">{peer.dividendYield || "Not specified"}</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 