"use client";
import React from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface SimilarRecommendationsProps {
  reit: REIT;
}

export default function SimilarRecommendations({ reit }: SimilarRecommendationsProps) {
  // Mock data for similar REITs
  const similarREITs = [
    {
      name: "Commercial Real Estate REIT",
      price: "$45.20",
      dividendYield: "4.2%",
      marketCap: "$2.1B",
      sector: "Commercial",
    },
    {
      name: "Residential REIT Fund",
      price: "$38.75",
      dividendYield: "3.8%",
      marketCap: "$1.8B",
      sector: "Residential",
    },
    {
      name: "Industrial Properties REIT",
      price: "$52.10",
      dividendYield: "4.5%",
      marketCap: "$3.2B",
      sector: "Industrial",
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Similar Recommendations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {similarREITs.map((similarREIT, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <h4 className="font-medium text-gray-900 mb-2">{similarREIT.name}</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Price:</span>
                  <span className="font-medium">{similarREIT.price}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Dividend Yield:</span>
                  <span className="font-medium text-green-600">{similarREIT.dividendYield}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Market Cap:</span>
                  <span className="font-medium">{similarREIT.marketCap}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Sector:</span>
                  <span className="font-medium">{similarREIT.sector}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 