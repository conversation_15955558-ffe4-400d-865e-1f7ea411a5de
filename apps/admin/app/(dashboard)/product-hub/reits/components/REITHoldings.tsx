"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT, REITHolding } from "../data";

interface REITHoldingsProps {
  reit: REIT;
  onSave: (holdings: REIT['holdings']) => void;
}

export default function REITHoldings({ reit, onSave }: REITHoldingsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [holdings, setHoldings] = useState<REITHolding[]>(reit.holdings || []);

  const handleSave = () => {
    onSave(holdings);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setHoldings(reit.holdings || []);
    setIsEditing(false);
  };

  const addHolding = () => {
    setHoldings([...holdings, { 
      propertyName: "", 
      propertyType: "", 
      location: "", 
      percentage: "", 
      value: "", 
      yield: "" 
    }]);
  };

  const removeHolding = (index: number) => {
    setHoldings(holdings.filter((_, i) => i !== index));
  };

  const updateHolding = (index: number, field: keyof REITHolding, value: string) => {
    const updated = [...holdings];
    updated[index] = { ...updated[index], [field]: value };
    setHoldings(updated);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Property Holdings</CardTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Edit
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="admin_green_gradient hover:admin_green_gradient_hover text-white"
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            {holdings.map((holding, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Property {index + 1}</h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeHolding(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    Remove
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Property Name</label>
                    <Input
                      value={holding.propertyName}
                      onChange={(e) => updateHolding(index, 'propertyName', e.target.value)}
                      placeholder="Enter property name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Property Type</label>
                    <Input
                      value={holding.propertyType}
                      onChange={(e) => updateHolding(index, 'propertyType', e.target.value)}
                      placeholder="e.g., Office, Retail, Residential"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <Input
                      value={holding.location}
                      onChange={(e) => updateHolding(index, 'location', e.target.value)}
                      placeholder="Enter location"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Percentage</label>
                    <Input
                      value={holding.percentage}
                      onChange={(e) => updateHolding(index, 'percentage', e.target.value)}
                      placeholder="e.g., 15%"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Value</label>
                    <Input
                      value={holding.value}
                      onChange={(e) => updateHolding(index, 'value', e.target.value)}
                      placeholder="e.g., $50M"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Yield</label>
                    <Input
                      value={holding.yield}
                      onChange={(e) => updateHolding(index, 'yield', e.target.value)}
                      placeholder="e.g., 6.5%"
                    />
                  </div>
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              onClick={addHolding}
              className="w-full"
            >
              Add Property
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {holdings.length === 0 ? (
              <p className="text-gray-500">No property holdings added yet.</p>
            ) : (
              holdings.map((holding, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium mb-2">{holding.propertyName || `Property ${index + 1}`}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Type:</span>
                      <p className="text-gray-900">{holding.propertyType || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Location:</span>
                      <p className="text-gray-900">{holding.location || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Percentage:</span>
                      <p className="text-gray-900">{holding.percentage || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Value:</span>
                      <p className="text-gray-900">{holding.value || "Not specified"}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Yield:</span>
                      <p className="text-gray-900">{holding.yield || "Not specified"}</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 