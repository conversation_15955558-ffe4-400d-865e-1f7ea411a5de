"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface PropertyAnalysisProps {
  reit: REIT;
}

export default function PropertyAnalysis({ reit }: PropertyAnalysisProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Property Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900">Total Properties</h4>
              <p className="text-2xl font-bold text-blue-600">{reit.properties.length}</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-green-900">Average Occupancy</h4>
              <p className="text-2xl font-bold text-green-600">
                {reit.properties.length > 0 
                  ? `${(reit.properties.reduce((acc, prop) => acc + parseFloat(prop.occupancy.replace('%', '') || '0'), 0) / reit.properties.length).toFixed(1)}%`
                  : "N/A"
                }
              </p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h4 className="font-medium text-purple-900">Average Yield</h4>
              <p className="text-2xl font-bold text-purple-600">
                {reit.properties.length > 0 
                  ? `${(reit.properties.reduce((acc, prop) => acc + parseFloat(prop.yield.replace('%', '') || '0'), 0) / reit.properties.length).toFixed(1)}%`
                  : "N/A"
                }
              </p>
            </div>
          </div>
          
          <div className="mt-6">
            <h4 className="font-medium mb-3">Property Distribution by Type</h4>
            <div className="space-y-2">
              {(() => {
                const typeCounts = reit.properties.reduce((acc, prop) => {
                  acc[prop.type] = (acc[prop.type] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>);
                
                return Object.entries(typeCounts).map(([type, count]) => (
                  <div key={type} className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{type}</span>
                    <span className="text-sm font-medium">{count} properties</span>
                  </div>
                ));
              })()}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 