"use client";
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { REIT } from "../data";

interface REITPerformanceProps {
  reit: REIT;
}

export default function REITPerformance({ reit }: REITPerformanceProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Chart</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
          <p className="text-gray-500">Performance chart will be displayed here</p>
        </div>
        <div className="mt-4 text-sm text-gray-600">
          <p>Chart data points: {reit.performanceChart.chartData.length}</p>
        </div>
      </CardContent>
    </Card>
  );
} 