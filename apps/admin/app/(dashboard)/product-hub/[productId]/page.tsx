"use client";
import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { Button } from "@admin/components/ui/button";
import { ArrowLeftIcon, ExternalLinkIcon, DownloadIcon, CalendarIcon, DollarSignIcon, TrendingUpIcon, ShieldIcon, UsersIcon } from "lucide-react";
import { Product } from "@admin/lib/productService";
import { productService } from "@admin/lib/productService";

export default function ProductDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const productId = params.productId as string;

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const productData = await productService.getProductById(productId);
        setProduct(productData);
      } catch (err) {
        setError("Failed to load product details");
        console.error("Error fetching product:", err);
      } finally {
        setLoading(false);
      }
    };

    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading product details...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || "Product not found"}</p>
          <Button onClick={() => router.back()}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const getProductSymbol = () => {
    return product.stock?.symbol || product.bond?.symbol || product.fund?.symbol || product.structured_product?.symbol || "N/A";
  };

  const getProductType = () => {
    if (product.stock) return "Stock";
    if (product.bond) return "Bond";
    if (product.fund) return "Fund";
    if (product.structured_product) return "Structured Product";
    return "Unknown";
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low Risk": return "bg-green-100 text-green-800";
      case "Medium Risk": return "bg-yellow-100 text-yellow-800";
      case "High Risk": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case "Available": return "bg-green-100 text-green-800";
      case "Limited Availability": return "bg-yellow-100 text-yellow-800";
      case "Unavailable": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="flex items-center space-x-2"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span>Back</span>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
                <p className="text-gray-600">Product ID: {product.product_id}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge className={getRiskColor(product.risk_level)}>
                {product.risk_level}
              </Badge>
              <Badge className={getAvailabilityColor(product.availability)}>
                {product.availability}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Basic Information</span>
                  <Badge variant="outline">{getProductType()}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{getProductSymbol()}</h3>
                    <p className="text-gray-600 text-sm">Symbol</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.name}</h3>
                    <p className="text-gray-600 text-sm">Product Name</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.product_type}</h3>
                    <p className="text-gray-600 text-sm">Product Type</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.currency}</h3>
                    <p className="text-gray-600 text-sm">Currency</p>
                  </div>
                  {product.description && (
                    <div className="md:col-span-2">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                      <p className="text-gray-600">{product.description}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSignIcon className="h-5 w-5" />
                  <span>Financial Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {product.minimum_investment && (
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">
                        ${product.minimum_investment.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">Minimum Investment</div>
                    </div>
                  )}
                  
                  {/* Stock specific data */}
                  {product.stock && (
                    <>
                      {product.stock.current_price && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">
                            ${product.stock.current_price}
                          </div>
                          <div className="text-sm text-gray-600">Current Price</div>
                        </div>
                      )}
                      {product.stock.market_cap && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-lg font-bold text-gray-900">
                            ${product.stock.market_cap.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600">Market Cap</div>
                        </div>
                      )}
                    </>
                  )}

                  {/* Bond specific data */}
                  {product.bond && (
                    <>
                      {product.bond.current_price && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">
                            ${product.bond.current_price}
                          </div>
                          <div className="text-sm text-gray-600">Current Price</div>
                        </div>
                      )}
                      {product.bond.yield && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">
                            {product.bond.yield}%
                          </div>
                          <div className="text-sm text-gray-600">Yield</div>
                        </div>
                      )}
                      {product.bond.coupon_rate && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">
                            {product.bond.coupon_rate}%
                          </div>
                          <div className="text-sm text-gray-600">Coupon Rate</div>
                        </div>
                      )}
                    </>
                  )}

                  {/* Fund specific data */}
                  {product.fund && (
                    <>
                      {product.fund.nav && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">
                            ${product.fund.nav}
                          </div>
                          <div className="text-sm text-gray-600">NAV</div>
                        </div>
                      )}
                      {product.fund.aum && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-lg font-bold text-gray-900">
                            ${product.fund.aum.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600">AUM</div>
                        </div>
                      )}
                      {product.fund.expense_ratio && (
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-gray-900">
                            {product.fund.expense_ratio}%
                          </div>
                          <div className="text-sm text-gray-600">Expense Ratio</div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Product Specific Details */}
            {(product.stock || product.bond || product.fund || product.structured_product) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUpIcon className="h-5 w-5" />
                    <span>{getProductType()} Specific Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {product.stock && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {product.stock.sector && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Sector</h4>
                          <p className="text-gray-600">{product.stock.sector}</p>
                        </div>
                      )}
                      {product.stock.risk_metrics && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Risk Metrics</h4>
                          <pre className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                            {JSON.stringify(product.stock.risk_metrics, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  )}

                  {product.bond && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {product.bond.credit_rating && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Credit Rating</h4>
                          <Badge variant="outline">{product.bond.credit_rating}</Badge>
                        </div>
                      )}
                      {product.bond.maturity_date && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Maturity Date</h4>
                          <p className="text-gray-600">{new Date(product.bond.maturity_date).toLocaleDateString()}</p>
                        </div>
                      )}
                      {product.bond.isin_code && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">ISIN Code</h4>
                          <p className="text-gray-600">{product.bond.isin_code}</p>
                        </div>
                      )}
                    </div>
                  )}

                  {product.fund && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">Fund Type</h4>
                        <p className="text-gray-600">{product.fund.fund_type}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">Trading Type</h4>
                        <div className="flex space-x-2">
                          {product.fund.exchange_traded && (
                            <Badge variant="outline">Exchange Traded</Badge>
                          )}
                          {product.fund.privately_held && (
                            <Badge variant="outline">Privately Held</Badge>
                          )}
                        </div>
                      </div>
                      {product.fund.dividend_yield && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Dividend Yield</h4>
                          <p className="text-gray-600">{product.fund.dividend_yield}%</p>
                        </div>
                      )}
                      {product.fund.performance_metrics && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Performance Metrics</h4>
                          <pre className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                            {JSON.stringify(product.fund.performance_metrics, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  )}

                  {product.structured_product && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {product.structured_product.strike_price && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Strike Price</h4>
                          <p className="text-gray-600">${product.structured_product.strike_price}</p>
                        </div>
                      )}
                      {product.structured_product.return_type && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Return Type</h4>
                          <p className="text-gray-600">{product.structured_product.return_type}</p>
                        </div>
                      )}
                      {product.structured_product.maturity_date && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">Maturity Date</h4>
                          <p className="text-gray-600">{new Date(product.structured_product.maturity_date).toLocaleDateString()}</p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Vendor & Issuer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <UsersIcon className="h-5 w-5" />
                  <span>Vendor & Issuer</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {product.vendor && (
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Vendor</h4>
                    <p className="text-gray-600">{product.vendor.name}</p>
                    <Badge variant="outline" className="mt-1">{product.vendor.type}</Badge>
                  </div>
                )}
                {product.issuer && (
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Issuer</h4>
                    <p className="text-gray-600">{product.issuer.name}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Timestamps */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CalendarIcon className="h-5 w-5" />
                  <span>Timestamps</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Created</h4>
                  <p className="text-gray-600 text-sm">
                    {new Date(product.created_at).toLocaleString()}
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Last Updated</h4>
                  <p className="text-gray-600 text-sm">
                    {new Date(product.updated_at).toLocaleString()}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full admin_green_gradient hover:admin_green_gradient_hover text-white">
                  <ExternalLinkIcon className="h-4 w-4 mr-2" />
                  View Market Data
                </Button>
                <Button variant="outline" className="w-full">
                  <DownloadIcon className="h-4 w-4 mr-2" />
                  Download Fact Sheet
                </Button>
                <Button variant="outline" className="w-full">
                  <ShieldIcon className="h-4 w-4 mr-2" />
                  View Compliance
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
} 