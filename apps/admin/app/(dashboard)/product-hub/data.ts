import {
  Table,
  ShieldIcon,
  CircleCheckIcon,
  TrendingUpIcon,
} from "lucide-react";
import { OverviewCard, Product, ProductCategory } from "./types";

export const productsData: Product[] = [
  {
    symbol: "AAPL",
    name: "Apple Inc.",
    price: "$185.42",
    change: "+2.15%",
    isPositive: true,
    volume: "Vol: 52.3M",
    marketCap: "Cap: $2.87T",
    sector: "Technology",
    risk: "Medium Risk",
    riskColor: "bg-yellow-100 text-yellow-800",
    availability: "Available",
    brokers: ["IB", "Schwab", "Fidelity"],
  },
  {
    symbol: "TSLA",
    name: "Tesla Inc.",
    price: "$239.85",
    change: "-1.45%",
    isPositive: false,
    volume: "Vol: 89.7M",
    marketCap: "Cap: $762B",
    sector: "Automotive",
    risk: "High Risk",
    riskColor: "bg-red-100 text-red-800",
    availability: "Available",
    brokers: ["IB", "TD", "E*TRADE"],
  },
  {
    symbol: "NVDA",
    name: "NVIDIA Corporation",
    price: "$875.30",
    change: "+5.67%",
    isPositive: true,
    volume: "Vol: 34.1M",
    marketCap: "Cap: $2.15T",
    sector: "Technology",
    risk: "High Risk",
    riskColor: "bg-red-100 text-red-800",
    availability: "Available",
    brokers: ["IB", "Schwab", "Fidelity", "TD"],
  },
];

export const productCategories: ProductCategory[] = [
  { name: "Stocks", count: "2847", isActive: true },
  { name: "ETFs", count: "892", isActive: false },
  { name: "Crypto ETF", count: "45", isActive: false },
  { name: "Crypto-Funds", count: "456", isActive: false },
  { name: "Bonds", count: "1235", isActive: false },
  { name: "Mutual Funds", count: "678", isActive: false },
  { name: "REITs", count: "234", isActive: false },
  { name: "Structured Products", count: "89", isActive: false },
  { name: "Private Market", count: "156", isActive: false },
];

export const overviewCards: OverviewCard[] = [
  {
    title: "Vendor Coverage",
    icon: "Table",
    iconBgColor: "bg-blue-100",
    iconColor: "text-blue-600",
    description: "Products from 8 major financial institutions",
    details: [
      { type: "text", value: "Top: Goldman Sachs, JP Morgan, Morgan Stanley" },
    ],
  },
  {
    title: "Risk Distribution",
    icon: "Shield",
    iconBgColor: "bg-[#EDF8F2]",
    iconColor: "text-[#05A049]",
    description: "Balanced portfolio across risk levels",
    details: [
      { type: "keyValue", label: "Low Risk", value: 1456 },
      { type: "keyValue", label: "Medium Risk", value: 2134 },
      { type: "keyValue", label: "High Risk", value: 1037 },
    ],
  },
  {
    title: "Availability Status",
    icon: "CircleCheck",
    iconBgColor: "bg-yellow-100",
    iconColor: "text-yellow-600",
    description: "Real-time product availability tracking",
    details: [
      { type: "keyValue", label: "Available", value: 4234 },
      { type: "keyValue", label: "Limited Availability", value: 298 },
      { type: "keyValue", label: "Unavailable", value: 95 },
    ],
  },
  {
    title: "Product Types",
    icon: "TrendingUp",
    iconBgColor: "bg-purple-100",
    iconColor: "text-purple-600",
    description: "Equity and credit-based instruments",
    details: [
      { type: "keyValue", label: "Equity Based", value: 3247 },
      { type: "keyValue", label: "Credit Based", value: 1380 },
    ],
  },
];
