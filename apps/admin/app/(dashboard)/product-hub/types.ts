export type CardDetail = {
  type: "keyValue" | "text";
  label?: string;
  value: string | number;
};

export type OverviewCard = {
  title: string;
  icon: string;
  iconBgColor: string;
  iconColor: string;
  description: string;
  details: CardDetail[];
};

export type Product = {
  symbol: string;
  name: string;
  price: string;
  change: string;
  isPositive: boolean;
  volume: string;
  marketCap: string;
  sector: string;
  risk: string;
  riskColor: string;
  availability: string;
  brokers: string[];
};

export type ProductCategory = {
  name: string;
  count: string;
  isActive: boolean;
};
