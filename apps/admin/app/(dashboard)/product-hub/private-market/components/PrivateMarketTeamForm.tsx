"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { InvestmentTeam } from "../data";

interface PrivateMarketTeamFormProps {
  investmentTeam: InvestmentTeam[];
  onSave: (investmentTeam: InvestmentTeam[]) => void;
}

export default function PrivateMarketTeamForm({ investmentTeam, onSave }: PrivateMarketTeamFormProps) {
  const [formData, setFormData] = useState<InvestmentTeam[]>(investmentTeam);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(investmentTeam);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Investment Team</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {formData.map((member, index) => (
          <div key={index} className="p-4 border border-gray-200 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <Input
                  value={member.name}
                  onChange={(e) => {
                    const newTeam = [...formData];
                    newTeam[index].name = e.target.value;
                    setFormData(newTeam);
                  }}
                  disabled={!isEditing}
                  placeholder="Enter name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Title
                </label>
                <Input
                  value={member.title}
                  onChange={(e) => {
                    const newTeam = [...formData];
                    newTeam[index].title = e.target.value;
                    setFormData(newTeam);
                  }}
                  disabled={!isEditing}
                  placeholder="Enter title"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
} 