"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { PastPerformance } from "../data";

interface PrivateMarketPerformanceFormProps {
  pastPerformance: PastPerformance;
  onSave: (pastPerformance: PastPerformance) => void;
}

export default function PrivateMarketPerformanceForm({ pastPerformance, onSave }: PrivateMarketPerformanceFormProps) {
  const [formData, setFormData] = useState<PastPerformance>(pastPerformance);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(pastPerformance);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Past Performance</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label htmlFor="annualizedDistributionRate" className="block text-sm font-medium text-gray-700 mb-1">
            Annualized Distribution Rate (%)
          </label>
          <Input
            id="annualizedDistributionRate"
            type="number"
            step="0.01"
            value={formData.annualizedDistributionRate}
            onChange={(e) => setFormData({ ...formData, annualizedDistributionRate: parseFloat(e.target.value) || 0 })}
            disabled={!isEditing}
            placeholder="8.37"
          />
        </div>
        <div>
          <label htmlFor="totalReturn" className="block text-sm font-medium text-gray-700 mb-1">
            Total Return
          </label>
          <Input
            id="totalReturn"
            value={formData.totalReturn}
            onChange={(e) => setFormData({ ...formData, totalReturn: e.target.value })}
            disabled={!isEditing}
            placeholder="$100K"
          />
        </div>
      </div>
    </Card>
  );
} 