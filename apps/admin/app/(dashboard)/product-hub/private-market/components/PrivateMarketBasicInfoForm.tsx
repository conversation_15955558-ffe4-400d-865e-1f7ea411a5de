"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";

import { BasicInfo } from "../data";

interface PrivateMarketBasicInfoFormProps {
  basicInfo: BasicInfo;
  onSave: (basicInfo: BasicInfo) => void;
}

export default function PrivateMarketBasicInfoForm({ basicInfo, onSave }: PrivateMarketBasicInfoFormProps) {
  const [formData, setFormData] = useState<BasicInfo>(basicInfo);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(basicInfo);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <label htmlFor="fundName" className="block text-sm font-medium text-gray-700 mb-1">
              Fund Name *
            </label>
            <Input
              id="fundName"
              value={formData.fundName}
              onChange={(e) => setFormData({ ...formData, fundName: e.target.value })}
              disabled={!isEditing}
              placeholder="Enter fund name"
            />
          </div>

          <div>
            <label htmlFor="fundManager" className="block text-sm font-medium text-gray-700 mb-1">
              Fund Manager *
            </label>
            <Input
              id="fundManager"
              value={formData.fundManager}
              onChange={(e) => setFormData({ ...formData, fundManager: e.target.value })}
              disabled={!isEditing}
              placeholder="Enter fund manager"
            />
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as "OPEN" | "CLOSED" | "COMING_SOON" })}
              disabled={!isEditing}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
            >
              <option value="OPEN">Open</option>
              <option value="CLOSED">Closed</option>
              <option value="COMING_SOON">Coming Soon</option>
            </select>
          </div>

          <div>
            <label htmlFor="minInvestment" className="block text-sm font-medium text-gray-700 mb-1">
              Minimum Investment *
            </label>
            <Input
              id="minInvestment"
              value={formData.minInvestment}
              onChange={(e) => setFormData({ ...formData, minInvestment: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., AED 100k"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label htmlFor="targetIRR" className="block text-sm font-medium text-gray-700 mb-1">
              Target IRR (%) *
            </label>
            <Input
              id="targetIRR"
              type="number"
              value={formData.targetIRR}
              onChange={(e) => setFormData({ ...formData, targetIRR: parseFloat(e.target.value) || 0 })}
              disabled={!isEditing}
              placeholder="18"
            />
          </div>

          <div>
            <label htmlFor="targetMOIC" className="block text-sm font-medium text-gray-700 mb-1">
              Target MOIC (x) *
            </label>
            <Input
              id="targetMOIC"
              type="number"
              step="0.1"
              value={formData.targetMOIC}
              onChange={(e) => setFormData({ ...formData, targetMOIC: parseFloat(e.target.value) || 0 })}
              disabled={!isEditing}
              placeholder="2.5"
            />
          </div>

          <div>
            <label htmlFor="vintage" className="block text-sm font-medium text-gray-700 mb-1">
              Vintage
            </label>
            <Input
              id="vintage"
              value={formData.vintage}
              onChange={(e) => setFormData({ ...formData, vintage: e.target.value })}
              disabled={!isEditing}
              placeholder="2024"
            />
          </div>

          <div>
            <label htmlFor="tenor" className="block text-sm font-medium text-gray-700 mb-1">
              Tenor
            </label>
            <Input
              id="tenor"
              value={formData.tenor}
              onChange={(e) => setFormData({ ...formData, tenor: e.target.value })}
              disabled={!isEditing}
              placeholder="Evergreen"
            />
          </div>
        </div>
      </div>
    </Card>
  );
} 