"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Pricing } from "../data";

interface PrivateMarketPricingFormProps {
  pricing: Pricing;
  onSave: (pricing: Pricing) => void;
}

export default function PrivateMarketPricingForm({ pricing, onSave }: PrivateMarketPricingFormProps) {
  const [formData, setFormData] = useState<Pricing>(pricing);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(pricing);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Pricing</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label htmlFor="valuraFee" className="block text-sm font-medium text-gray-700 mb-1">
            Valura Fee (%)
          </label>
          <Input
            id="valuraFee"
            type="number"
            step="0.01"
            value={formData.valuraFee}
            onChange={(e) => setFormData({ ...formData, valuraFee: parseFloat(e.target.value) || 0 })}
            disabled={!isEditing}
            placeholder="0.50"
          />
        </div>
        <div>
          <label htmlFor="fundManagementFee" className="block text-sm font-medium text-gray-700 mb-1">
            Fund Management Fee (%)
          </label>
          <Input
            id="fundManagementFee"
            type="number"
            step="0.01"
            value={formData.fundManagementFee}
            onChange={(e) => setFormData({ ...formData, fundManagementFee: parseFloat(e.target.value) || 0 })}
            disabled={!isEditing}
            placeholder="1.00"
          />
        </div>
        <div>
          <label htmlFor="fundIncentiveFee" className="block text-sm font-medium text-gray-700 mb-1">
            Fund Incentive Fee (%)
          </label>
          <Input
            id="fundIncentiveFee"
            type="number"
            step="0.01"
            value={formData.fundIncentiveFee}
            onChange={(e) => setFormData({ ...formData, fundIncentiveFee: parseFloat(e.target.value) || 0 })}
            disabled={!isEditing}
            placeholder="15"
          />
        </div>
        <div>
          <label htmlFor="accessFundFee" className="block text-sm font-medium text-gray-700 mb-1">
            Access Fund Fee (%)
          </label>
          <Input
            id="accessFundFee"
            type="number"
            step="0.01"
            value={formData.accessFundFee}
            onChange={(e) => setFormData({ ...formData, accessFundFee: parseFloat(e.target.value) || 0 })}
            disabled={!isEditing}
            placeholder="0.25"
          />
        </div>
      </div>
    </Card>
  );
} 