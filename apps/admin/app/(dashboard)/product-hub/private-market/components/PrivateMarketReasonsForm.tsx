"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Textarea } from "@admin/components/ui/textarea";
import { ReasonToConsider } from "../data";

interface PrivateMarketReasonsFormProps {
  reasonsToConsider: ReasonToConsider[];
  onSave: (reasonsToConsider: ReasonToConsider[]) => void;
}

export default function PrivateMarketReasonsForm({ reasonsToConsider, onSave }: PrivateMarketReasonsFormProps) {
  const [formData, setFormData] = useState<ReasonToConsider[]>(reasonsToConsider);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(reasonsToConsider);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Reasons to Consider</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {formData.map((reason, index) => (
          <div key={reason.id} className="p-4 border border-gray-200 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Title
                </label>
                <Input
                  value={reason.title}
                  onChange={(e) => {
                    const newReasons = [...formData];
                    newReasons[index].title = e.target.value;
                    setFormData(newReasons);
                  }}
                  disabled={!isEditing}
                  placeholder="Enter title"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Icon
                </label>
                <Input
                  value={reason.icon}
                  onChange={(e) => {
                    const newReasons = [...formData];
                    newReasons[index].icon = e.target.value;
                    setFormData(newReasons);
                  }}
                  disabled={!isEditing}
                  placeholder="Enter icon name"
                />
              </div>
            </div>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <Textarea
                value={reason.description}
                onChange={(e) => {
                  const newReasons = [...formData];
                  newReasons[index].description = e.target.value;
                  setFormData(newReasons);
                }}
                disabled={!isEditing}
                placeholder="Enter description"
              />
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
} 