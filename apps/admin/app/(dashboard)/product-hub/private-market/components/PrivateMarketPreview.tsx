"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@admin/components/ui/button";
import { Card } from "@admin/components/ui/card";
import { ArrowLeft, Download, MessageCircle, Play, ExternalLink, ChevronDown, ChevronUp, TrendingUp, Thermometer, PieChart, BarChart3, Handshake, CheckCircle, Target, Snowflake, DollarSign, Calendar, List, Users, Pie<PERSON>hart as PieChartIcon, HelpCircle, Info, AlertCircle, ArrowRight, Globe } from "lucide-react";
import { PrivateMarketProduct } from "../data";

interface PrivateMarketPreviewProps {
  privateMarket: PrivateMarketProduct;
  onBack: () => void;
  onSubmit: () => void;
  isSubmitting: boolean;
}

export default function PrivateMarketPreview({ 
  privateMarket, 
  onBack, 
  onSubmit, 
  isSubmitting 
}: PrivateMarketPreviewProps) {
  const [expandedFAQs, setExpandedFAQs] = useState<number[]>([]);

  const toggleFAQ = (index: number) => {
    setExpandedFAQs(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'trending-up': return <TrendingUp className="w-5 h-5" />;
      case 'thermometer': return <Thermometer className="w-5 h-5" />;
      case 'pie-chart': return <PieChart className="w-5 h-5" />;
      case 'bar-chart-3': return <BarChart3 className="w-5 h-5" />;
      case 'handshake': return <Handshake className="w-5 h-5" />;
      default: return <Info className="w-5 h-5" />;
    }
  };

  return (
    <div className="w-full h-full overflow-y-auto bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Form
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">iCapital Access Fund</h1>
              <p className="text-gray-600 text-sm">Preview your private market product</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Download className="w-4 h-4 mr-2" />
              Downloads
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact Advisor
            </Button>
            <Button
              size="sm"
              onClick={onSubmit}
              disabled={isSubmitting}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Saving...
                </div>
              ) : (
                <>
                  Invest Now
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Left Sidebar - Fund Overview */}
        <div className="w-1/3 p-6 space-y-6">
          {/* Fund Card */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold text-gray-600">iC</span>
                </div>
                <h2 className="text-lg font-semibold text-gray-900">{privateMarket.basicInfo.fundName}</h2>
              </div>
              <span className="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                {privateMarket.basicInfo.status}
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{privateMarket.basicInfo.targetIRR}%</div>
                <div className="text-sm text-gray-600">Target IRR</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{privateMarket.basicInfo.targetMOIC}x</div>
                <div className="text-sm text-gray-600">Target MOIC</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">{privateMarket.basicInfo.minInvestment}</div>
                <div className="text-sm text-gray-600">Min. Inv.</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">{privateMarket.basicInfo.vintage}</div>
                <div className="text-sm text-gray-600">Vintage</div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">{privateMarket.basicInfo.tenor}</div>
              <div className="text-sm text-gray-600">Tenor</div>
            </div>
          </Card>

          {/* Essentials */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Essentials</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Fund size:</span>
                <span className="font-medium">{privateMarket.essentials.fundSize}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Inception:</span>
                <span className="font-medium">{privateMarket.essentials.inception}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Leverage:</span>
                <span className="font-medium">{privateMarket.essentials.leverage}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Annualized Distribution Rate:</span>
                <span className="font-medium">{privateMarket.essentials.annualizedDistributionRate}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Share class:</span>
                <span className="font-medium">{privateMarket.essentials.shareClass}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Distributions:</span>
                <span className="font-medium">{privateMarket.essentials.distributions}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Subscription timing:</span>
                <span className="font-medium">{privateMarket.essentials.subscriptionTiming}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Repurchase:</span>
                <span className="font-medium">{privateMarket.essentials.repurchase}</span>
              </div>
            </div>
          </Card>
        </div>

        {/* Right Content - Scrollable */}
        <div className="w-2/3 p-6 space-y-8 overflow-y-auto">
          {/* About Section */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <HelpCircle className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">About</h3>
            </div>
            <p className="text-gray-700 mb-6">{privateMarket.aboutSection.description}</p>
            
            <div className="grid grid-cols-2 gap-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{privateMarket.aboutSection.assetsUnderManagement}</div>
                  <div className="text-sm text-gray-600">Assets under management</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{privateMarket.aboutSection.experienceYears} years</div>
                  <div className="text-sm text-gray-600">of experience in Private Credit</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{privateMarket.aboutSection.employees}</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                  <Globe className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{privateMarket.aboutSection.offices}</div>
                  <div className="text-sm text-gray-600">across {privateMarket.aboutSection.continents} continents</div>
                </div>
              </div>
            </div>
          </Card>

          {/* 5 Reasons to Consider */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-6">
              <Info className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">5 reasons to consider</h3>
            </div>
            <div className="space-y-4">
              {privateMarket.reasonsToConsider.map((reason, index) => (
                <div key={reason.id} className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-green-600">#{index + 1}</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-semibold text-gray-900">{reason.title}</h4>
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        {getIconComponent(reason.icon)}
                      </div>
                    </div>
                    <p className="text-gray-700 text-sm">{reason.description}</p>
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </div>
              ))}
            </div>
          </Card>

          {/* Key Considerations */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <CheckCircle className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Key Considerations</h3>
            </div>
            <p className="text-gray-700">{privateMarket.keyConsideration.description}</p>
          </Card>

          {/* Ideal for Investors Who */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <Target className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Ideal for investors who</h3>
            </div>
            <ul className="space-y-2">
              {privateMarket.idealInvestor.criteria.map((criterion, index) => (
                <li key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-gray-700">{criterion}</span>
                </li>
              ))}
            </ul>
          </Card>

          {/* Insights from PM's Desk */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <Play className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Insights from the PM's Desk - Jan 2024</h3>
            </div>
            <div className="relative h-48 bg-gray-200 rounded-lg flex items-center justify-center">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg">
                <Play className="w-8 h-8 text-gray-600 ml-1" />
              </div>
            </div>
          </Card>

          {/* Past Performance */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <Info className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Past Performance</h3>
            </div>
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Snowflake className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{privateMarket.pastPerformance.annualizedDistributionRate}%</div>
                  <div className="text-sm text-gray-600">Annualized Distribution Rate</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{privateMarket.pastPerformance.totalReturn}</div>
                  <div className="text-sm text-gray-600">Total Return (since inception)</div>
                </div>
              </div>
            </div>
            
            {/* Performance Chart Placeholder */}
            <div className="h-32 bg-gray-100 rounded-lg flex items-center justify-center">
              <span className="text-gray-500">Performance Chart</span>
            </div>
          </Card>

          {/* Cash Flow Simulator */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <Info className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Cash Flow Simulator</h3>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Invested Capital</label>
              <input
                type="text"
                defaultValue={privateMarket.cashFlowSimulator.defaultInvestment}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Investment Phase</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-300 rounded-full"></div>
                <span className="text-sm text-gray-600">Distribution Phase</span>
              </div>
            </div>
            <div className="h-32 bg-gray-100 rounded-lg flex items-center justify-center">
              <span className="text-gray-500">Cash Flow Chart</span>
            </div>
          </Card>

          {/* Investment Due Dates */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-6">
              <Calendar className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Investment Due Dates</h3>
            </div>
            <div className="space-y-4">
              {privateMarket.investmentDueDates.map((dueDate, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-green-600">{index + 1}</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-1">
                      <h4 className="font-semibold text-gray-900">{dueDate.step}</h4>
                      <span className="text-sm text-gray-500">{dueDate.date}</span>
                    </div>
                    <p className="text-gray-700 text-sm">{dueDate.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Pricing */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <List className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Pricing</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Valura Fee:</span>
                <span className="font-medium">{privateMarket.pricing.valuraFee}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Fund management fee:</span>
                <span className="font-medium">{privateMarket.pricing.fundManagementFee}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Fund incentive fee:</span>
                <span className="font-medium">{privateMarket.pricing.fundIncentiveFee}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Access Fund Fee:</span>
                <span className="font-medium">{privateMarket.pricing.accessFundFee}%</span>
              </div>
            </div>
          </Card>

          {/* Sample Companies */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <Info className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Sample Companies</h3>
            </div>
            <div className="grid grid-cols-5 gap-4">
              {privateMarket.sampleCompanies.map((company, index) => (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                    <span className="text-xs font-semibold text-gray-600">{company.name.charAt(0)}</span>
                  </div>
                  <div className="text-sm font-medium text-gray-900">{company.name}</div>
                  <div className="text-xs text-gray-500">{company.percentage}%</div>
                </div>
              ))}
            </div>
          </Card>

          {/* Investment Team */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <Users className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Investment Team</h3>
            </div>
            <div className="space-y-4">
              {privateMarket.investmentTeam.map((member, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-gray-600">{member.name.split(' ').map(n => n[0]).join('')}</span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">{member.name}</div>
                    <div className="text-sm text-gray-600">{member.title}</div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Investment Composition */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <PieChartIcon className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Investment Composition</h3>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Portfolio Strategy</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Deep Lending:</span>
                    <span className="font-medium">{privateMarket.investmentComposition.portfolioStrategy.deepLending}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Opportunistic Credit:</span>
                    <span className="font-medium">{privateMarket.investmentComposition.portfolioStrategy.opportunisticCredit}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Structured Credit:</span>
                    <span className="font-medium">{privateMarket.investmentComposition.portfolioStrategy.structuredCredit}%</span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Industries</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Other:</span>
                    <span className="font-medium">{privateMarket.investmentComposition.industries.other}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Software:</span>
                    <span className="font-medium">{privateMarket.investmentComposition.industries.software}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Finance, Insurance & Real Estate:</span>
                    <span className="font-medium">{privateMarket.investmentComposition.industries.financeInsuranceRealEstate}%</span>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* FAQs */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">FAQs</h3>
            <div className="space-y-3">
              {privateMarket.faqs.map((faq, index) => (
                <div key={index} className="border border-gray-200 rounded-lg">
                  <button
                    className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50"
                    onClick={() => toggleFAQ(index)}
                  >
                    <span className="font-medium text-gray-900">{faq.question}</span>
                    {expandedFAQs.includes(index) ? (
                      <ChevronUp className="w-4 h-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    )}
                  </button>
                  {expandedFAQs.includes(index) && (
                    <div className="px-4 pb-3">
                      <p className="text-gray-700 text-sm">{faq.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card>

          {/* Data Room */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Room</h3>
            <div className="space-y-3">
              {privateMarket.dataRoom.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-900">{item.title}</span>
                  <ExternalLink className="w-4 h-4 text-gray-500" />
                </div>
              ))}
            </div>
          </Card>

          {/* Similar Recommendations */}
          <Card className="p-6 bg-white rounded-xl shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Similar Recommendations</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-semibold text-blue-600">A</span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">AMZN</div>
                      <div className="text-sm text-gray-600">Amazon Inc Big Cap</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-green-600 font-semibold">+5.63%</div>
                    <div className="text-sm text-gray-600">Current Value</div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">AUD 203.65</div>
                <div className="h-8 bg-gray-100 rounded mt-2 flex items-center justify-center">
                  <span className="text-xs text-gray-500">Chart</span>
                </div>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-semibold text-blue-600">A</span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">AMZN</div>
                      <div className="text-sm text-gray-600">Amazon Inc Big Cap</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-green-600 font-semibold">+5.63%</div>
                    <div className="text-sm text-gray-600">Current Value</div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">AUD 203.65</div>
                <div className="h-8 bg-gray-100 rounded mt-2 flex items-center justify-center">
                  <span className="text-xs text-gray-500">Chart</span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}