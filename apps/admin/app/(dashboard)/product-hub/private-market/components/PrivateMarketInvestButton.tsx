import React from "react";
import { But<PERSON> } from "@admin/components/ui/button";

interface PrivateMarketInvestButtonProps {
  onClick: () => void;
  disabled?: boolean;
}

export default function PrivateMarketInvestButton({ onClick, disabled }: PrivateMarketInvestButtonProps) {
  return (
    <Button
      className="w-full py-3 text-lg font-bold rounded-xl admin_green_gradient hover:admin_green_gradient_hover text-white shadow-md"
      onClick={onClick}
      disabled={disabled}
      size="lg"
    >
      Invest Now
    </Button>
  );
} 