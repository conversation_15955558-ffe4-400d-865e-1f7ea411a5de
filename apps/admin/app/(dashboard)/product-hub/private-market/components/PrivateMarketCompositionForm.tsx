"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { InvestmentComposition } from "../data";

interface PrivateMarketCompositionFormProps {
  investmentComposition: InvestmentComposition;
  onSave: (investmentComposition: InvestmentComposition) => void;
}

export default function PrivateMarketCompositionForm({ investmentComposition, onSave }: PrivateMarketCompositionFormProps) {
  const [formData, setFormData] = useState<InvestmentComposition>(investmentComposition);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(investmentComposition);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Investment Composition</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-6">
        {/* Portfolio Strategy */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-3">Portfolio Strategy</h4>
          <div className="space-y-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Deep Lending (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.portfolioStrategy.deepLending}
                onChange={(e) => setFormData({
                  ...formData,
                  portfolioStrategy: {
                    ...formData.portfolioStrategy,
                    deepLending: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="30.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Opportunistic Credit (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.portfolioStrategy.opportunisticCredit}
                onChange={(e) => setFormData({
                  ...formData,
                  portfolioStrategy: {
                    ...formData.portfolioStrategy,
                    opportunisticCredit: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="55.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Structured Credit (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.portfolioStrategy.structuredCredit}
                onChange={(e) => setFormData({
                  ...formData,
                  portfolioStrategy: {
                    ...formData.portfolioStrategy,
                    structuredCredit: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="15.00"
              />
            </div>
          </div>
        </div>

        {/* Industries */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-3">Industries</h4>
          <div className="space-y-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Other (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.industries.other}
                onChange={(e) => setFormData({
                  ...formData,
                  industries: {
                    ...formData.industries,
                    other: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="48.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Software (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.industries.software}
                onChange={(e) => setFormData({
                  ...formData,
                  industries: {
                    ...formData.industries,
                    software: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="45.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Finance, Insurance & Real Estate (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.industries.financeInsuranceRealEstate}
                onChange={(e) => setFormData({
                  ...formData,
                  industries: {
                    ...formData.industries,
                    financeInsuranceRealEstate: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="7.00"
              />
            </div>
          </div>
        </div>

        {/* Debt Class */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-3">Debt Class</h4>
          <div className="space-y-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">First Lien Senior Secured (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.debtClass.firstLienSeniorSecured}
                onChange={(e) => setFormData({
                  ...formData,
                  debtClass: {
                    ...formData.debtClass,
                    firstLienSeniorSecured: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="58.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Structured Credit (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.debtClass.structuredCredit}
                onChange={(e) => setFormData({
                  ...formData,
                  debtClass: {
                    ...formData.debtClass,
                    structuredCredit: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="20.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Second Lien Senior Secured (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.debtClass.secondLienSeniorSecured}
                onChange={(e) => setFormData({
                  ...formData,
                  debtClass: {
                    ...formData.debtClass,
                    secondLienSeniorSecured: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="22.00"
              />
            </div>
          </div>
        </div>

        {/* Geography */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-3">Geography</h4>
          <div className="space-y-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">North America (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.geography.northAmerica}
                onChange={(e) => setFormData({
                  ...formData,
                  geography: {
                    ...formData.geography,
                    northAmerica: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="79.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">South America & Europe (%)</label>
              <Input
                type="number"
                step="0.01"
                value={formData.geography.southAmericaEurope}
                onChange={(e) => setFormData({
                  ...formData,
                  geography: {
                    ...formData.geography,
                    southAmericaEurope: parseFloat(e.target.value) || 0
                  }
                })}
                disabled={!isEditing}
                placeholder="21.00"
              />
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
} 