"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Textarea } from "@admin/components/ui/textarea";
import { AboutSection } from "../data";

interface PrivateMarketAboutFormProps {
  aboutSection: AboutSection;
  onSave: (aboutSection: AboutSection) => void;
}

export default function PrivateMarketAboutForm({ aboutSection, onSave }: PrivateMarketAboutFormProps) {
  const [formData, setFormData] = useState<AboutSection>(aboutSection);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(aboutSection);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">About Section</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-6">
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Fund Description *
          </label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            disabled={!isEditing}
            rows={4}
            placeholder="Enter fund description..."
          />
        </div>

        <div className="grid grid-cols-2 gap-6">
          <div>
            <label htmlFor="assetsUnderManagement" className="block text-sm font-medium text-gray-700 mb-1">
              Assets Under Management *
            </label>
            <Input
              id="assetsUnderManagement"
              value={formData.assetsUnderManagement}
              onChange={(e) => setFormData({ ...formData, assetsUnderManagement: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., $42.5B"
            />
          </div>

          <div>
            <label htmlFor="experienceYears" className="block text-sm font-medium text-gray-700 mb-1">
              Experience Years
            </label>
            <Input
              id="experienceYears"
              type="number"
              value={formData.experienceYears}
              onChange={(e) => setFormData({ ...formData, experienceYears: parseInt(e.target.value) || 0 })}
              disabled={!isEditing}
              placeholder="24"
            />
          </div>

          <div>
            <label htmlFor="employees" className="block text-sm font-medium text-gray-700 mb-1">
              Employees
            </label>
            <Input
              id="employees"
              value={formData.employees}
              onChange={(e) => setFormData({ ...formData, employees: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., 2,200+ Employee, 200+ credit professionals"
            />
          </div>

          <div>
            <label htmlFor="offices" className="block text-sm font-medium text-gray-700 mb-1">
              Offices
            </label>
            <Input
              id="offices"
              value={formData.offices}
              onChange={(e) => setFormData({ ...formData, offices: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., 28 offices"
            />
          </div>

          <div>
            <label htmlFor="continents" className="block text-sm font-medium text-gray-700 mb-1">
              Continents
            </label>
            <Input
              id="continents"
              type="number"
              value={formData.continents}
              onChange={(e) => setFormData({ ...formData, continents: parseInt(e.target.value) || 0 })}
              disabled={!isEditing}
              placeholder="4"
            />
          </div>
        </div>
      </div>
    </Card>
  );
} 