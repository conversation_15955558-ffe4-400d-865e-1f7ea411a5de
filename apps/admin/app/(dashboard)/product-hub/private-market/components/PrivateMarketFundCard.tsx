import React from "react";
import { Card } from "@admin/components/ui/card";

interface PrivateMarketFundCardProps {
  fundName: string;
  status: string;
  targetIRR: number;
  targetMOIC: number;
  minInvestment: string;
  vintage: string;
  tenor: string;
}

export default function PrivateMarketFundCard({
  fundName,
  status,
  targetIRR,
  targetMOIC,
  minInvestment,
  vintage,
  tenor,
}: PrivateMarketFundCardProps) {
  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
            <span className="text-sm font-semibold text-gray-600">iC</span>
          </div>
          <h2 className="text-lg font-semibold text-gray-900">{fundName}</h2>
        </div>
        <span className="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
          {status}
        </span>
      </div>
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{targetIRR}%</div>
          <div className="text-sm text-gray-600">Target IRR</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{targetMOIC}x</div>
          <div className="text-sm text-gray-600">Target MOIC</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">{minInvestment}</div>
          <div className="text-sm text-gray-600">Min. Inv.</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">{vintage}</div>
          <div className="text-sm text-gray-600">Vintage</div>
        </div>
      </div>
      <div className="text-center">
        <div className="text-lg font-semibold text-gray-900">{tenor}</div>
        <div className="text-sm text-gray-600">Tenor</div>
      </div>
    </Card>
  );
} 