"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Textarea } from "@admin/components/ui/textarea";
import { FAQ } from "../data";

interface PrivateMarketFAQsFormProps {
  faqs: FAQ[];
  onSave: (faqs: FAQ[]) => void;
}

export default function PrivateMarketFAQsForm({ faqs, onSave }: PrivateMarketFAQsFormProps) {
  const [formData, setFormData] = useState<FAQ[]>(faqs);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(faqs);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">FAQs</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {formData.map((faq, index) => (
          <div key={index} className="p-4 border border-gray-200 rounded-lg">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Question
                </label>
                <Input
                  value={faq.question}
                  onChange={(e) => {
                    const newFaqs = [...formData];
                    newFaqs[index].question = e.target.value;
                    setFormData(newFaqs);
                  }}
                  disabled={!isEditing}
                  placeholder="Enter question"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Answer
                </label>
                <Textarea
                  value={faq.answer}
                  onChange={(e) => {
                    const newFaqs = [...formData];
                    newFaqs[index].answer = e.target.value;
                    setFormData(newFaqs);
                  }}
                  disabled={!isEditing}
                  placeholder="Enter answer"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
} 