import React from "react";
import { Card } from "@admin/components/ui/card";

interface PrivateMarketEssentialsCardProps {
  fundSize: string;
  inception: string;
  leverage: number;
  annualizedDistributionRate: number;
  shareClass: string;
  distributions: string;
  subscriptionTiming: string;
  repurchase: string;
}

export default function PrivateMarketEssentialsCard({
  fundSize,
  inception,
  leverage,
  annualizedDistributionRate,
  shareClass,
  distributions,
  subscriptionTiming,
  repurchase,
}: PrivateMarketEssentialsCardProps) {
  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Essentials</h3>
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-gray-600">Fund size:</span>
          <span className="font-medium">{fundSize}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Inception:</span>
          <span className="font-medium">{inception}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Leverage:</span>
          <span className="font-medium">{leverage}%</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Annualized Distribution Rate:</span>
          <span className="font-medium">{annualizedDistributionRate}%</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Share class:</span>
          <span className="font-medium">{shareClass}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Distributions:</span>
          <span className="font-medium">{distributions}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Subscription timing:</span>
          <span className="font-medium">{subscriptionTiming}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Repurchase:</span>
          <span className="font-medium">{repurchase}</span>
        </div>
      </div>
    </Card>
  );
} 