"use client";
import React, { useState } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Essentials } from "../data";

interface PrivateMarketEssentialsFormProps {
  essentials: Essentials;
  onSave: (essentials: Essentials) => void;
}

export default function PrivateMarketEssentialsForm({ essentials, onSave }: PrivateMarketEssentialsFormProps) {
  const [formData, setFormData] = useState<Essentials>(essentials);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(essentials);
    setIsEditing(false);
  };

  return (
    <Card className="p-6 bg-white rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Essentials</h3>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <label htmlFor="fundSize" className="block text-sm font-medium text-gray-700 mb-1">
              Fund Size *
            </label>
            <Input
              id="fundSize"
              value={formData.fundSize}
              onChange={(e) => setFormData({ ...formData, fundSize: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., $4.1B+AUM"
            />
          </div>

          <div>
            <label htmlFor="inception" className="block text-sm font-medium text-gray-700 mb-1">
              Inception *
            </label>
            <Input
              id="inception"
              value={formData.inception}
              onChange={(e) => setFormData({ ...formData, inception: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., June 2018"
            />
          </div>

          <div>
            <label htmlFor="leverage" className="block text-sm font-medium text-gray-700 mb-1">
              Leverage (%)
            </label>
            <Input
              id="leverage"
              type="number"
              value={formData.leverage}
              onChange={(e) => setFormData({ ...formData, leverage: parseFloat(e.target.value) || 0 })}
              disabled={!isEditing}
              placeholder="21"
            />
          </div>

          <div>
            <label htmlFor="annualizedDistributionRate" className="block text-sm font-medium text-gray-700 mb-1">
              Annualized Distribution Rate (%) *
            </label>
            <Input
              id="annualizedDistributionRate"
              type="number"
              step="0.01"
              value={formData.annualizedDistributionRate}
              onChange={(e) => setFormData({ ...formData, annualizedDistributionRate: parseFloat(e.target.value) || 0 })}
              disabled={!isEditing}
              placeholder="8.37"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label htmlFor="shareClass" className="block text-sm font-medium text-gray-700 mb-1">
              Share Class
            </label>
            <Input
              id="shareClass"
              value={formData.shareClass}
              onChange={(e) => setFormData({ ...formData, shareClass: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., Class I"
            />
          </div>

          <div>
            <label htmlFor="distributions" className="block text-sm font-medium text-gray-700 mb-1">
              Distributions
            </label>
            <Input
              id="distributions"
              value={formData.distributions}
              onChange={(e) => setFormData({ ...formData, distributions: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., Quarterly"
            />
          </div>

          <div>
            <label htmlFor="subscriptionTiming" className="block text-sm font-medium text-gray-700 mb-1">
              Subscription Timing
            </label>
            <Input
              id="subscriptionTiming"
              value={formData.subscriptionTiming}
              onChange={(e) => setFormData({ ...formData, subscriptionTiming: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., Monthly"
            />
          </div>

          <div>
            <label htmlFor="repurchase" className="block text-sm font-medium text-gray-700 mb-1">
              Repurchase
            </label>
            <Input
              id="repurchase"
              value={formData.repurchase}
              onChange={(e) => setFormData({ ...formData, repurchase: e.target.value })}
              disabled={!isEditing}
              placeholder="e.g., Quarterly"
            />
          </div>
        </div>
      </div>
    </Card>
  );
} 