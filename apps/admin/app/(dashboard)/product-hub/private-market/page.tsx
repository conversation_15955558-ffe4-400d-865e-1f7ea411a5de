"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from "@admin/components/ui/button";
import { ArrowLeft, Download, MessageCircle, Save, Eye, Send, Play, ExternalLink, ChevronDown, ChevronUp } from "lucide-react";
import { samplePrivateMarket, PrivateMarketProduct } from "./data";
import PrivateMarketPreview from "./components/PrivateMarketPreview";
import PrivateMarketBasicInfoForm from "./components/PrivateMarketBasicInfoForm";
import PrivateMarketEssentialsForm from "./components/PrivateMarketEssentialsForm";
import PrivateMarketAboutForm from "./components/PrivateMarketAboutForm";
import PrivateMarketReasonsForm from "./components/PrivateMarketReasonsForm";
import PrivateMarketPerformanceForm from "./components/PrivateMarketPerformanceForm";
import PrivateMarketPricingForm from "./components/PrivateMarketPricingForm";
import PrivateMarketTeamForm from "./components/PrivateMarketTeamForm";
import PrivateMarketCompositionForm from "./components/PrivateMarketCompositionForm";
import PrivateMarketFAQsForm from "./components/PrivateMarketFAQsForm";
import PrivateMarketDataRoomForm from "./components/PrivateMarketDataRoomForm";
import axios from 'axios';
import { createPrivateMarket } from '@admin/app/lib/productApiService';

export default function PrivateMarketDetailPage() {
  const searchParams = useSearchParams();
  const [mode, setMode] = useState<'form' | 'review'>('form');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [privateMarket, setPrivateMarket] = useState<PrivateMarketProduct>(samplePrivateMarket);
  const [vendorId, setVendorId] = useState<string>("");
  const router = useRouter();

  useEffect(() => {
    const vendorParam = searchParams.get('vendor');
    if (vendorParam) {
      setVendorId(vendorParam);
      setPrivateMarket(prev => ({
        ...prev,
        vendor_id: vendorParam
      }));
    }
  }, [searchParams]);

  const handleBasicInfoSave = (basicInfo: PrivateMarketProduct['basicInfo']) => {
    setPrivateMarket(prev => ({ ...prev, basicInfo }));
  };

  const handleEssentialsSave = (essentials: PrivateMarketProduct['essentials']) => {
    setPrivateMarket(prev => ({ ...prev, essentials }));
  };

  const handleAboutSave = (aboutSection: PrivateMarketProduct['aboutSection']) => {
    setPrivateMarket(prev => ({ ...prev, aboutSection }));
  };

  const handleReasonsSave = (reasonsToConsider: PrivateMarketProduct['reasonsToConsider']) => {
    setPrivateMarket(prev => ({ ...prev, reasonsToConsider }));
  };

  const handlePerformanceSave = (pastPerformance: PrivateMarketProduct['pastPerformance']) => {
    setPrivateMarket(prev => ({ ...prev, pastPerformance }));
  };

  const handlePricingSave = (pricing: PrivateMarketProduct['pricing']) => {
    setPrivateMarket(prev => ({ ...prev, pricing }));
  };

  const handleTeamSave = (investmentTeam: PrivateMarketProduct['investmentTeam']) => {
    setPrivateMarket(prev => ({ ...prev, investmentTeam }));
  };

  const handleCompositionSave = (investmentComposition: PrivateMarketProduct['investmentComposition']) => {
    setPrivateMarket(prev => ({ ...prev, investmentComposition }));
  };

  const handleFAQsSave = (faqs: PrivateMarketProduct['faqs']) => {
    setPrivateMarket(prev => ({ ...prev, faqs }));
  };

  const handleDataRoomSave = (dataRoom: PrivateMarketProduct['dataRoom']) => {
    setPrivateMarket(prev => ({ ...prev, dataRoom }));
  };

  const validateForm = () => {
    const errors = [];
    
    // Check required fields from basicInfo
    if (!privateMarket.basicInfo?.fundName) errors.push("Fund Name is required");
    if (!privateMarket.basicInfo?.fundManager) errors.push("Fund Manager is required");
    if (!privateMarket.basicInfo?.targetIRR) errors.push("Target IRR is required");
    if (!privateMarket.basicInfo?.targetMOIC) errors.push("Target MOIC is required");
    if (!privateMarket.basicInfo?.minInvestment) errors.push("Minimum Investment is required");
    
    // Check required fields from essentials
    if (!privateMarket.essentials?.fundSize) errors.push("Fund Size is required");
    if (!privateMarket.essentials?.inception) errors.push("Inception Date is required");
    if (!privateMarket.essentials?.annualizedDistributionRate) errors.push("Annualized Distribution Rate is required");
    
    // Check required fields from about
    if (!privateMarket.aboutSection?.description) errors.push("Fund Description is required");
    if (!privateMarket.aboutSection?.assetsUnderManagement) errors.push("Assets Under Management is required");
    
    return errors;
  };

  const getFormProgress = () => {
    const errors = validateForm();
    const totalFields = 10; // Approximate number of required fields
    const filledFields = totalFields - errors.length;
    return Math.max(0, Math.min(100, (filledFields / totalFields) * 100));
  };

  const handleReview = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    setMode('review');
  };

  const handleSubmit = async () => {
    console.log('Starting submission...');
    setIsSubmitting(true);
    try {
      if (!vendorId) {
        throw new Error('Vendor ID is required');
      }

      console.log('Preparing data for submission...');
      const privateMarketDataToSave = {
        vendor_id: vendorId,
        basicInfo: privateMarket.basicInfo,
        essentials: privateMarket.essentials,
        aboutSection: privateMarket.aboutSection,
        reasonsToConsider: privateMarket.reasonsToConsider,
        keyConsideration: privateMarket.keyConsideration,
        idealInvestor: privateMarket.idealInvestor,
        pastPerformance: privateMarket.pastPerformance,
        cashFlowSimulator: privateMarket.cashFlowSimulator,
        investmentDueDates: privateMarket.investmentDueDates,
        pricing: privateMarket.pricing,
        sampleCompanies: privateMarket.sampleCompanies,
        investmentTeam: privateMarket.investmentTeam,
        investmentComposition: privateMarket.investmentComposition,
        faqs: privateMarket.faqs,
        dataRoom: privateMarket.dataRoom,
      };

      console.log('Sending data to API:', privateMarketDataToSave);
      const response: any = await createPrivateMarket(privateMarketDataToSave);
      console.log('API Response:', response);

      if (response && response.statusCode === 201) {
        alert('Private Market product created successfully!');
        setMode('form');
        router.push('/product-hub/');
      } else if (response && response.id) {
        alert('Private Market product created successfully!');
        setMode('form');
        router.push('/product-hub/');
      } else {
        throw new Error('Failed to create private market product');
      }
    } catch (error: any) {
      console.error('Error saving Private Market product:', error);
      alert(error.message || 'Failed to save Private Market product. Please try again.');
    } finally {
      console.log('Submission completed');
      setIsSubmitting(false);
    }
  };

  const handleSubmitWithValidation = async () => {
    console.log('Starting validation...');
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    if (!vendorId) {
      alert('Vendor ID is required. Please ensure you have accessed this page with a valid vendor parameter.');
      return;
    }
    console.log('Validation passed, proceeding with submission...');
    await handleSubmit();
  };

  if (mode === 'review') {
    return (
      <div className="w-full h-full overflow-y-auto bg-gray-50">
        <PrivateMarketPreview 
          privateMarket={privateMarket}
          onBack={() => setMode('form')}
          onSubmit={handleSubmitWithValidation}
          isSubmitting={isSubmitting}
        />
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/product-hub/')}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Product Hub
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Private Market Product</h1>
              <p className="text-gray-600 text-sm">
                Create a new private market investment product
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-500">
              Progress: {getFormProgress().toFixed(0)}%
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReview}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button
              size="sm"
              onClick={handleSubmitWithValidation}
              disabled={isSubmitting}
              className="admin_green_gradient hover:admin_green_gradient_hover text-white"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Saving...
                </div>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Product
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="p-6 space-y-6">
        {/* Basic Information */}
        <PrivateMarketBasicInfoForm
          basicInfo={privateMarket.basicInfo}
          onSave={handleBasicInfoSave}
        />

        {/* Essentials */}
        <PrivateMarketEssentialsForm
          essentials={privateMarket.essentials}
          onSave={handleEssentialsSave}
        />

        {/* About Section */}
        <PrivateMarketAboutForm
          aboutSection={privateMarket.aboutSection}
          onSave={handleAboutSave}
        />

        {/* Reasons to Consider */}
        <PrivateMarketReasonsForm
          reasonsToConsider={privateMarket.reasonsToConsider}
          onSave={handleReasonsSave}
        />

        {/* Past Performance */}
        <PrivateMarketPerformanceForm
          pastPerformance={privateMarket.pastPerformance}
          onSave={handlePerformanceSave}
        />

        {/* Pricing */}
        <PrivateMarketPricingForm
          pricing={privateMarket.pricing}
          onSave={handlePricingSave}
        />

        {/* Investment Team */}
        <PrivateMarketTeamForm
          investmentTeam={privateMarket.investmentTeam}
          onSave={handleTeamSave}
        />

        {/* Investment Composition */}
        <PrivateMarketCompositionForm
          investmentComposition={privateMarket.investmentComposition}
          onSave={handleCompositionSave}
        />

        {/* FAQs */}
        <PrivateMarketFAQsForm
          faqs={privateMarket.faqs}
          onSave={handleFAQsSave}
        />

        {/* Data Room */}
        <PrivateMarketDataRoomForm
          dataRoom={privateMarket.dataRoom}
          onSave={handleDataRoomSave}
        />
      </div>
    </div>
  );
} 