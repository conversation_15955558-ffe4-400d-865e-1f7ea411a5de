import React, { useState } from "react";
import { CustomSelect } from "@admin/components/ui/CustomSelect";
import { useRouter } from "next/navigation";
import { getProductTypePath } from "./utils/productTypeMapper";
import { useProductVendors } from "@admin/app/lib/hooks/api-hooks";

interface AddProductModalProps {
  onClose: () => void;
}

export const AddProductModal: React.FC<AddProductModalProps> = ({
  onClose,
}) => {
  const router = useRouter();
  const [productType, setProductType] = useState("");
  const [selectedVendor, setSelectedVendor] = useState("");
  const [isNavigating, setIsNavigating] = useState(false);

  // React Query hooks
  const { data: vendors = [], isLoading: loading } = useProductVendors();

  const productTypeOptions = [
    "Structure Products",
    "Mutual Funds",
    "ETFs", 
    "Private Market",
    "REITs",
    "Crypto",
    "Crypto ETF",
    "Bonds",
    "Equity"
  ];

  const handleSubmit = () => {
    if (!productType || !selectedVendor) {
      console.error('Please select both product type and vendor');
      return;
    }

    setIsNavigating(true);
    const path = getProductTypePath(productType);
    router.push(path + `?vendor=${selectedVendor}`);
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md mx-4 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#05A049] to-[#05A049]/90 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="text-white">
                  <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Add New Product</h2>
                <p className="text-white/80 text-sm">Select product type and vendor</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white/80 hover:text-white transition-colors duration-200 p-1 rounded-full hover:bg-white/10"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M18 6L6 18M6 6l12 12"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Product Type Selection */}
          <div className="space-y-3">
            <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
              <div className="w-2 h-2 bg-[#05A049] rounded-full"></div>
              Product Type
              <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <CustomSelect
                options={productTypeOptions}
                value={productType}
                onChange={setProductType}
                placeholder="Choose product type..."
                className="w-full"
              />
              {productType && (
                <div className="mt-2 text-xs text-gray-500">
                  Selected: <span className="font-medium text-[#05A049]">{productType}</span>
                </div>
              )}
            </div>
          </div>

          {/* Vendor Selection */}
          <div className="space-y-3">
            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <div className="w-2 h-2 bg-[#05A049] rounded-full"></div>
              Vendor
              <span className="text-red-500">*</span>
              {vendors.length > 0 && (
                <span className="text-xs text-gray-500 font-normal">
                  ({vendors.length} available)
                </span>
              )}
            </label>
            <div className="relative">
              <CustomSelect
                options={vendors.map(vendor => `${vendor.name} (${vendor.compliance_status || 'Pending'})`)}
                value={(() => {
                  const selectedVendorObj = vendors.find(v => v.vendor_id === selectedVendor);
                  return selectedVendorObj ? `${selectedVendorObj.name} (${selectedVendorObj.compliance_status || 'Pending'})` : "";
                })()}
                onChange={(vendorNameWithStatus) => {
                  const vendorName = vendorNameWithStatus.split(' (')[0];
                  const vendor = vendors.find(v => v.name === vendorName);
                  setSelectedVendor(vendor?.vendor_id || "");
                }}
                placeholder={loading ? "Loading vendors..." : vendors.length === 0 ? "No vendors available" : "Select a vendor..."}
                className="w-full"
                disabled={loading}
              />
              {selectedVendor && (
                <div className="mt-2 text-xs text-gray-500">
                  Selected vendor ID: <span className="font-mono text-[#05A049]">{selectedVendor}</span>
                </div>
              )}
              {vendors.length === 0 && !loading && (
                <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-amber-600">
                      <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p className="text-sm text-amber-800">
                      No vendors found. Please add vendors through the vendor management section.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Summary */}
          {productType && selectedVendor && (
            <div className="mt-6 p-4 bg-gray-50 rounded-xl border border-gray-200">
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-[#05A049]">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Product Summary
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium text-gray-900">{productType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Risk Level:</span>
                  <span className="font-medium text-gray-900">Medium Risk</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Currency:</span>
                  <span className="font-medium text-gray-900">USD</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Availability:</span>
                  <span className="font-medium text-gray-900">Available</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end gap-3">
          <button
            type="button"
            className="px-6 py-2.5 rounded-2xl border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            type="button"
            className="px-6 py-2.5 rounded-2xl bg-gradient-to-r from-[#05A049] to-[#05A049]/90 text-white hover:from-[#05A049]/90 hover:to-[#05A049]/80 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md"
            onClick={handleSubmit}
            disabled={!productType || !selectedVendor || isNavigating}
          >
            {isNavigating ? (
              <div className="flex items-center gap-2">
                <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"/>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                </svg>
                Navigating...
              </div>
            ) : (
              'Continue to Form'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
