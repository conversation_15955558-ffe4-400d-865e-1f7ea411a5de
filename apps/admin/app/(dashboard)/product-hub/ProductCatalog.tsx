"use client";
import React, { useState, useMemo } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { 
  SearchIcon, 
  FilterIcon, 
  GridIcon, 
  ListIcon,
  DownloadIcon,
  EyeIcon,
  EditIcon,
  TrashIcon
} from "lucide-react";
import { useProducts } from "@admin/app/lib/hooks/api-hooks";
import { Product } from "@admin/lib/productService";
import { ProductDetailsModal } from "./components/ProductDetailsModal";

type ViewMode = "grid" | "list";
type SortOption = "name" | "symbol" | "price" | "risk" | "sector";

export const ProductCatalog = (): JSX.Element => {
  const { data: products = [], isLoading } = useProducts();
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [sortBy, setSortBy] = useState<SortOption>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [selectedFilters, setSelectedFilters] = useState({
    sector: "All",
    risk: "All",
    availability: "All",
    productType: "All"
  });
  const [selectedProduct, setSelectedProduct] = useState<{
    tableProduct: any;
    dbProduct: any;
  } | null>(null);
  const [showProductDetailsModal, setShowProductDetailsModal] = useState(false);

  // Filter and sort products
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (product.stock?.symbol || product.bond?.symbol || product.fund?.symbol || product.structured_product?.symbol || "").toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesSector = selectedFilters.sector === "All" || 
                           product.stock?.sector === selectedFilters.sector;
      
      const matchesRisk = selectedFilters.risk === "All" || 
                         product.risk_level === selectedFilters.risk;
      
      const matchesAvailability = selectedFilters.availability === "All" || 
                                 product.availability === selectedFilters.availability;
      
      const matchesProductType = selectedFilters.productType === "All" || 
                                (selectedFilters.productType === "Equity" && (product.stock || product.fund || product.structured_product)) ||
                                (selectedFilters.productType === "Credit" && product.bond);

      return matchesSearch && matchesSector && matchesRisk && matchesAvailability && matchesProductType;
    });

    // Sort products
    filtered.sort((a, b) => {
      let aValue: string | number, bValue: string | number;
      
      switch (sortBy) {
        case "name":
          aValue = a.name;
          bValue = b.name;
          break;
        case "symbol":
          aValue = a.stock?.symbol || a.bond?.symbol || a.fund?.symbol || a.structured_product?.symbol || "";
          bValue = b.stock?.symbol || b.bond?.symbol || b.fund?.symbol || b.structured_product?.symbol || "";
          break;
        case "price":
          aValue = a.stock?.current_price || 0;
          bValue = b.stock?.current_price || 0;
          break;
        case "risk":
          aValue = a.risk_level;
          bValue = b.risk_level;
          break;
        case "sector":
          aValue = a.stock?.sector || "";
          bValue = b.stock?.sector || "";
          break;
        default:
          aValue = a.name;
          bValue = b.name;
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [products, searchTerm, sortBy, sortOrder, selectedFilters]);

  const sectors = ["Technology", "Healthcare", "Finance", "Automotive", "Energy", "Consumer Goods"];
  const riskLevels = ["Low Risk", "Medium Risk", "High Risk"];
  const availabilityOptions = ["Available", "Limited Availability", "Unavailable"];
  const productTypes = ["Equity", "Credit"];

  const getProductSymbol = (product: Product) => {
    return product.stock?.symbol || product.bond?.symbol || product.fund?.symbol || product.structured_product?.symbol || "N/A";
  };

  const getProductPrice = (product: Product) => {
    return product.stock?.current_price ? `$${product.stock.current_price}` : "N/A";
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low Risk": return "bg-green-100 text-green-800";
      case "Medium Risk": return "bg-yellow-100 text-yellow-800";
      case "High Risk": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case "Available": return "bg-green-100 text-green-800";
      case "Limited Availability": return "bg-yellow-100 text-yellow-800";
      case "Unavailable": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const handleViewProduct = (product: Product) => {
    // Create a table product object similar to ProductManagement
    const tableProduct = {
      symbol: getProductSymbol(product),
      name: product.name,
      price: getProductPrice(product),
      change: "+0.00%", // This would need to be calculated from historical data
      isPositive: true,
      volume: "Vol: N/A", // This would need to be fetched from market data
      marketCap: product.stock?.market_cap ? `Cap: $${product.stock.market_cap}` : "Cap: N/A",
      sector: product.stock?.sector || "N/A",
      risk: product.risk_level,
      riskColor: getRiskColor(product.risk_level),
      availability: product.availability,
      brokers: ["IB", "Schwab", "Fidelity"], // This would need to be fetched from broker data
    };
    
    setSelectedProduct({ tableProduct, dbProduct: product });
    setShowProductDetailsModal(true);
  };

  return (
    <div className="w-full h-full overflow-y-auto">
      {/* Header */}
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Product Catalog</h1>
            <p className="text-gray-600 text-sm">
              Browse and manage all available products in your portfolio
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="text-sm">
              <DownloadIcon className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button className="admin_green_gradient hover:admin_green_gradient_hover text-white">
              Add Product
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Search and Filters */}
        <Card className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search products by name or symbol..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <GridIcon className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <ListIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-2 lg:grid-cols-6 gap-4">
            <select
              value={selectedFilters.sector}
              onChange={(e) => setSelectedFilters(prev => ({ ...prev, sector: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="All">All Sectors</option>
              {sectors.map(sector => (
                <option key={sector} value={sector}>{sector}</option>
              ))}
            </select>

            <select
              value={selectedFilters.risk}
              onChange={(e) => setSelectedFilters(prev => ({ ...prev, risk: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="All">All Risk Levels</option>
              {riskLevels.map(risk => (
                <option key={risk} value={risk}>{risk}</option>
              ))}
            </select>

            <select
              value={selectedFilters.availability}
              onChange={(e) => setSelectedFilters(prev => ({ ...prev, availability: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="All">All Availability</option>
              {availabilityOptions.map(availability => (
                <option key={availability} value={availability}>{availability}</option>
              ))}
            </select>

            <select
              value={selectedFilters.productType}
              onChange={(e) => setSelectedFilters(prev => ({ ...prev, productType: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="All">All Types</option>
              {productTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortOption)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="name">Sort by Name</option>
              <option value="symbol">Sort by Symbol</option>
              <option value="price">Sort by Price</option>
              <option value="risk">Sort by Risk</option>
              <option value="sector">Sort by Sector</option>
            </select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(prev => prev === "asc" ? "desc" : "asc")}
              className="text-sm"
            >
              {sortOrder === "asc" ? "↑" : "↓"} {sortBy}
            </Button>
          </div>
        </Card>

        {/* Results Count */}
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-600">
            {isLoading ? "Loading..." : `${filteredAndSortedProducts.length} products found`}
          </p>
        </div>

        {/* Products Display */}
        {viewMode === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAndSortedProducts.map((product, index) => (
              <Card key={index} className="p-4 hover:shadow-lg transition-shadow">
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-900">{getProductSymbol(product)}</h3>
                      <p className="text-sm text-gray-600 truncate">{product.name}</p>
                    </div>
                    <div className="flex gap-1">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleViewProduct(product)}
                      >
                        <EyeIcon className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <EditIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Price:</span>
                      <span className="font-medium">{getProductPrice(product)}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Sector:</span>
                      <span className="text-sm">{product.stock?.sector || "N/A"}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Risk:</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getRiskColor(product.risk_level)}`}>
                        {product.risk_level}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Status:</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getAvailabilityColor(product.availability)}`}>
                        {product.availability}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sector</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Risk</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Availability</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredAndSortedProducts.map((product, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="font-medium text-gray-900">{getProductSymbol(product)}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900">{product.name}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="font-medium">{getProductPrice(product)}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900">{product.stock?.sector || "N/A"}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-xs px-2 py-1 rounded-full ${getRiskColor(product.risk_level)}`}>
                          {product.risk_level}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-xs px-2 py-1 rounded-full ${getAvailabilityColor(product.availability)}`}>
                          {product.availability}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleViewProduct(product)}
                          >
                            <EyeIcon className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <EditIcon className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        )}

        {/* Product Details Modal */}
        {showProductDetailsModal && selectedProduct && (
          <ProductDetailsModal
            product={selectedProduct.tableProduct}
            dbProduct={selectedProduct.dbProduct}
            onClose={() => {
              setShowProductDetailsModal(false);
              setSelectedProduct(null);
            }}
          />
        )}
      </div>
    </div>
  );
}; 