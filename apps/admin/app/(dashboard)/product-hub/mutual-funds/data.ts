export interface Manager {
  name: string;
  timeline: string;
}

export interface Fees {
  expenseRatio: number;
  exitLoad: number;
  stamp_duty: number;
}

export interface ChartPoint {
  date: Date;
  value: number;
}

export interface Holding {
  name: string;
  field: string;
  assets: string;
  instrument: string;
}

export interface ReturnSet {
  "1y": number;
  "3y": number;
  "5y": number;
  "All": number;
}

export interface ReturnComparison {
  funds_returns: ReturnSet;
  category_average: ReturnSet;
  rank_within_category: ReturnSet;
}

export interface PeerFund {
  name: string;
  rating: number;
  returns1Y: number;
  returns3Y: number;
  fundSize: string;
}

export interface Faq {
  question: string;
  answer: string;
}

export interface MutualFund {
  productId: string;
  basicInfo: {
    fundName: string;
    logo_url: string;
    flags: string[];
    AUM: string;
    currency: string;
    current_nav: string;
    expense_ratio: string;
    minimum_investment: string;
  };
  kdUrl?: string;
  contactNumber?: string;
  essentials: {
    netAssetValue: number;
    date_of_nav: string;
    rating: string;
    min_sp_amount: string;
    fund_size: string;
  };
  about: {
    description: string;
    experience: string;
    employee_size: string;
    offices: string;
  };
  managementTeam: Manager[];
  fees: Fees;
  investmentLimits: {
    firstInvestment: number;
    secondInvestment: number;
    thirdInvestment: number;
  };
  ratings: {
    Top5: number;
    Top25: number;
    ExitRate: number;
    PERatio: number;
    Alpha: number;
    Beta: number;
    Sharpe: number;
    Sortino: number;
  };
  performanceChart: {
    chartData: ChartPoint[];
  };
  holdings: Holding[];
  returnsAndRankings: {
    category: string;
    annualised_returns: ReturnComparison;
    absolute_returns: ReturnComparison;
  };
  peerComparison: {
    funds: PeerFund[];
  };
  faqs: {
    questions: Faq[];
  };
}

// Sample data with empty/default values
export const sampleMutualFund: MutualFund = {
  productId: "",
  basicInfo: {
    fundName: "",
    logo_url: "",
    flags: [],
    AUM: "",
    currency: "",
    current_nav: "",
    expense_ratio: "",
    minimum_investment: "",
  },
  essentials: {
    netAssetValue: 0,
    date_of_nav: "",
    rating: "",
    min_sp_amount: "",
    fund_size: "",
  },
  about: {
    description: "",
    experience: "",
    employee_size: "",
    offices: "",
  },
  managementTeam: [],
  fees: {
    expenseRatio: 0,
    exitLoad: 0,
    stamp_duty: 0,
  },
  investmentLimits: {
    firstInvestment: 0,
    secondInvestment: 0,
    thirdInvestment: 0,
  },
  ratings: {
    Top5: 0,
    Top25: 0,
    ExitRate: 0,
    PERatio: 0,
    Alpha: 0,
    Beta: 0,
    Sharpe: 0,
    Sortino: 0,
  },
  performanceChart: {
    chartData: [],
  },
  holdings: [],
  returnsAndRankings: {
    category: "",
    annualised_returns: {
      funds_returns: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
      category_average: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
      rank_within_category: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
    },
    absolute_returns: {
      funds_returns: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
      category_average: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
      rank_within_category: { "1y": 0, "3y": 0, "5y": 0, "All": 0 },
    },
  },
  peerComparison: {
    funds: [],
  },
  faqs: {
    questions: [],
  },
}; 