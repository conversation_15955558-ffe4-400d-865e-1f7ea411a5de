import React, { useState } from "react";
import { Info, DollarSign, Calendar, Users, Building2 } from "lucide-react";
import { MutualFund } from "../data";

type FundAboutProps = {
  about: MutualFund['about'];
  onSave?: (about: MutualFund['about']) => void;
};

const FundAbout: React.FC<FundAboutProps> = ({ 
  about: initialAbout,
  onSave 
}) => {
  const [about, setAbout] = useState(initialAbout);

  const handleChange = (field: keyof MutualFund['about'], value: string) => {
    setAbout(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(about);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow p-6">
      <div className="flex items-center gap-2 mb-2">
        <Info className="text-green-700 w-5 h-5" />
        <span className="font-semibold text-lg text-green-700">About</span>
      </div>
      <textarea
        className="border rounded px-2 py-1 text-sm text-gray-700 mb-4 w-full resize-none"
        placeholder="Enter fund description..."
        value={about.description}
        onChange={e => handleChange('description', e.target.value)}
        rows={3}
      />
      <div className="flex flex-wrap gap-6 md:gap-10 text-xs mt-2">
        <div className="flex items-center gap-2 min-w-[160px]">
          <Calendar className="w-7 h-7 text-green-700 bg-green-50 rounded-full p-1" />
          <div>
            <input
              className="font-bold text-green-700 text-base border-b border-green-200 bg-transparent outline-none w-20"
              placeholder="Enter years"
              value={about.experience}
              onChange={e => handleChange('experience', e.target.value)}
            />
            <div className="text-gray-500">of experience in Private Credit</div>
          </div>
        </div>
        <div className="flex items-center gap-2 min-w-[160px]">
          <Users className="w-7 h-7 text-green-700 bg-green-50 rounded-full p-1" />
          <div>
            <input
              className="font-bold text-green-700 text-base border-b border-green-200 bg-transparent outline-none w-20"
              placeholder="Enter size"
              value={about.employee_size}
              onChange={e => handleChange('employee_size', e.target.value)}
            />
            <div className="text-gray-500">Employee size</div>
          </div>
        </div>
        <div className="flex items-center gap-2 min-w-[160px]">
          <Building2 className="w-7 h-7 text-green-700 bg-green-50 rounded-full p-1" />
          <div>
            <input
              className="font-bold text-green-700 text-base border-b border-green-200 bg-transparent outline-none w-20"
              placeholder="Enter count"
              value={about.offices}
              onChange={e => handleChange('offices', e.target.value)}
            />
            <div className="text-gray-500">offices across 4 continents</div>
          </div>
        </div>
      </div>
      <button
        className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-4"
        onClick={handleSave}
      >
        Save Info
      </button>
    </div>
  );
};

export default FundAbout; 