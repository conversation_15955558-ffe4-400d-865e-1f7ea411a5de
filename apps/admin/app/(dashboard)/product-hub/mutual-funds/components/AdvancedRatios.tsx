import React, { useState } from "react";
import { MutualFund } from "../data";

type AdvancedRatiosProps = {
  ratings: MutualFund['ratings'];
  onSave?: (ratings: MutualFund['ratings']) => void;
};

const AdvancedRatios: React.FC<AdvancedRatiosProps> = ({ ratings: initialRatings, onSave }) => {
  const [ratings, setRatings] = useState(initialRatings);

  const handleChange = (field: keyof MutualFund['ratings'], value: number) => {
    setRatings(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    if (onSave) onSave(ratings);
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Advanced Ratios</div>
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="flex justify-between">
          <span>Top 5</span>
          <input
            className="border rounded px-1 py-1 w-16 text-right"
            value={ratings.Top5}
            onChange={e => handleChange('Top5', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <span>Top 25</span>
          <input
            className="border rounded px-1 py-1 w-16 text-right"
            value={ratings.Top25}
            onChange={e => handleChange('Top25', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <span>Exit Rate</span>
          <input
            className="border rounded px-1 py-1 w-16 text-right"
            value={ratings.ExitRate}
            onChange={e => handleChange('ExitRate', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <span>P/E Ratio</span>
          <input
            className="border rounded px-1 py-1 w-16 text-right"
            value={ratings.PERatio}
            onChange={e => handleChange('PERatio', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <span>Alpha</span>
          <input
            className="border rounded px-1 py-1 w-16 text-right"
            value={ratings.Alpha}
            onChange={e => handleChange('Alpha', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <span>Beta</span>
          <input
            className="border rounded px-1 py-1 w-16 text-right"
            value={ratings.Beta}
            onChange={e => handleChange('Beta', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <span>Sharpe</span>
          <input
            className="border rounded px-1 py-1 w-16 text-right"
            value={ratings.Sharpe}
            onChange={e => handleChange('Sharpe', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <span>Sortino</span>
          <input
            className="border rounded px-1 py-1 w-16 text-right"
            value={ratings.Sortino}
            onChange={e => handleChange('Sortino', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
      </div>
      <button
        className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2"
        onClick={handleSave}
      >
        Save Info
      </button>
    </div>
  );
};

export default AdvancedRatios; 