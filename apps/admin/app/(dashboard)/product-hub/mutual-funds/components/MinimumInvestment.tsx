import React, { useState } from "react";
import { MutualFund } from "../data";

type MinimumInvestmentProps = {
  investmentLimits: MutualFund['investmentLimits'];
  onSave?: (investmentLimits: MutualFund['investmentLimits']) => void;
};

const MinimumInvestment: React.FC<MinimumInvestmentProps> = ({ 
  investmentLimits: initialInvestmentLimits,
  onSave 
}) => {
  const [investmentLimits, setInvestmentLimits] = useState(initialInvestmentLimits);

  const handleChange = (field: keyof MutualFund['investmentLimits'], value: number) => {
    setInvestmentLimits(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(investmentLimits);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Minimum Investment</div>
      <div className="flex flex-col gap-2">
        <div className="flex justify-between text-xs items-center">
          <span>First Investment</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Amount"
            value={investmentLimits.firstInvestment}
            onChange={e => handleChange('firstInvestment', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Second Investment</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Amount"
            value={investmentLimits.secondInvestment}
            onChange={e => handleChange('secondInvestment', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Third Investment</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Amount"
            value={investmentLimits.thirdInvestment}
            onChange={e => handleChange('thirdInvestment', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
      </div>
      <button
        className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2"
        onClick={handleSave}
      >
        Save Info
      </button>
    </div>
  );
};

export default MinimumInvestment; 