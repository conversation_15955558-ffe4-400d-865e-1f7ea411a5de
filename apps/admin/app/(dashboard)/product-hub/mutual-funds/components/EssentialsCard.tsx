import React, { useState } from "react";
import { MutualFund } from "../data";

type EssentialsCardProps = {
  essentials: MutualFund['essentials'];
  onSave?: (essentials: MutualFund['essentials']) => void;
};

const EssentialsCard: React.FC<EssentialsCardProps> = ({ 
  essentials: initialEssentials,
  onSave 
}) => {
  const [essentials, setEssentials] = useState(initialEssentials);

  const handleChange = (field: keyof MutualFund['essentials'], value: string | number) => {
    setEssentials(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(essentials);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Essentials</div>
      <div className="flex flex-col gap-2">
        <div className="flex justify-between text-xs items-center">
          <span>Net Asset Value</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter NAV"
            value={essentials.netAssetValue}
            onChange={e => handleChange('netAssetValue', parseFloat(e.target.value) || 0)}
            type="number"
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Date of NAV</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Select Date"
            value={essentials.date_of_nav}
            onChange={e => handleChange('date_of_nav', e.target.value)}
            type="date"
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Rating</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Rating"
            value={essentials.rating}
            onChange={e => handleChange('rating', e.target.value)}
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Min SIP Amount</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Min SIP"
            value={essentials.min_sp_amount}
            onChange={e => handleChange('min_sp_amount', e.target.value)}
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Fund Size</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Fund Size"
            value={essentials.fund_size}
            onChange={e => handleChange('fund_size', e.target.value)}
          />
        </div>
      </div>
      <button
        className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2"
        onClick={handleSave}
      >
        Save Info
      </button>
    </div>
  );
};

export default EssentialsCard; 