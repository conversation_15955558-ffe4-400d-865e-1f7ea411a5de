import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, Manager } from "../data";

type FundManagementProps = {
  managementTeam: Manager[];
  onSave?: (managementTeam: Manager[]) => void;
};

const FundManagement: React.FC<FundManagementProps> = ({ managementTeam: initialManagementTeam, onSave }) => {
  const [managementTeam, setManagementTeam] = useState<Manager[]>(initialManagementTeam);

  const handleChange = (index: number, field: keyof Manager, value: string) => {
    setManagementTeam(prev => prev.map((m, i) => i === index ? { ...m, [field]: value } : m));
  };

  const handleAdd = () => {
    setManagementTeam(prev => [...prev, { name: "", timeline: "" }]);
  };

  const handleRemove = (index: number) => {
    setManagementTeam(prev => prev.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    if (onSave) onSave(managementTeam);
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Fund Management</div>
      <div className="flex flex-col gap-2">
        {managementTeam.map((m, i) => (
          <div key={i} className="flex gap-2 items-center w-full flex-wrap">
            <input
              className="border rounded px-2 py-1 text-xs flex-1 min-w-0 max-w-full"
              style={{ minWidth: 0 }}
              placeholder="Enter Manager Name"
              value={m.name}
              onChange={e => handleChange(i, "name", e.target.value)}
            />
            <input
              className="border rounded px-2 py-1 text-xs flex-1 min-w-0 max-w-full"
              style={{ minWidth: 0 }}
              placeholder="Enter Timeline"
              value={m.timeline}
              onChange={e => handleChange(i, "timeline", e.target.value)}
            />
            <button
              className="text-xs text-red-500 px-2 py-1 rounded hover:bg-red-100"
              onClick={() => handleRemove(i)}
              type="button"
            >
              Remove
            </button>
          </div>
        ))}
      </div>
      <div className="flex gap-2 mt-2">
        <button
          className="text-xs text-[#05A049] bg-[#e8f5e0] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit"
          onClick={handleAdd}
          type="button"
        >
          Add Manager
        </button>
        <button
          className="text-xs text-[#05A049] bg-[#e8f5e0] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit"
          onClick={handleSave}
          type="button"
        >
          Save Info
        </button>
      </div>
    </div>
  );
};

export default FundManagement; 