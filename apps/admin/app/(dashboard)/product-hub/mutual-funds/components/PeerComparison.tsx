import React, { useState } from "react";
import { MutualFund, PeerFund } from "../data";

type PeerComparisonProps = {
  peerComparison: MutualFund['peerComparison'];
  onSave?: (peerComparison: MutualFund['peerComparison']) => void;
};

const PeerComparison: React.FC<PeerComparisonProps> = ({ 
  peerComparison: initialPeerComparison,
  onSave 
}) => {
  const [peerComparison, setPeerComparison] = useState(initialPeerComparison);

  const handleChange = (index: number, field: keyof PeerFund, value: string | number) => {
    setPeerComparison(prev => ({
      ...prev,
      funds: prev.funds.map((f, i) => i === index ? { ...f, [field]: value } : f)
    }));
  };

  const handleAdd = () => {
    setPeerComparison(prev => ({
      ...prev,
      funds: [...prev.funds, { name: "", rating: 0, returns1Y: 0, returns3Y: 0, fundSize: "" }]
    }));
  };

  const handleRemove = (index: number) => {
    setPeerComparison(prev => ({
      ...prev,
      funds: prev.funds.filter((_, i) => i !== index)
    }));
  };

  const handleSave = () => {
    if (onSave) onSave(peerComparison);
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Peer Comparison</div>
      <div className="flex flex-col gap-2">
        {peerComparison.funds.map((fund, i) => (
          <div key={i} className="flex gap-2 items-center w-full flex-wrap">
            <input
              className="border rounded px-2 py-1 text-xs flex-1 min-w-0 max-w-full"
              style={{ minWidth: 0 }}
              placeholder="Enter Fund Name"
              value={fund.name}
              onChange={e => handleChange(i, "name", e.target.value)}
            />
            <input
              className="border rounded px-2 py-1 text-xs w-16 text-right"
              placeholder="Rating"
              value={fund.rating}
              onChange={e => handleChange(i, "rating", parseFloat(e.target.value) || 0)}
              type="number"
              min="0"
              max="5"
            />
            <input
              className="border rounded px-2 py-1 text-xs w-16 text-right"
              placeholder="1Y Return"
              value={fund.returns1Y}
              onChange={e => handleChange(i, "returns1Y", parseFloat(e.target.value) || 0)}
              type="number"
              step="0.1"
            />
            <input
              className="border rounded px-2 py-1 text-xs w-16 text-right"
              placeholder="3Y Return"
              value={fund.returns3Y}
              onChange={e => handleChange(i, "returns3Y", parseFloat(e.target.value) || 0)}
              type="number"
              step="0.1"
            />
            <input
              className="border rounded px-2 py-1 text-xs w-24 text-right"
              placeholder="Fund Size"
              value={fund.fundSize}
              onChange={e => handleChange(i, "fundSize", e.target.value)}
            />
            <button
              className="text-xs text-red-500 px-2 py-1 rounded hover:bg-red-100"
              onClick={() => handleRemove(i)}
              type="button"
            >
              Remove
            </button>
          </div>
        ))}
      </div>
      <div className="flex gap-2 mt-2">
        <button
          className="text-xs text-[#05A049] bg-[#e8f5e0] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit"
          onClick={handleAdd}
          type="button"
        >
          Add Peer Fund
        </button>
        <button
          className="text-xs text-[#05A049] bg-[#e8f5e0] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit"
          onClick={handleSave}
          type="button"
        >
          Save Info
        </button>
      </div>
    </div>
  );
};

export default PeerComparison; 