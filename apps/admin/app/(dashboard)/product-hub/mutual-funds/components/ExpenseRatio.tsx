import React, { useState } from "react";
import { MutualFund } from "../data";

type ExpenseRatioProps = {
  fees: MutualFund['fees'];
  onSave?: (fees: MutualFund['fees']) => void;
};

const ExpenseRatio: React.FC<ExpenseRatioProps> = ({ 
  fees: initialFees,
  onSave 
}) => {
  const [fees, setFees] = useState(initialFees);

  const handleChange = (field: keyof MutualFund['fees'], value: number) => {
    setFees(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(fees);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Expense Ratio</div>
      <div className="flex flex-col gap-2">
        <div className="flex justify-between text-xs items-center">
          <span>Expense Ratio</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Ratio"
            value={fees.expenseRatio}
            onChange={e => handleChange('expenseRatio', parseFloat(e.target.value) || 0)}
            type="number"
            step="0.01"
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Exit Load</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Load"
            value={fees.exitLoad}
            onChange={e => handleChange('exitLoad', parseFloat(e.target.value) || 0)}
            type="number"
            step="0.1"
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Stamp Duty</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Duty"
            value={fees.stamp_duty}
            onChange={e => handleChange('stamp_duty', parseFloat(e.target.value) || 0)}
            type="number"
            step="0.001"
          />
        </div>
      </div>
      <button
        className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2"
        onClick={handleSave}
      >
        Save Info
      </button>
    </div>
  );
};

export default ExpenseRatio; 