import React from "react";

type Recommendation = { name: string; value: string; currentValue: string };
type SimilarRecommendationsProps = {
  recommendations: Recommendation[];
};

const SimilarRecommendations: React.FC<SimilarRecommendationsProps> = ({ recommendations }) => (
  <div className="bg-white rounded-xl shadow p-4">
    <div className="font-semibold text-lg mb-2">Similar Recommendations</div>
    <div className="flex gap-4">
      {recommendations.map((rec, i) => (
        <div key={i} className="bg-[#f6fafd] rounded-lg p-4 flex flex-col items-center w-40">
          <div className="text-xs font-semibold mb-1">{rec.name}</div>
          <div className="text-green-700 font-bold">{rec.value}</div>
          <div className="text-xs text-gray-500">Current Value</div>
          <div className="text-xs">{rec.currentValue}</div>
        </div>
      ))}
    </div>
  </div>
);

export default SimilarRecommendations; 