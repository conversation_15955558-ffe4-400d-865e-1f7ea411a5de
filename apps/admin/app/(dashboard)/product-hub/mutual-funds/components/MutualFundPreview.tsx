import React from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { ArrowLeft, Edit, Send } from "lucide-react";
import { MutualFund } from "../data";

type MutualFundPreviewProps = {
  fund: MutualFund;
  onEdit: () => void;
  onSave: () => void;
  isSaving: boolean;
  kdUrl?: string;
  contactNumber?: string;
};

const MutualFundPreview: React.FC<MutualFundPreviewProps> = ({ fund, onEdit, onSave, isSaving, kdUrl, contactNumber }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e8f5ee] to-[#f6fafd] p-4 flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <Button 
          variant="ghost" 
          size="sm" 
          className="flex items-center gap-2"
          onClick={onEdit}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Form
        </Button>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={onEdit}
          >
            <Edit className="h-4 w-4" /> Edit
          </Button>
          <Button 
            className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            onClick={onSave}
            disabled={isSaving}
          >
            <Send className="h-4 w-4" /> 
            {isSaving ? 'Submitting...' : 'Submit Mutual Fund'}
          </Button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Sidebar */}
        <div className="lg:col-span-1 flex flex-col gap-6">
          {/* Basic Info Summary */}
          <div className="bg-white rounded-xl shadow p-4">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Fund Summary</h3>
            <div className="space-y-3">
              <div>
                <label className="text-xs text-gray-500">Fund Name</label>
                <p className="text-sm font-medium">{fund.basicInfo.fundName || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">AUM</label>
                <p className="text-sm font-medium">{fund.basicInfo.AUM || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Currency</label>
                <p className="text-sm font-medium">{fund.basicInfo.currency || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Current NAV</label>
                <p className="text-sm font-medium">{fund.basicInfo.current_nav || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Expense Ratio</label>
                <p className="text-sm font-medium">{fund.basicInfo.expense_ratio || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Minimum Investment</label>
                <p className="text-sm font-medium">{fund.basicInfo.minimum_investment || 'Not specified'}</p>
              </div>
              {kdUrl && (
                <div>
                  <label className="text-xs text-gray-500">KD URL</label>
                  <p className="text-sm font-medium text-blue-600 break-all">
                    <a href={kdUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                      {kdUrl}
                    </a>
                  </p>
                </div>
              )}
              {contactNumber && (
                <div>
                  <label className="text-xs text-gray-500">Contact Number</label>
                  <p className="text-sm font-medium text-green-600">
                    <a href={`tel:${contactNumber}`} className="hover:underline">
                      {contactNumber}
                    </a>
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Essentials */}
          <div className="bg-white rounded-xl shadow p-4">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Essentials</h3>
            <div className="space-y-3">
              <div>
                <label className="text-xs text-gray-500">Net Asset Value</label>
                <p className="text-sm font-medium">{fund.essentials.netAssetValue || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Date of NAV</label>
                <p className="text-sm font-medium">{fund.essentials.date_of_nav || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Rating</label>
                <p className="text-sm font-medium">{fund.essentials.rating || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Fund Size</label>
                <p className="text-sm font-medium">{fund.essentials.fund_size || 'Not specified'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 flex flex-col gap-6">
          {/* About Section */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-semibold text-lg mb-4 text-green-700">About</h3>
            <div className="space-y-4">
              <div>
                <label className="text-xs text-gray-500">Description</label>
                <p className="text-sm mt-1">{fund.about.description || 'No description provided'}</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-xs text-gray-500">Experience</label>
                  <p className="text-sm font-medium">{fund.about.experience || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-xs text-gray-500">Employee Size</label>
                  <p className="text-sm font-medium">{fund.about.employee_size || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-xs text-gray-500">Offices</label>
                  <p className="text-sm font-medium">{fund.about.offices || 'Not specified'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Fund Holdings */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Fund Holdings</h3>
            {fund.holdings.length > 0 ? (
              <div className="space-y-3">
                {fund.holdings.map((holding, index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-2 text-sm">
                      <div>
                        <label className="text-xs text-gray-500">Name</label>
                        <p className="font-medium">{holding.name || 'Not specified'}</p>
                      </div>
                      <div>
                        <label className="text-xs text-gray-500">Field</label>
                        <p className="font-medium">{holding.field || 'Not specified'}</p>
                      </div>
                      <div>
                        <label className="text-xs text-gray-500">Assets</label>
                        <p className="font-medium">{holding.assets || 'Not specified'}</p>
                      </div>
                      <div>
                        <label className="text-xs text-gray-500">Instrument</label>
                        <p className="font-medium">{holding.instrument || 'Not specified'}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No holdings specified</p>
            )}
          </div>

          {/* Investment Limits */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Investment Limits</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-xs text-gray-500">First Investment</label>
                <p className="text-sm font-medium">{fund.investmentLimits.firstInvestment || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Second Investment</label>
                <p className="text-sm font-medium">{fund.investmentLimits.secondInvestment || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Third Investment</label>
                <p className="text-sm font-medium">{fund.investmentLimits.thirdInvestment || 'Not specified'}</p>
              </div>
            </div>
          </div>

          {/* Fees */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Fees</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-xs text-gray-500">Expense Ratio</label>
                <p className="text-sm font-medium">{fund.fees.expenseRatio || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Exit Load</label>
                <p className="text-sm font-medium">{fund.fees.exitLoad || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Stamp Duty</label>
                <p className="text-sm font-medium">{fund.fees.stamp_duty || 'Not specified'}</p>
              </div>
            </div>
          </div>

          {/* Management Team */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Management Team</h3>
            {fund.managementTeam.length > 0 ? (
              <div className="space-y-3">
                {fund.managementTeam.map((manager, index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                      <div>
                        <label className="text-xs text-gray-500">Name</label>
                        <p className="font-medium">{manager.name || 'Not specified'}</p>
                      </div>
                      <div>
                        <label className="text-xs text-gray-500">Timeline</label>
                        <p className="font-medium">{manager.timeline || 'Not specified'}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No management team specified</p>
            )}
          </div>

          {/* Ratings */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Advanced Ratios</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <label className="text-xs text-gray-500">Top 5</label>
                <p className="text-sm font-medium">{fund.ratings.Top5 || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Top 25</label>
                <p className="text-sm font-medium">{fund.ratings.Top25 || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Exit Rate</label>
                <p className="text-sm font-medium">{fund.ratings.ExitRate || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">P/E Ratio</label>
                <p className="text-sm font-medium">{fund.ratings.PERatio || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Alpha</label>
                <p className="text-sm font-medium">{fund.ratings.Alpha || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Beta</label>
                <p className="text-sm font-medium">{fund.ratings.Beta || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Sharpe</label>
                <p className="text-sm font-medium">{fund.ratings.Sharpe || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Sortino</label>
                <p className="text-sm font-medium">{fund.ratings.Sortino || 'Not specified'}</p>
              </div>
            </div>
          </div>

          {/* Returns and Rankings */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Returns & Rankings</h3>
            <div className="space-y-4">
              <div>
                <label className="text-xs text-gray-500">Category</label>
                <p className="text-sm font-medium">{fund.returnsAndRankings.category || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Annualised Returns</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-1">
                  <div>
                    <span className="text-xs text-gray-500">1Y: </span>
                    <span className="text-sm font-medium">{fund.returnsAndRankings.annualised_returns.funds_returns["1y"] || 'N/A'}</span>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500">3Y: </span>
                    <span className="text-sm font-medium">{fund.returnsAndRankings.annualised_returns.funds_returns["3y"] || 'N/A'}</span>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500">5Y: </span>
                    <span className="text-sm font-medium">{fund.returnsAndRankings.annualised_returns.funds_returns["5y"] || 'N/A'}</span>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500">All: </span>
                    <span className="text-sm font-medium">{fund.returnsAndRankings.annualised_returns.funds_returns["All"] || 'N/A'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQs */}
          <div className="bg-white rounded-xl shadow p-6">
            <h3 className="font-semibold text-lg mb-4 text-green-700">Frequently Asked Questions</h3>
            {fund.faqs.questions.length > 0 ? (
              <div className="space-y-4">
                {fund.faqs.questions.map((faq, index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="mb-2">
                      <label className="text-xs text-gray-500">Question</label>
                      <p className="text-sm font-medium">{faq.question || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="text-xs text-gray-500">Answer</label>
                      <p className="text-sm">{faq.answer || 'Not specified'}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No FAQs specified</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MutualFundPreview; 