import React from "react";
import { MutualFund } from "../data";

type HoldingAnalysisProps = {
  holdings: MutualFund['holdings'];
};

const HoldingAnalysis: React.FC<HoldingAnalysisProps> = ({ holdings }) => {
  // Calculate analysis from holdings data
  const totalHoldings = holdings.length;
  const equityHoldings = holdings.filter(h => h.field.toLowerCase().includes('equity')).length;
  const debtHoldings = holdings.filter(h => h.field.toLowerCase().includes('debt')).length;
  const cashHoldings = holdings.filter(h => h.field.toLowerCase().includes('cash')).length;

  const equityPercentage = totalHoldings > 0 ? ((equityHoldings / totalHoldings) * 100).toFixed(1) : '0';
  const debtPercentage = totalHoldings > 0 ? ((debtHoldings / totalHoldings) * 100).toFixed(1) : '0';
  const cashPercentage = totalHoldings > 0 ? ((cashHoldings / totalHoldings) * 100).toFixed(1) : '0';

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Holding Analysis</div>
      <div className="text-xs mb-2">Total Holdings: {totalHoldings}</div>
      <div className="flex flex-col gap-2">
        <div className="flex justify-between text-xs items-center">
          <span>Equity</span>
          <span className="font-semibold">{equityPercentage}%</span>
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Debt</span>
          <span className="font-semibold">{debtPercentage}%</span>
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Cash</span>
          <span className="font-semibold">{cashPercentage}%</span>
        </div>
      </div>
      <div className="mt-4">
        <div className="text-xs font-semibold mb-2">Top Holdings by Assets:</div>
        <div className="text-xs">
          {holdings.slice(0, 3).map((holding, index) => (
            <div key={index} className="flex justify-between mb-1">
              <span>{holding.name}</span>
              <span>{holding.assets}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HoldingAnalysis; 