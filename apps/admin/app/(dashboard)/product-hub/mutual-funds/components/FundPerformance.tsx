import React from "react";
import { MutualFund } from "../data";

type FundPerformanceProps = {
  performanceChart: MutualFund['performanceChart'];
};

const FundPerformance: React.FC<FundPerformanceProps> = ({ performanceChart }) => {
  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Fund Performance</div>
      <div className="text-sm text-gray-600">
        Chart data points: {performanceChart.chartData.length} points
      </div>
      <div className="mt-2 text-xs text-gray-500">
        Latest value: {performanceChart.chartData[performanceChart.chartData.length - 1]?.value || 'N/A'}
      </div>
    </div>
  );
};

export default FundPerformance; 