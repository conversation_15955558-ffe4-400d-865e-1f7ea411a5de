import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { MutualFund } from "../data";

type MutualFundSummaryProps = {
  basicInfo: MutualFund['basicInfo'];
  onSave?: (basicInfo: MutualFund['basicInfo']) => void;
  onAddMutualFund?: () => void;
};

const MutualFundSummary: React.FC<MutualFundSummaryProps> = ({ 
  basicInfo: initialBasicInfo,
  onSave,
  onAddMutualFund 
}) => {
  const [basicInfo, setBasicInfo] = useState(initialBasicInfo);

  const handleChange = (field: keyof MutualFund['basicInfo'], value: string | string[]) => {
    setBasicInfo(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(basicInfo);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow p-4 flex flex-col gap-4">
      <input
        className="border rounded px-2 py-1 text-sm text-gray-900"
        placeholder="Enter Fund Name"
        value={basicInfo.fundName}
        onChange={e => handleChange('fundName', e.target.value)}
      />
      <input
        className="border rounded px-2 py-1 text-xs text-green-700 font-semibold"
        placeholder="Enter Flags (separated by |)"
        value={basicInfo.flags.join(' | ')}
        onChange={e => handleChange('flags', e.target.value.split(' | '))}
      />
      <input
        className="border rounded px-2 py-1 text-xs text-gray-600"
        placeholder="Enter Logo URL"
        value={basicInfo.logo_url}
        onChange={e => handleChange('logo_url', e.target.value)}
      />
      <div className="flex flex-col gap-1 mt-2">
        <div className="flex justify-between text-xs items-center">
          <span>AUM</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter AUM"
            value={basicInfo.AUM}
            onChange={e => handleChange('AUM', e.target.value)}
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Currency</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Currency"
            value={basicInfo.currency}
            onChange={e => handleChange('currency', e.target.value)}
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Current NAV</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Current NAV"
            value={basicInfo.current_nav}
            onChange={e => handleChange('current_nav', e.target.value)}
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Expense Ratio</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Expense Ratio"
            value={basicInfo.expense_ratio}
            onChange={e => handleChange('expense_ratio', e.target.value)}
          />
        </div>
        <div className="flex justify-between text-xs items-center">
          <span>Minimum Investment</span>
          <input
            className="border rounded px-2 py-1 w-28 text-right"
            placeholder="Enter Min Investment"
            value={basicInfo.minimum_investment}
            onChange={e => handleChange('minimum_investment', e.target.value)}
          />
        </div>
      </div>
      <Button className="mt-2" onClick={onAddMutualFund}>Add mutual fund</Button>
      <button
        className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-1 ml-0"
        onClick={handleSave}
      >
        Save Info
      </button>
    </div>
  );
};

export default MutualFundSummary; 