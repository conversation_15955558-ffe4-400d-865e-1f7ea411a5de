import React, { useState } from "react";
import { MutualFund } from "../data";

type ReturnsRankingsProps = {
  returnsAndRankings: MutualFund['returnsAndRankings'];
  onSave?: (returnsAndRankings: MutualFund['returnsAndRankings']) => void;
};

const ReturnsRankings: React.FC<ReturnsRankingsProps> = ({ 
  returnsAndRankings: initialReturnsAndRankings,
  onSave 
}) => {
  const [returnsAndRankings, setReturnsAndRankings] = useState(initialReturnsAndRankings);

  const handleChange = (field: keyof MutualFund['returnsAndRankings'], value: string | MutualFund['returnsAndRankings']['annualised_returns']) => {
    setReturnsAndRankings(prev => ({ ...prev, [field]: value }));
  };

  const handleReturnsChange = (type: 'annualised_returns' | 'absolute_returns', period: keyof MutualFund['returnsAndRankings']['annualised_returns']['funds_returns'], value: number) => {
    setReturnsAndRankings(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        funds_returns: {
          ...prev[type].funds_returns,
          [period]: value
        }
      }
    }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(returnsAndRankings);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Returns & Rankings</div>
      <div className="flex flex-col gap-2">
        <div className="flex justify-between text-xs items-center">
          <span>Category</span>
          <input
            className="border rounded px-2 py-1 w-32 text-right"
            placeholder="Enter Category"
            value={returnsAndRankings.category}
            onChange={e => handleChange('category', e.target.value)}
          />
        </div>
        <div className="text-xs font-semibold mt-2">Annualised Returns</div>
        <div className="grid grid-cols-3 gap-1 text-xs">
          <div className="flex justify-between">
            <span>1Y</span>
            <input
              className="border rounded px-1 py-1 w-12 text-right"
              value={returnsAndRankings.annualised_returns.funds_returns["1y"]}
              onChange={e => handleReturnsChange('annualised_returns', '1y', parseFloat(e.target.value) || 0)}
              type="number"
              step="0.1"
            />
          </div>
          <div className="flex justify-between">
            <span>3Y</span>
            <input
              className="border rounded px-1 py-1 w-12 text-right"
              value={returnsAndRankings.annualised_returns.funds_returns["3y"]}
              onChange={e => handleReturnsChange('annualised_returns', '3y', parseFloat(e.target.value) || 0)}
              type="number"
              step="0.1"
            />
          </div>
          <div className="flex justify-between">
            <span>5Y</span>
            <input
              className="border rounded px-1 py-1 w-12 text-right"
              value={returnsAndRankings.annualised_returns.funds_returns["5y"]}
              onChange={e => handleReturnsChange('annualised_returns', '5y', parseFloat(e.target.value) || 0)}
              type="number"
              step="0.1"
            />
          </div>
        </div>
      </div>
      <button
        className="text-xs text-[#05A049] bg-[#e8f5ee] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit mt-2"
        onClick={handleSave}
      >
        Save Info
      </button>
    </div>
  );
};

export default ReturnsRankings; 