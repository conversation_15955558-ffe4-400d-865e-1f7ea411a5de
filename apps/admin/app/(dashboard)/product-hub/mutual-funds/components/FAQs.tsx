import React, { useState } from "react";
import { MutualFund, Faq } from "../data";

type FAQsProps = {
  faqs: MutualFund['faqs'];
  onSave?: (faqs: MutualFund['faqs']) => void;
};

const FAQs: React.FC<FAQsProps> = ({ faqs: initialFaqs, onSave }) => {
  const [faqs, setFaqs] = useState(initialFaqs);

  const handleChange = (index: number, field: keyof Faq, value: string) => {
    setFaqs(prev => ({
      ...prev,
      questions: prev.questions.map((f, i) => i === index ? { ...f, [field]: value } : f)
    }));
  };

  const handleAdd = () => {
    setFaqs(prev => ({
      ...prev,
      questions: [...prev.questions, { question: "", answer: "" }]
    }));
  };

  const handleRemove = (index: number) => {
    setFaqs(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }));
  };

  const handleSave = () => {
    if (onSave) onSave(faqs);
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">FAQs</div>
      <div className="flex flex-col gap-2">
        {faqs.questions.map((faq, i) => (
          <div key={i} className="flex flex-col gap-2 border rounded p-2">
            <input
              className="border rounded px-2 py-1 text-xs"
              placeholder="Enter Question"
              value={faq.question}
              onChange={e => handleChange(i, "question", e.target.value)}
            />
            <textarea
              className="border rounded px-2 py-1 text-xs resize-none"
              placeholder="Enter Answer"
              value={faq.answer}
              onChange={e => handleChange(i, "answer", e.target.value)}
              rows={2}
            />
            <button
              className="text-xs text-red-500 px-2 py-1 rounded hover:bg-red-100 w-fit"
              onClick={() => handleRemove(i)}
              type="button"
            >
              Remove
            </button>
          </div>
        ))}
      </div>
      <div className="flex gap-2 mt-2">
        <button
          className="text-xs text-[#05A049] bg-[#e8f5e0] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit"
          onClick={handleAdd}
          type="button"
        >
          Add FAQ
        </button>
        <button
          className="text-xs text-[#05A049] bg-[#e8f5e0] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit"
          onClick={handleSave}
          type="button"
        >
          Save Info
        </button>
      </div>
    </div>
  );
};

export default FAQs; 