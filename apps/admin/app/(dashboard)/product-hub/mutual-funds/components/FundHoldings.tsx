import React, { useState } from "react";
import { MutualFund, Holding } from "../data";

type FundHoldingsProps = {
  holdings: Holding[];
  onSave?: (holdings: Holding[]) => void;
};

const FundHoldings: React.FC<FundHoldingsProps> = ({ holdings: initialHoldings, onSave }) => {
  const [holdings, setHoldings] = useState<Holding[]>(initialHoldings);

  const handleChange = (index: number, field: keyof Holding, value: string) => {
    setHoldings(prev => prev.map((h, i) => i === index ? { ...h, [field]: value } : h));
  };

  const handleAdd = () => {
    setHoldings(prev => [...prev, { name: "", field: "", assets: "", instrument: "" }]);
  };

  const handleRemove = (index: number) => {
    setHoldings(prev => prev.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    if (onSave) onSave(holdings);
  };

  return (
    <div className="bg-white rounded-xl shadow p-4">
      <div className="font-semibold text-lg mb-2">Fund Holdings</div>
      <div className="flex flex-col gap-2">
        {holdings.map((h, i) => (
          <div key={i} className="flex gap-2 items-center w-full flex-wrap">
            <input
              className="border rounded px-2 py-1 text-xs flex-1 min-w-0 max-w-full"
              style={{ minWidth: 0 }}
              placeholder="Enter Holding Name"
              value={h.name}
              onChange={e => handleChange(i, "name", e.target.value)}
            />
            <input
              className="border rounded px-2 py-1 text-xs flex-1 min-w-0 max-w-full"
              style={{ minWidth: 0 }}
              placeholder="Enter Field"
              value={h.field}
              onChange={e => handleChange(i, "field", e.target.value)}
            />
            <input
              className="border rounded px-2 py-1 text-xs flex-1 min-w-0 max-w-full"
              style={{ minWidth: 0 }}
              placeholder="Enter Assets %"
              value={h.assets}
              onChange={e => handleChange(i, "assets", e.target.value)}
            />
            <input
              className="border rounded px-2 py-1 text-xs flex-1 min-w-0 max-w-full"
              style={{ minWidth: 0 }}
              placeholder="Enter Instrument"
              value={h.instrument}
              onChange={e => handleChange(i, "instrument", e.target.value)}
            />
            <button
              className="text-xs text-red-500 px-2 py-1 rounded hover:bg-red-100"
              onClick={() => handleRemove(i)}
              type="button"
            >
              Remove
            </button>
          </div>
        ))}
      </div>
      <div className="flex gap-2 mt-2">
        <button
          className="text-xs text-[#05A049] bg-[#e8f5e0] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit"
          onClick={handleAdd}
          type="button"
        >
          Add Holding
        </button>
        <button
          className="text-xs text-[#05A049] bg-[#e8f5e0] px-3 py-1 rounded hover:bg-[#d1f5e0] transition-all w-fit"
          onClick={handleSave}
          type="button"
        >
          Save Info
        </button>
      </div>
    </div>
  );
};

export default FundHoldings; 