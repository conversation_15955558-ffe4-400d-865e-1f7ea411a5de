"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { ArrowLeft, Download, MessageCircle, Save, Eye, Send } from "lucide-react";
import MutualFundSummary from "./components/MutualFundSummary";
import FundAbout from "./components/FundAbout";
import FundPerformance from "./components/FundPerformance";
import FundHoldings from "./components/FundHoldings";
import HoldingAnalysis from "./components/HoldingAnalysis";
import AdvancedRatios from "./components/AdvancedRatios";
import MinimumInvestment from "./components/MinimumInvestment";
import ExpenseRatio from "./components/ExpenseRatio";
import ReturnsRankings from "./components/ReturnsRankings";
import FundManagement from "./components/FundManagement";
import PeerComparison from "./components/PeerComparison";
import FAQs from "./components/FAQs";
import SimilarRecommendations from "./components/SimilarRecommendations";
import EssentialsCard from "./components/EssentialsCard";
import { sampleMutualFund, MutualFund } from "./data";
import MutualFundPreview from "./components/MutualFundPreview";
import { createMutualFund } from '@admin/app/lib/productApiService';
import { useRouter } from "next/navigation";

export default function MutualFundDetailPage() {
  const [mode, setMode] = useState<'form' | 'review'>('form');
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [vendorId, setVendorId] = useState<string | null>(null);
  const [fund, setFund] = useState<MutualFund>(sampleMutualFund);
  const [kdUrl, setKdUrl] = useState("");
  const [showKdInput, setShowKdInput] = useState(false);
  const [contactNumber, setContactNumber] = useState("");
  const [showContactInput, setShowContactInput] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const searchParams = new URLSearchParams(window.location.search);
      const vendorParam = searchParams.get('vendor');
      if (vendorParam) {
        setVendorId(vendorParam);
        setFund(prev => ({
          ...prev,
          vendor_id: vendorParam
        }));
      }
    }
  }, []);
  

  const handleBasicInfoSave = (basicInfo: MutualFund['basicInfo']) => {
    setFund(prev => ({ ...prev, basicInfo }));
  };

  const handleEssentialsSave = (essentials: MutualFund['essentials']) => {
    setFund(prev => ({ ...prev, essentials }));
  };

  const handleAboutSave = (about: MutualFund['about']) => {
    setFund(prev => ({ ...prev, about }));
  };

  const handleManagementSave = (managementTeam: MutualFund['managementTeam']) => {
    setFund(prev => ({ ...prev, managementTeam }));
  };

  const handleFeesSave = (fees: MutualFund['fees']) => {
    setFund(prev => ({ ...prev, fees }));
  };

  const handleInvestmentLimitsSave = (investmentLimits: MutualFund['investmentLimits']) => {
    setFund(prev => ({ ...prev, investmentLimits }));
  };

  const handleRatingsSave = (ratings: MutualFund['ratings']) => {
    setFund(prev => ({ ...prev, ratings }));
  };

  const handleHoldingsSave = (holdings: MutualFund['holdings']) => {
    setFund(prev => ({ ...prev, holdings }));
  };

  const handleReturnsSave = (returnsAndRankings: MutualFund['returnsAndRankings']) => {
    setFund(prev => ({ ...prev, returnsAndRankings }));
  };

  const handlePeerComparisonSave = (peerComparison: MutualFund['peerComparison']) => {
    setFund(prev => ({ ...prev, peerComparison }));
  };

  const handleFaqsSave = (faqs: MutualFund['faqs']) => {
    setFund(prev => ({ ...prev, faqs }));
  };

  const validateForm = () => {
    const errors = [];
    
    // Check required fields from basicInfo
    if (!fund.basicInfo?.fundName) errors.push("Fund Name is required");
    if (!fund.basicInfo?.AUM) errors.push("AUM is required");
    if (!fund.basicInfo?.currency) errors.push("Currency is required");
    if (!fund.basicInfo?.current_nav) errors.push("Current NAV is required");
    if (!fund.basicInfo?.expense_ratio) errors.push("Expense Ratio is required");
    if (!fund.basicInfo?.minimum_investment) errors.push("Minimum Investment is required");
    
    // Check required fields from essentials
    if (!fund.essentials?.netAssetValue) errors.push("Net Asset Value is required");
    if (!fund.essentials?.date_of_nav) errors.push("Date of NAV is required");
    if (!fund.essentials?.rating) errors.push("Rating is required");
    if (!fund.essentials?.fund_size) errors.push("Fund Size is required");
    
    // Check required fields from about
    if (!fund.about?.description) errors.push("Fund Description is required");
    if (!fund.about?.experience) errors.push("Experience is required");
    if (!fund.about?.employee_size) errors.push("Employee Size is required");
    if (!fund.about?.offices) errors.push("Offices is required");
    
    // Check required fields from management
    if (!fund.managementTeam || fund.managementTeam.length === 0) {
      errors.push("At least one Management Team member is required");
    }
    
    // Check required fields from fees
    if (fund.fees?.expenseRatio === undefined || fund.fees?.expenseRatio === 0) {
      errors.push("Expense Ratio is required");
    }
    
    // Check required fields from investment limits
    if (!fund.investmentLimits?.firstInvestment) errors.push("First Investment Limit is required");
    if (!fund.investmentLimits?.secondInvestment) errors.push("Second Investment Limit is required");
    if (!fund.investmentLimits?.thirdInvestment) errors.push("Third Investment Limit is required");
    
    // Check required fields from ratings
    if (fund.ratings?.Top5 === undefined) errors.push("Top 5 Rating is required");
    if (fund.ratings?.Top25 === undefined) errors.push("Top 25 Rating is required");
    
    // Check required fields from holdings
    if (!fund.holdings || fund.holdings.length === 0) {
      errors.push("At least one Holding is required");
    }
    
    // Check required fields from returns
    if (!fund.returnsAndRankings?.category) errors.push("Category is required");
    
    return errors;
  };

  const getFormProgress = () => {
    const errors = validateForm();
    const totalFields = 20; // Approximate number of required fields
    const filledFields = totalFields - errors.length;
    return Math.max(0, Math.min(100, (filledFields / totalFields) * 100));
  };

  const handleReview = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    setMode('review');
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Prepare the mutual fund data
      const fundDataToSave = {
        vendor_id: vendorId,
        basicInfo: fund.basicInfo,
        essentials: fund.essentials,
        about: fund.about,
        managementTeam: fund.managementTeam,
        fees: fund.fees,
        investmentLimits: fund.investmentLimits,
        ratings: fund.ratings,
        performanceChart: fund.performanceChart,
        holdings: fund.holdings,
        returnsAndRankings: fund.returnsAndRankings,
        peerComparison: fund.peerComparison,
        faqs: fund.faqs,
        documents: {
          kidAvailable: !!kdUrl,
          kidDoc: kdUrl,
          contactAdvisorAvailable: !!contactNumber,
          contactAdvisor_phone: contactNumber,
        }
      };

      const response: any = await createMutualFund(fundDataToSave);

      if (response && response.statusCode === 201) {
        alert('Mutual fund created successfully!');
        setMode('form');
        router.push('/product-hub');
      } else if (response && response.id) {
        alert('Mutual fund created successfully!');
        setMode('form');
        router.push('/product-hub');
      } else {
        throw new Error('Failed to save mutual fund');
      }
    } catch (error: any) {
      console.error('Error saving mutual fund:', error);
      alert(error.message || 'Failed to save mutual fund. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitWithValidation = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }

    if (!vendorId) {
      alert('Vendor ID is required. Please make sure you have accessed this page with the correct vendor parameter.');
      return;
    }

    await handleSubmit();
  };

  const handleKdUrlSave = () => {
    if (kdUrl.trim()) {
      setShowKdInput(false);
      console.log('KD URL saved:', kdUrl);
      alert('KD URL saved successfully!');
    } else {
      alert('Please enter a valid KD URL');
    }
  };

  const handleKdUrlCancel = () => {
    setKdUrl("");
    setShowKdInput(false);
  };

  const handleContactSave = () => {
    if (contactNumber.trim()) {
      setShowContactInput(false);
      console.log('Contact number saved:', contactNumber);
      alert('Contact number saved successfully!');
    } else {
      alert('Please enter a valid contact number');
    }
  };

  const handleContactCancel = () => {
    setContactNumber("");
    setShowContactInput(false);
  };

  if (mode === 'review') {
    return (
      <MutualFundPreview 
        fund={fund} 
        onEdit={() => setMode('form')} 
        onSave={handleSubmitWithValidation}
        isSaving={isSubmitting}
        kdUrl={kdUrl}
        contactNumber={contactNumber}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e8f5ee] to-[#f6fafd] p-4 flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <Button variant="ghost" size="sm" className="flex items-center gap-2" onClick={()=> router.back()}>
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex gap-2">
          {showKdInput ? (
            <div className="flex gap-2 items-center">
              <input
                type="url"
                placeholder="Enter KD URL"
                value={kdUrl}
                onChange={(e) => setKdUrl(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{ minWidth: '250px' }}
              />
              <Button 
                onClick={handleKdUrlSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Save
              </Button>
              <Button 
                onClick={handleKdUrlCancel}
                size="sm"
                variant="outline"
              >
                Cancel
              </Button>
            </div>
          ) : (
            <Button 
              onClick={() => setShowKdInput(true)}
              variant="outline" 
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" /> 
              {kdUrl ? 'Edit KD URL' : 'Upload KD'}
            </Button>
          )}
          {showContactInput ? (
            <div className="flex gap-2 items-center">
              <input
                type="tel"
                placeholder="Enter contact number"
                value={contactNumber}
                onChange={(e) => setContactNumber(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{ minWidth: '200px' }}
              />
              <Button 
                onClick={handleContactSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Save
              </Button>
              <Button 
                onClick={handleContactCancel}
                size="sm"
                variant="outline"
              >
                Cancel
              </Button>
            </div>
          ) : (
            <Button 
              onClick={() => setShowContactInput(true)}
              variant="outline" 
              className="flex items-center gap-2"
            >
              <MessageCircle className="h-4 w-4" /> 
              {contactNumber ? 'Edit Contact' : 'Contact Advisor'}
            </Button>
          )}
          <Button 
            onClick={handleReview}
            variant="outline"
            className="flex items-center gap-2 border-blue-600 text-blue-600 hover:bg-blue-50"
          >
            <Eye className="h-4 w-4" />
            Preview
          </Button>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Form Progress</span>
          <span className="text-sm text-gray-500">{Math.round(getFormProgress())}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-green-600 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${getFormProgress()}%` }}
          ></div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Sidebar */}
        <div className="lg:col-span-1 flex flex-col gap-6">
          <MutualFundSummary
            basicInfo={fund.basicInfo}
            onSave={handleBasicInfoSave}
            onAddMutualFund={handleSubmitWithValidation}
          />
          <EssentialsCard
            essentials={fund.essentials}
            onSave={handleEssentialsSave}
          />
        </div>
        {/* Main Content */}
        <div className="lg:col-span-3 flex flex-col gap-6">
          <FundAbout 
            about={fund.about}
            onSave={handleAboutSave}
          />
          <FundPerformance performanceChart={fund.performanceChart} />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FundHoldings 
              holdings={fund.holdings}
              onSave={handleHoldingsSave}
            />
            <HoldingAnalysis holdings={fund.holdings} />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <AdvancedRatios 
              ratings={fund.ratings}
              onSave={handleRatingsSave}
            />
            <MinimumInvestment 
              investmentLimits={fund.investmentLimits}
              onSave={handleInvestmentLimitsSave}
            />
            <ExpenseRatio 
              fees={fund.fees}
              onSave={handleFeesSave}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ReturnsRankings
              returnsAndRankings={fund.returnsAndRankings}
              onSave={handleReturnsSave}
            />
            <FundManagement 
              managementTeam={fund.managementTeam}
              onSave={handleManagementSave}
            />
          </div>
          <PeerComparison 
            peerComparison={fund.peerComparison}
            onSave={handlePeerComparisonSave}
          />
          <FAQs 
            faqs={fund.faqs}
            onSave={handleFaqsSave}
          />
          <SimilarRecommendations recommendations={[]} />
        </div>
      </div>
    </div>
  );
}