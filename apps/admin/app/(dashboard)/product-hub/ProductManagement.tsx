"use client";
import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { PlusIcon } from "lucide-react";
import { AddProductModal } from "./AddProductModal";
import { ProductTable } from "./components/ProductTable";
import { OverviewCard } from "./components/OverviewCard";
import { ProductDetailsModal } from "./components/ProductDetailsModal";
import { overviewCards } from "./data";
import { useProducts } from "@admin/app/lib/hooks/api-hooks";
import { Product } from "@admin/lib/productService";

export const ProductHub = (): JSX.Element => {
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<{
    tableProduct: any;
    dbProduct: any;
  } | null>(null);
  const [showProductDetailsModal, setShowProductDetailsModal] = useState(false);
  
  // Use React Query hook
  const { data: products = [], isLoading: loading, error } = useProducts();

  // Handle any errors
  if (error) {
    console.error('Error fetching products:', error);
  }

  // Transform API products to match the expected format for ProductTable
  const transformedProducts = products.map((product) => ({
    symbol: product.stock?.symbol || product.bond?.symbol || product.fund?.symbol || product.structured_product?.symbol || "N/A",
    name: product.name,
    price: product.stock?.current_price ? `$${product.stock.current_price}` : "N/A",
    change: "+0.00%", // This would need to be calculated from historical data
    isPositive: true,
    volume: "Vol: N/A", // This would need to be fetched from market data
    marketCap: product.stock?.market_cap ? `Cap: $${product.stock.market_cap}` : "Cap: N/A",
    sector: product.stock?.sector || "N/A",
    risk: product.risk_level,
    riskColor: product.risk_level === "Low Risk" 
      ? "bg-green-100 text-green-800" 
      : product.risk_level === "Medium Risk"
      ? "bg-yellow-100 text-yellow-800"
      : "bg-red-100 text-red-800",
    availability: product.availability,
    brokers: ["IB", "Schwab", "Fidelity"], // This would need to be fetched from broker data
  }));

  const handleProductAdded = () => {
    // React Query will automatically refetch when mutations invalidate queries
    // No manual refetch needed
  };

  const handleViewProduct = (product: any) => {
    // Find the corresponding database product
    const dbProduct = products.find(p => 
      p.stock?.symbol === product.symbol || 
      p.bond?.symbol === product.symbol || 
      p.fund?.symbol === product.symbol || 
      p.structured_product?.symbol === product.symbol
    );
    
    setSelectedProduct({ tableProduct: product, dbProduct });
    setShowProductDetailsModal(true);
  };

  const handleEditProduct = (product: any) => {
    console.log('Edit product:', product);
    // Add your edit logic here
  };

  const handleArchiveProduct = (product: any) => {
    console.log('Archive product:', product);
    // Add your archive logic here
  };

  return (
    <div className="w-full h-full overflow-y-auto">
      {/* Header */}
      <div className=" px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Product Hub</h1>
            <p className="text-gray-600 text-sm">
              Manage stocks, bonds, funds, and structured products
            </p>
          </div>
          <Button
            className="admin_green_gradient hover:admin_green_gradient_hover rounded-[8px] text-white"
            onClick={() => setShowAddProductModal(true)}
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            Add Product
          </Button>
        </div>
      </div>

      <div className="p-6">

        {/* Products Table */}
        <ProductTable 
          products={transformedProducts}
          dbProducts={products}
          onView={handleViewProduct}
          onEdit={handleEditProduct}
          onArchive={handleArchiveProduct}
          onCategoryChange={(category) => {
            console.log('Category changed to:', category);
            // You can add additional logic here if needed
          }}
        />

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {overviewCards.map((card, index) => (
            <OverviewCard key={index} card={card} />
          ))}
        </div>
      </div>

      {showAddProductModal && (
        <AddProductModal 
          onClose={() => {
            setShowAddProductModal(false);
            handleProductAdded();
          }} 
        />
      )}

      {showProductDetailsModal && selectedProduct && (
        <ProductDetailsModal
          product={selectedProduct.tableProduct}
          dbProduct={selectedProduct.dbProduct}
          onClose={() => {
            setShowProductDetailsModal(false);
            setSelectedProduct(null);
          }}
        />
      )}
    </div>
  );
};
