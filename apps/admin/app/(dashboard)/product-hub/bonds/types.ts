// MongoDB Schema Types
export interface BasicBondInfo {
  yield: number;
  yieldType: string;
  creditRating: string;
  logo: string;
  faceValue: number;
  currency: string;
  paymentFrequency: string;
  maturityDate: string;
}

export interface Essentials {
  minimumInvestment: string;
  allotmentDate: string;
  couponRate: number;
  security: string;
  lastOfferedYTM: number;
  couponType: string;
}

export interface IssueDetails {
  ownership: string;
  businessSector: string;
  debentureTrustee: string;
  listingDetails: boolean;
}

export interface PaymentSchedule {
  year: number;
  currency: string;
  paymentScheduleType: string;
  interestPayout: number;
  redemptionAmount: number;
  redemptionPercentage: number;
  _deleted?: boolean;
}

export interface Risks {
  creditRating: string;
  creditRatingDescription: string;
  creditOutlook: string;
  creditOutlookDescription: string;
  ratingWatch: string;
  ratingWatchDescription: string;
}

export interface Factors {
  yield: string;
  issueSize: string;
  taxSaving: string;
  ratingCategory: string;
  modeOfIssuance: string;
  typeOfGuarantee: string;
  defaultHistory: string;
  repaymentPriority: string;
  securityWithCollateral: string;
}

export interface SWOTAnalysis {
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
  threats: string[];
}

export interface Documents {
  kidAvailable: boolean;
  gidAvailable: boolean;
  ratingRationaleAvailable: boolean;
  contactAdvisorAvailable: boolean;
  cashflow_structure_available: boolean;
  kidDoc: string;
  gidDoc: string;
  ratingRationaleDoc: string;
  contactAdvisor_phone: string;
  cashflow_structure_doc?: string;
}

// Main Bond Interface
export interface Bond {
  _id?: string;
  productId: string;
  bondName: string;
  basicBondInfo: BasicBondInfo;
  essentials: Essentials;
  issueDetails: IssueDetails;
  paymentSchedule: PaymentSchedule[];
  risks: Risks;
  factors: Factors;
  swotAnalysis: SWOTAnalysis;
  documents: Documents;
  createdAt?: Date;
  updatedAt?: Date;
}

// Component Props Types
export interface BondSummaryProps {
  bond: Bond;
  onEdit?: (field: string, value: any) => void;
  isEditing?: boolean;
}

export interface CashflowStructureProps {
  paymentSchedule: PaymentSchedule[];
  onEdit?: (index: number, data: PaymentSchedule) => void;
  isEditing?: boolean;
}

export interface RisksAndFactorsProps {
  risks: Risks;
  factors: Factors;
  onEdit?: (section: 'risks' | 'factors', field: string, value: any) => void;
  isEditing?: boolean;
}

export interface SwotAnalysisProps {
  swotAnalysis: SWOTAnalysis;
  onEdit?: (section: keyof SWOTAnalysis, index: number, value: string) => void;
  onAdd?: (section: keyof SWOTAnalysis, value: string) => void;
  onRemove?: (section: keyof SWOTAnalysis, index: number) => void;
  isEditing?: boolean;
}

export interface SimilarRecommendationsProps {
  recommendations: Bond[];
  onSelect?: (bond: Bond) => void;
}

export interface BondPerformanceProps {
  title: string;
  metrics: Array<{
    label: string;
    value: string | number;
    change?: string;
    isPositive?: boolean;
  }>;
  showChart?: boolean;
}

export interface BondDetailsProps {
  title: string;
  details: Array<{
    label: string;
    value: string | number;
    description?: string;
  }>;
  isExpanded?: boolean;
}

export interface BondComparisonProps {
  bonds: Bond[];
  title?: string;
}