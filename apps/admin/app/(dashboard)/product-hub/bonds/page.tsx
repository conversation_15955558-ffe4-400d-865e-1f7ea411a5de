"use client";
import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { ArrowLeft, Edit, Save, X } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "axios";
import { ConfirmationModal } from "@admin/components/ui/confirmation-modal";
import {
  BondSummary,
  CashflowStructure,
  RisksAndFactors,
  SwotAnalysis,
  SimilarRecommendations,
  BondDocuments,
  BondEssentials,
  BondIssueDetails,
} from "./components";
import { Bond } from "./types";
import { createBond } from '@admin/app/lib/productApiService';

// Mock data for demonstration based on MongoDB schema
const mockBondData: Bond = {
  productId: "BOND001",
  bondName: "Dubai SUKUK LTD",
  basicBondInfo: {
    yield: 7.5,
    yieldType: "SUKUK",
    creditRating: "AA",
    logo: "/logo.png",
    faceValue: 100000,
    currency: "AED",
    paymentFrequency: "Semi-Annual",
    maturityDate: "October 2026",
  },
  essentials: {
    minimumInvestment: "₹1,00,10,717",
    allotmentDate: "Sep 12, 2024",
    couponRate: 6.95,
    security: "Secured",
    lastOfferedYTM: 9.5,
    couponType: "Fixed interest",
  },
  issueDetails: {
    ownership: "Non PSU",
    businessSector: "Finance",
    debentureTrustee: "Catalyst",
    listingDetails: true,
  },
  paymentSchedule: [],
  risks: {
    creditRating: "AA",
    creditRatingDescription:
      "Instruments with this rating are considered to have high degree of safety regarding timely servicing of financial obligations. Such instruments carry very low credit risk.",
    creditOutlook: "Stable",
    creditOutlookDescription:
      "A Stable outlook indicates that the Rating is likely to remain unchanged",
    ratingWatch: "None",
    ratingWatchDescription: "Not Applicable",
  },
  factors: {
    yield: "High",
    issueSize: "Large",
    taxSaving: "No",
    ratingCategory: "AA",
    modeOfIssuance: "Public Issue",
    typeOfGuarantee: "Corporate",
    defaultHistory: "0",
    repaymentPriority: "1",
    securityWithCollateral: "Yes",
  },
  swotAnalysis: {
    strengths: [],
    weaknesses: [],
    opportunities: [],
    threats: [],
  },
  documents: {
    kidAvailable: true,
    gidAvailable: true,
    ratingRationaleAvailable: true,
    contactAdvisorAvailable: true,
    cashflow_structure_available: true,
    kidDoc: "/documents/kid.pdf",
    gidDoc: "/documents/gid.pdf",
    ratingRationaleDoc: "/documents/rationale.pdf",
    contactAdvisor_phone: "+971-50-123-4567",
  },
};

const similarRecommendations: Bond[] = [
  {
    ...mockBondData,
    productId: "BOND002",
    bondName: "Abu Dhabi SUKUK LTD",
    basicBondInfo: {
      ...mockBondData.basicBondInfo,
      yield: 7.25,
      faceValue: 50000,
    },
  },
  {
    ...mockBondData,
    productId: "BOND003",
    bondName: "Sharjah SUKUK LTD",
    basicBondInfo: {
      ...mockBondData.basicBondInfo,
      yield: 7.75,
      faceValue: 75000,
    },
  },
];

const Bonds = () => {
  const router = useRouter();
  const [bondData, setBondData] = useState<Bond>(mockBondData);
  const [isEditing, setIsEditing] = useState(true);
  const [vendor_id, setVendorId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [alert, setAlert] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const searchParams = useSearchParams();

  useEffect(() => {
    const vendorParam = searchParams.get("vendor");
    if (vendorParam) {
      setVendorId(vendorParam);
    }
  }, [searchParams]);

  // Auto-hide alert after 3 seconds
  useEffect(() => {
    if (alert) {
      const timer = setTimeout(() => {
        setAlert(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [alert]);

  const handleSaveChanges = async () => {
    if (!vendor_id) {
      setAlert({
        type: "error",
        message: "Vendor ID is required",
      });
      return;
    }

    setIsLoading(true);
    try {
      const bondPayload = {
        vendor_id,
        bondName: bondData.bondName,
        basicBondInfo: bondData.basicBondInfo,
        essentials: bondData.essentials,
        issueDetails: bondData.issueDetails,
        paymentSchedule: bondData.paymentSchedule,
        risks: bondData.risks,
        factors: bondData.factors,
        swotAnalysis: bondData.swotAnalysis,
        documents: bondData.documents,
      };

      const response: any = await createBond(bondPayload);

      if (response && response.statusCode === 201) {
        setAlert({
          type: "success",
          message: "Bond created successfully",
        });
        setIsRedirecting(true);
        setBondData(mockBondData);
        setIsEditing(true);
        router.push("/product-hub");
      } else if (response && response.id) {
        setAlert({
          type: "success",
          message: "Bond created successfully",
        });
        setIsRedirecting(true);
        setBondData(mockBondData);
        setIsEditing(true);
        router.push("/product-hub");
      } else {
        throw new Error('Failed to create bond');
      }
    } catch (error: any) {
      setAlert({
        type: "error",
        message: error.message || "Failed to create bond",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (field: string, value: any) => {
    const fieldPath = field.split(".");
    setBondData((prev) => {
      const newData = { ...prev };
      let current: any = newData;

      for (let i = 0; i < fieldPath.length - 1; i++) {
        current = current[fieldPath[i]];
      }

      current[fieldPath[fieldPath.length - 1]] = value;
      return newData;
    });
  };

  const handlePaymentScheduleEdit = (index: number, data: any) => {
    if (data._deleted) {
      setBondData((prev) => ({
        ...prev,
        paymentSchedule: prev.paymentSchedule.filter((_, i) => i !== index),
      }));
    } else if (index === bondData.paymentSchedule.length) {
      // Add new row
      setBondData((prev) => ({
        ...prev,
        paymentSchedule: [...prev.paymentSchedule, data],
      }));
    } else {
      setBondData((prev) => ({
        ...prev,
        paymentSchedule: prev.paymentSchedule.map((item, i) =>
          i === index ? data : item
        ),
      }));
    }
  };

  const handleRisksFactorsEdit = (
    section: "risks" | "factors",
    field: string,
    value: any
  ) => {
    setBondData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleSwotEdit = (
    section: keyof Bond["swotAnalysis"],
    index: number,
    value: string
  ) => {
    setBondData((prev) => ({
      ...prev,
      swotAnalysis: {
        ...prev.swotAnalysis,
        [section]: prev.swotAnalysis[section].map((item, i) =>
          i === index ? value : item
        ),
      },
    }));
  };

  const handleSwotAdd = (
    section: keyof Bond["swotAnalysis"],
    value: string
  ) => {
    setBondData((prev) => ({
      ...prev,
      swotAnalysis: {
        ...prev.swotAnalysis,
        [section]: [...prev.swotAnalysis[section], value],
      },
    }));
  };

  const handleSwotRemove = (
    section: keyof Bond["swotAnalysis"],
    index: number
  ) => {
    setBondData((prev) => ({
      ...prev,
      swotAnalysis: {
        ...prev.swotAnalysis,
        [section]: prev.swotAnalysis[section].filter((_, i) => i !== index),
      },
    }));
  };

  const handleSimilarBondSelect = (bond: Bond) => {
    // Navigate to the selected bond or update current view
    console.log("Selected bond:", bond);
  };

  return (
    <div className="min-h-screen bg-[#f6faf9] p-6 flex flex-col gap-6">
      {alert && (
        <div
          className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg ${
            alert.type === "success"
              ? "bg-green-600 text-white"
              : "bg-red-600 text-white"
          }`}
        >
          {alert.message}
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2"
          disabled={isLoading || isRedirecting}
          onClick={() => router.push("/product-hub")}
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>

        <div className="flex items-center gap-2">
          {isEditing ? (
            <>
              <Button
                onClick={() => {
                  setBondData(mockBondData);
                  setIsEditing(false);
                }}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                disabled={isLoading || isRedirecting}
              >
                <Save className="h-4 w-4" />
                Save Changes
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setBondData(mockBondData);
                  setIsEditing(false);
                }}
                className="flex items-center gap-2"
                disabled={isLoading || isRedirecting}
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
            </>
          ) : (
            <>
              <Button
                onClick={() => setShowConfirmation(true)}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 mr-2"
                disabled={isLoading || isRedirecting}
              >
                <Edit className="h-4 w-4" />
                Add Bond
              </Button>
              <Button
                onClick={() => setIsEditing(true)}
                className="flex items-center gap-2"
                disabled={isLoading || isRedirecting}
              >
                <Edit className="h-4 w-4" />
                Edit Bond
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="flex flex-row gap-6">
        {/* Left Panel */}
        <div className="w-1/4 flex flex-col gap-6">
          <BondSummary
            bond={bondData}
            onEdit={handleEdit}
            isEditing={isEditing}
          />
          <BondEssentials
            essentials={bondData.essentials}
            onEdit={handleEdit}
            isEditing={isEditing}
          />
          <BondIssueDetails
            issueDetails={bondData.issueDetails}
            onEdit={handleEdit}
            isEditing={isEditing}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col gap-6">
          <CashflowStructure
            paymentSchedule={bondData.paymentSchedule}
            onEdit={handlePaymentScheduleEdit}
            isEditing={isEditing}
          />
          <RisksAndFactors
            risks={bondData.risks}
            factors={bondData.factors}
            onEdit={handleRisksFactorsEdit}
            isEditing={isEditing}
          />
          <SwotAnalysis
            swotAnalysis={bondData.swotAnalysis}
            onEdit={handleSwotEdit}
            onAdd={handleSwotAdd}
            onRemove={handleSwotRemove}
            isEditing={isEditing}
          />
          <BondDocuments
            documents={bondData.documents}
            onEdit={handleEdit}
            isEditing={isEditing}
          />
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmation}
        onConfirm={handleSaveChanges}
        onCancel={() => setShowConfirmation(false)}
        title="Add New Bond"
        message="Are you sure you want to add this bond? This action will create a new bond entry in the system."
        confirmText="Add Bond"
        cancelText="Cancel"
        isLoading={isLoading}
      />
    </div>
  );
};

export default Bonds;
