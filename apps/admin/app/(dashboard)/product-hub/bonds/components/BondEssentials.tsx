import React, { useState } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Edit2, Save, X } from "lucide-react";
import { Essentials } from "../types";

interface BondEssentialsProps {
  essentials: Essentials;
  onEdit?: (field: string, value: any) => void;
  isEditing?: boolean;
}

const BondEssentials: React.FC<BondEssentialsProps> = ({
  essentials,
  onEdit,
  isEditing = false,
}) => {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>("");

  const handleEdit = (field: string, currentValue: any) => {
    setEditingField(field);
    setEditValue(String(currentValue));
  };

  const handleSave = () => {
    if (editingField && onEdit) {
      // Convert value back to appropriate type
      let finalValue: any = editValue;
      if (editingField === 'couponRate' || editingField === 'lastOfferedYTM') {
        finalValue = parseFloat(editValue);
      }
      onEdit(`essentials.${editingField}`, finalValue);
    }
    setEditingField(null);
    setEditValue("");
  };

  const handleCancel = () => {
    setEditingField(null);
    setEditValue("");
  };

  const renderEditableField = (
    label: string,
    value: any,
    field: keyof Essentials,
    type: "text" | "number" = "text",
    suffix?: string
  ) => {
    const isFieldEditing = editingField === field;
    const displayValue = suffix ? `${value}${suffix}` : value;
    
    return (
      <div className="flex items-center justify-between p-3 border rounded-lg">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">{label}:</span>
        </div>
        {isFieldEditing ? (
          <div className="flex items-center gap-1">
            <Input
              type={type}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="text-sm h-8 w-32"
              placeholder={label}
            />
            {suffix && <span className="text-sm text-gray-500">{suffix}</span>}
            <Button size="sm" onClick={handleSave} className="h-6 w-6 p-0">
              <Save className="h-3 w-3" />
            </Button>
            <Button size="sm" onClick={handleCancel} className="h-6 w-6 p-0">
              <X className="h-3 w-3" />
            </Button>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold text-gray-900">{displayValue}</span>
            {isEditing && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleEdit(field, value)}
                className="h-4 w-4 p-0"
              >
                <Edit2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div className="text-[#1a9c7c] font-semibold mb-4">Essentials</div>
      
      <div className="space-y-3">
        {renderEditableField(
          "Minimum Investment",
          essentials.minimumInvestment,
          "minimumInvestment",
          "text"
        )}
        {renderEditableField(
          "Allotment Date",
          essentials.allotmentDate,
          "allotmentDate",
          "text"
        )}
        {renderEditableField(
          "Coupon Rate",
          essentials.couponRate,
          "couponRate",
          "number",
          "%"
        )}
        {renderEditableField(
          "Security",
          essentials.security,
          "security",
          "text"
        )}
        {renderEditableField(
          "Last Offered YTM",
          essentials.lastOfferedYTM,
          "lastOfferedYTM",
          "number",
          "%"
        )}
        {renderEditableField(
          "Coupon Type",
          essentials.couponType,
          "couponType",
          "text"
        )}
      </div>
    </div>
  );
};

export default BondEssentials; 