import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Edit2, Save, X, Plus, Trash2 } from "lucide-react";
import { SwotAnalysisProps } from "../types";

const SwotAnalysis: React.FC<SwotAnalysisProps> = ({
  swotAnalysis,
  onEdit,
  onAdd,
  onRemove,
  isEditing = false,
}) => {
  const [editingItem, setEditingItem] = useState<{ section: keyof typeof swotAnalysis; index: number } | null>(null);
  const [editValue, setEditValue] = useState<string>("");
  // Use an object to track newItem for each section
  const [newItem, setNewItem] = useState<Partial<Record<keyof typeof swotAnalysis, string>>>({});

  const handleEdit = (section: keyof typeof swotAnalysis, index: number, value: string) => {
    setEditingItem({ section, index });
    setEditValue(value);
  };

  const handleSave = () => {
    if (editingItem && onEdit) {
      onEdit(editingItem.section, editingItem.index, editValue);
    }
    setEditingItem(null);
    setEditValue("");
  };

  const handleCancel = () => {
    setEditingItem(null);
    setEditValue("");
  };

  // Update handleAdd to use section-specific newItem
  const handleAdd = (section: keyof typeof swotAnalysis) => {
    const value = newItem[section]?.trim();
    if (value && onAdd) {
      onAdd(section, value);
      setNewItem((prev) => ({ ...prev, [section]: "" }));
    }
  };

  const handleRemove = (section: keyof typeof swotAnalysis, index: number) => {
    if (onRemove) {
      onRemove(section, index);
    }
  };

  const renderSwotSection = (
    title: string,
    section: keyof typeof swotAnalysis,
    items: string[]
  ) => {
    return (
      <div>
        <div className="font-bold text-[#1a9c7c] mb-2">{title}</div>
        {items.length > 0 ? (
          <ul className="list-disc ml-4 text-gray-700 space-y-1">
            {items.map((item, index) => {
              const isEditingItem = editingItem?.section === section && editingItem?.index === index;
              return (
                <li key={index} className="flex items-start justify-between group">
                  {isEditingItem ? (
                    <div className="flex items-center gap-1 flex-1">
                      <Input
                        value={editValue}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditValue(e.target.value)}
                        className="text-xs h-6 flex-1"
                      />
                      <Button size="sm" onClick={handleSave} className="h-6 w-6 p-0">
                        <Save className="h-3 w-3" />
                      </Button>
                      <Button size="sm" onClick={handleCancel} className="h-6 w-6 p-0">
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <>
                      <span className="flex-1">{item}</span>
                      {isEditing && (
                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEdit(section, index, item)}
                            className="h-4 w-4 p-0"
                          >
                            <Edit2 className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleRemove(section, index)}
                            className="h-4 w-4 p-0 text-red-500"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </li>
              );
            })}
          </ul>
        ) : (
          <div className="text-gray-500 text-xs italic ml-4">
            No {title.toLowerCase()} added yet
          </div>
        )}
        {isEditing && (
          <div className="flex items-center gap-1 mt-2">
            <Input
              value={newItem[section] || ""}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewItem((prev) => ({ ...prev, [section]: e.target.value }))}
              placeholder={`Add new ${title.toLowerCase().slice(0, -1)}`}
              className="text-xs h-6 flex-1"
              onKeyPress={(e) => e.key === 'Enter' && handleAdd(section)}
            />
            <Button size="sm" onClick={() => handleAdd(section)} className="h-6 w-6 p-0">
              <Plus className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div className="text-[#1a9c7c] font-semibold mb-4">SWOT Analysis</div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
        {renderSwotSection("Strengths", "strengths", swotAnalysis.strengths)}
        {renderSwotSection("Weakness", "weaknesses", swotAnalysis.weaknesses)}
        {renderSwotSection("Opportunities", "opportunities", swotAnalysis.opportunities)}
        {renderSwotSection("Threats", "threats", swotAnalysis.threats.length > 0 ? swotAnalysis.threats : [])}
      </div>
    </div>
  );
};

export default SwotAnalysis; 