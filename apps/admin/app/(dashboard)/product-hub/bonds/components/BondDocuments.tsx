import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Edit2, Save, X, FileText, Phone } from "lucide-react";
import { Bond } from "../types";

interface BondDocumentsProps {
  documents: Bond['documents'];
  onEdit?: (field: string, value: any) => void;
  isEditing?: boolean;
}

const BondDocuments: React.FC<BondDocumentsProps> = ({
  documents,
  onEdit,
  isEditing = false,
}) => {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>("");

  const handleEdit = (field: string, currentValue: any) => {
    setEditingField(field);
    setEditValue(String(currentValue));
  };

  const handleSave = () => {
    if (editingField && onEdit) {
      onEdit(editingField, editValue);
    }
    setEditingField(null);
    setEditValue("");
  };

  const handleCancel = () => {
    setEditingField(null);
    setEditValue("");
  };

  const handleCheckboxChange = (field: string, checked: boolean) => {
    if (onEdit) {
      onEdit(`documents.${field}`, checked);
    }
  };

  const renderEditableField = (
    label: string,
    value: string,
    field: string,
    icon?: React.ReactNode,
    isAvailable?: boolean
  ) => {
    const isFieldEditing = editingField === field;
    
    // If not available, show disabled state
    if (!isAvailable) {
      return (
        <div className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
          <div className="flex items-center gap-2">
            {icon}
            <span className="text-sm font-medium text-gray-400">{label}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-400">Not Available</span>
          </div>
        </div>
      );
    }
    
    return (
      <div className="flex items-center justify-between p-3 border rounded-lg">
        <div className="flex items-center gap-2">
          {icon}
          <span className="text-sm font-medium">{label}</span>
        </div>
        {isFieldEditing ? (
          <div className="flex items-center gap-1">
            <Input
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="text-xs h-6 w-48"
              placeholder="Enter URL"
            />
            <Button size="sm" onClick={handleSave} className="h-6 w-6 p-0">
              <Save className="h-3 w-3" />
            </Button>
            <Button size="sm" onClick={handleCancel} className="h-6 w-6 p-0">
              <X className="h-3 w-3" />
            </Button>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">{value || "Not set"}</span>
            {isEditing && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleEdit(field, value)}
                className="h-4 w-4 p-0"
              >
                <Edit2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div className="text-[#1a9c7c] font-semibold mb-4">Documents & Contact</div>
      
      <div className="space-y-4">
        {/* Document Availability */}
        <div>
          <h4 className="text-sm font-semibold mb-3 text-gray-700">Document Availability</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <input
                type="checkbox"
                id="kidAvailable"
                checked={documents.kidAvailable}
                onChange={(e) => handleCheckboxChange('kidAvailable', e.target.checked)}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 rounded"
              />
              <label htmlFor="kidAvailable" className="text-sm font-medium">
                KID Available
              </label>
            </div>
            
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <input
                type="checkbox"
                id="gidAvailable"
                checked={documents.gidAvailable}
                onChange={(e) => handleCheckboxChange('gidAvailable', e.target.checked)}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 rounded"
              />
              <label htmlFor="gidAvailable" className="text-sm font-medium">
                GID Available
              </label>
            </div>
            
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <input
                type="checkbox"
                id="ratingRationaleAvailable"
                checked={documents.ratingRationaleAvailable}
                onChange={(e) => handleCheckboxChange('ratingRationaleAvailable', e.target.checked)}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 rounded"
              />
              <label htmlFor="ratingRationaleAvailable" className="text-sm font-medium">
                Rating Rationale Available
              </label>
            </div>
            
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <input
                type="checkbox"
                id="contactAdvisorAvailable"
                checked={documents.contactAdvisorAvailable}
                onChange={(e) => handleCheckboxChange('contactAdvisorAvailable', e.target.checked)}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 rounded"
              />
              <label htmlFor="contactAdvisorAvailable" className="text-sm font-medium">
                Contact Advisor Available
              </label>
            </div>
            
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <input
                type="checkbox"
                id="cashflowStructureAvailable"
                checked={documents.cashflow_structure_available}
                onChange={(e) => handleCheckboxChange('cashflow_structure_available', e.target.checked)}
                disabled={!isEditing}
                className="h-4 w-4 text-blue-600 rounded"
              />
              <label htmlFor="cashflowStructureAvailable" className="text-sm font-medium">
                Cashflow Structure Available
              </label>
            </div>
          </div>
        </div>

        {/* Document URLs */}
        <div>
          <h4 className="text-sm font-semibold mb-3 text-gray-700">Document URLs</h4>
          <div className="space-y-3">
            {renderEditableField(
              "KID Document URL",
              documents.kidDoc,
              "documents.kidDoc",
              <FileText className="h-4 w-4 text-blue-500" />,
              documents.kidAvailable
            )}
            {renderEditableField(
              "GID Document URL",
              documents.gidDoc,
              "documents.gidDoc",
              <FileText className="h-4 w-4 text-green-500" />,
              documents.gidAvailable
            )}
            {renderEditableField(
              "Rating Rationale Document URL",
              documents.ratingRationaleDoc,
              "documents.ratingRationaleDoc",
              <FileText className="h-4 w-4 text-purple-500" />,
              documents.ratingRationaleAvailable
            )}
            {renderEditableField(
              "Cashflow Structure Document URL",
              documents.cashflow_structure_doc || "",
              "documents.cashflow_structure_doc",
              <FileText className="h-4 w-4 text-orange-500" />,
              documents.cashflow_structure_available
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h4 className="text-sm font-semibold mb-3 text-gray-700">Contact Information</h4>
          {renderEditableField(
            "Advisor Phone Number",
            documents.contactAdvisor_phone,
            "documents.contactAdvisor_phone",
            <Phone className="h-4 w-4 text-red-500" />,
            documents.contactAdvisorAvailable
          )}
        </div>
      </div>
    </div>
  );
};

export default BondDocuments;