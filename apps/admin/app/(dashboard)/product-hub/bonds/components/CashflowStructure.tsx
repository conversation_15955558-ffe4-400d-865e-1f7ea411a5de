import React, { useState, useEffect } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Edit2, Save, X, Plus, Trash2 } from "lucide-react";
import { CashflowStructureProps, PaymentSchedule } from "../types";

const CashflowStructure: React.FC<CashflowStructureProps> = ({ 
  paymentSchedule, 
  onEdit, 
  isEditing = false 
}) => {
  const [editingRow, setEditingRow] = useState<number | null>(null);
  const [editData, setEditData] = useState<PaymentSchedule | null>(null);
  const [lastAddedRow, setLastAddedRow] = useState<number | null>(null);

  const handleEdit = (index: number, data: PaymentSchedule) => {
    setEditingRow(index);
    setEditData({ ...data });
  };

  const handleSave = () => {
    if (editingRow !== null && editData && onEdit) {
      onEdit(editingRow, editData);
    }
    setEditingRow(null);
    setEditData(null);
    setLastAddedRow(null);
  };

  const handleCancel = () => {
    setEditingRow(null);
    setEditData(null);
    setLastAddedRow(null);
  };

  const handleAdd = () => {
    const newSchedule: PaymentSchedule = {
      year: new Date().getFullYear(),
      currency: "AED",
      paymentScheduleType: "Semi Annually",
      interestPayout: 0,
      redemptionAmount: 0,
      redemptionPercentage: 0,
    };
    if (onEdit) {
      onEdit(paymentSchedule.length, newSchedule);
      setLastAddedRow(paymentSchedule.length);
    }
  };

  const handleRemove = (index: number) => {
    if (onEdit) {
      onEdit(index, { ...paymentSchedule[index], _deleted: true });
    }
  };

  const updateEditData = (field: keyof PaymentSchedule, value: any) => {
    if (editData) {
      setEditData({ ...editData, [field]: value });
    }
  };

  useEffect(() => {
    if (
      lastAddedRow !== null &&
      paymentSchedule.length > lastAddedRow &&
      paymentSchedule[lastAddedRow]
    ) {
      setEditingRow(lastAddedRow);
      setEditData({ ...paymentSchedule[lastAddedRow] });
      setLastAddedRow(null);
    }
  }, [paymentSchedule.length, lastAddedRow, paymentSchedule]);

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div className="flex items-center gap-2 mb-6">
        <span className="text-[#1a9c7c] font-semibold text-lg">What can I earn?</span>
        <span className="ml-auto bg-[#e6f7f2] text-[#1a9c7c] px-3 py-1 rounded-full text-xs font-medium">
          Cashflow Structure
        </span>
        {isEditing && (
          <Button 
            size="sm" 
            onClick={handleAdd} 
            className="flex items-center gap-2 bg-[#1a9c7c] hover:bg-[#158a6a] text-white px-4"
          >
            <Plus className="h-4 w-4" />
            Add Row
          </Button>
        )}
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full text-sm mb-2">
          <thead>
            <tr className="text-gray-600 border-b">
              <th className="text-left font-semibold py-3 px-4">Year</th>
              <th className="text-left font-semibold py-3 px-4">Currency</th>
              <th className="text-left font-semibold py-3 px-4">Payment Schedule</th>
              <th className="text-left font-semibold py-3 px-4">Interest Payout</th>
              <th className="text-left font-semibold py-3 px-4">Redemption Amount</th>
              <th className="text-left font-semibold py-3 px-4">Redemption Value (%)</th>
              {isEditing && <th className="text-left font-semibold py-3 px-4">Actions</th>}
            </tr>
          </thead>
          <tbody>
            {paymentSchedule.map((row, index) => {
              const isRowEditing = editingRow === index;
              return (
                <tr key={index} className={`border-b border-gray-100 ${isRowEditing ? 'bg-gray-50' : 'hover:bg-gray-50'}`}>
                  <td className="py-4 px-4">
                    {isRowEditing ? (
                      <Input
                        type="number"
                        value={editData?.year === undefined || editData?.year === null ? '' : editData.year}
                        onChange={(e) => updateEditData('year', e.target.value === '' ? '' : parseInt(e.target.value))}
                        className="w-24 h-9 text-sm border-gray-300 focus:border-[#1a9c7c] focus:ring-[#1a9c7c]"
                      />
                    ) : (
                      <span className="font-medium">{row.year}</span>
                    )}
                  </td>
                  <td className="py-4 px-4">
                    {isRowEditing ? (
                      <Input
                        value={editData?.currency === undefined || editData?.currency === null ? '' : editData.currency}
                        onChange={(e) => updateEditData('currency', e.target.value)}
                        className="w-24 h-9 text-sm border-gray-300 focus:border-[#1a9c7c] focus:ring-[#1a9c7c]"
                      />
                    ) : (
                      row.currency
                    )}
                  </td>
                  <td className="py-4 px-4">
                    {isRowEditing ? (
                      <Input
                        value={editData?.paymentScheduleType === undefined || editData?.paymentScheduleType === null ? '' : editData.paymentScheduleType}
                        onChange={(e) => updateEditData('paymentScheduleType', e.target.value)}
                        className="w-40 h-9 text-sm border-gray-300 focus:border-[#1a9c7c] focus:ring-[#1a9c7c]"
                      />
                    ) : (
                      row.paymentScheduleType
                    )}
                  </td>
                  <td className="py-4 px-4">
                    {isRowEditing ? (
                      <Input
                        type="number"
                        value={editData?.interestPayout === undefined || editData?.interestPayout === null ? '' : editData.interestPayout}
                        onChange={(e) => updateEditData('interestPayout', e.target.value === '' ? '' : parseFloat(e.target.value))}
                        className="w-32 h-9 text-sm border-gray-300 focus:border-[#1a9c7c] focus:ring-[#1a9c7c]"
                      />
                    ) : (
                      <span className="font-medium">{`${row.currency} ${row.interestPayout.toLocaleString()}`}</span>
                    )}
                  </td>
                  <td className="py-4 px-4">
                    {isRowEditing ? (
                      <Input
                        type="number"
                        value={editData?.redemptionAmount === undefined || editData?.redemptionAmount === null ? '' : editData.redemptionAmount}
                        onChange={(e) => updateEditData('redemptionAmount', e.target.value === '' ? '' : parseFloat(e.target.value))}
                        className="w-32 h-9 text-sm border-gray-300 focus:border-[#1a9c7c] focus:ring-[#1a9c7c]"
                      />
                    ) : (
                      <span className="font-medium">{`${row.currency} ${row.redemptionAmount.toLocaleString()}`}</span>
                    )}
                  </td>
                  <td className="py-4 px-4">
                    {isRowEditing ? (
                      <Input
                        type="number"
                        value={editData?.redemptionPercentage === undefined || editData?.redemptionPercentage === null ? '' : editData.redemptionPercentage}
                        onChange={(e) => updateEditData('redemptionPercentage', e.target.value === '' ? '' : parseFloat(e.target.value))}
                        className="w-24 h-9 text-sm border-gray-300 focus:border-[#1a9c7c] focus:ring-[#1a9c7c]"
                      />
                    ) : (
                      <span className="font-medium">{`${row.redemptionPercentage}%`}</span>
                    )}
                  </td>
                  {isEditing && (
                    <td className="py-4 px-4">
                      {isRowEditing ? (
                        <div className="flex items-center gap-2">
                          <Button 
                            size="sm" 
                            onClick={handleSave} 
                            className="h-9 w-9 p-0 bg-green-600 hover:bg-green-700"
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button 
                            size="sm" 
                            onClick={handleCancel} 
                            className="h-9 w-9 p-0 bg-gray-200 hover:bg-gray-300 text-gray-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                          <Button 
                            size="sm" 
                            onClick={() => handleRemove(index)} 
                            className="h-9 w-9 p-0 bg-red-100 hover:bg-red-200 text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEdit(index, row)}
                            className="h-9 w-9 p-0 hover:bg-gray-100"
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleRemove(index)}
                            className="h-9 w-9 p-0 text-red-500 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CashflowStructure;