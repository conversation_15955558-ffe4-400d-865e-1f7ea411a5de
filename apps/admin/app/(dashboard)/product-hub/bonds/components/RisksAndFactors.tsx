import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Edit2, Save, X } from "lucide-react";
import { RisksAndFactorsProps } from "../types";

const RisksAndFactors: React.FC<RisksAndFactorsProps> = ({ 
  risks, 
  factors, 
  onEdit, 
  isEditing = false 
}) => {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>("");

  const handleEdit = (section: 'risks' | 'factors', field: string, currentValue: any) => {
    setEditingField(`${section}.${field}`);
    setEditValue(String(currentValue));
  };

  const handleSave = () => {
    if (editingField && onEdit) {
      const [section, field] = editingField.split('.') as ['risks' | 'factors', string];
      onEdit(section, field, editValue);
    }
    setEditingField(null);
    setEditValue("");
  };

  const handleCancel = () => {
    setEditingField(null);
    setEditValue("");
  };

  const renderEditableField = (
    label: string, 
    value: any, 
    section: 'risks' | 'factors',
    field: string,
    isTextarea = false
  ) => {
    const isFieldEditing = editingField === `${section}.${field}`;
    
    return (
      <div className="text-xs text-gray-600 mb-2">
        <div className="font-medium">{label}:</div>
        {isFieldEditing ? (
          <div className="mt-1">
            {isTextarea ? (
              <textarea
                value={editValue}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setEditValue(e.target.value)}
                className="text-xs min-h-[60px] w-full p-2 border border-gray-300 rounded resize-none"
                placeholder={label}
              />
            ) : (
              <Input
                value={editValue === undefined || editValue === null ? '' : editValue}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditValue(e.target.value)}
                className="text-xs h-6"
              />
            )}
            <div className="flex items-center gap-1 mt-1">
              <Button size="sm" onClick={handleSave} className="h-6 w-6 p-0">
                <Save className="h-3 w-3" />
              </Button>
              <Button size="sm" onClick={handleCancel} className="h-6 w-6 p-0">
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-start justify-between">
            <span className="font-semibold text-black">{value === undefined || value === null ? '' : value}</span>
            {isEditing && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleEdit(section, field, value)}
                className="h-4 w-4 p-0 ml-1"
              >
                <Edit2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex gap-6">
      {/* Risks Section */}
      <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
        <div className="text-[#1a9c7c] font-semibold mb-4">Risks</div>
        {renderEditableField("Credit Rating", risks.creditRating, "risks", "creditRating")}
        {renderEditableField("Credit Rating Description", risks.creditRatingDescription, "risks", "creditRatingDescription", true)}
        {renderEditableField("Credit Outlook", risks.creditOutlook, "risks", "creditOutlook")}
        {renderEditableField("Credit Outlook Description", risks.creditOutlookDescription, "risks", "creditOutlookDescription", true)}
        {renderEditableField("Rating Watch", risks.ratingWatch, "risks", "ratingWatch")}
        {renderEditableField("Rating Watch Description", risks.ratingWatchDescription, "risks", "ratingWatchDescription", true)}
      </div>

      {/* Factors Section */}
      <div className="flex-1 bg-white rounded-2xl p-6 shadow-sm">
        <div className="text-[#1a9c7c] font-semibold mb-4">Factors</div>
        <div className="flex flex-col gap-2 text-xs text-gray-600">
          {Object.entries(factors).map(([key, value]) => (
            <div key={key} className="flex justify-between items-center">
              <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
              {isEditing ? (
                <div className="flex items-center gap-1">
                  <Input
                    value={value === undefined || value === null ? '' : value}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => onEdit?.('factors', key, e.target.value)}
                    className="text-xs h-6 w-64"
                  />
                </div>
              ) : (
                <span className="font-medium">{value === undefined || value === null ? '' : value}</span>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RisksAndFactors; 