import React from "react";

interface PerformanceMetric {
  label: string;
  value: string;
  change?: string;
  isPositive?: boolean;
}

interface BondPerformanceProps {
  title: string;
  metrics: PerformanceMetric[];
  showChart?: boolean;
}

const BondPerformance: React.FC<BondPerformanceProps> = ({
  title,
  metrics,
  showChart = false,
}) => {
  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div className="text-[#1a9c7c] font-semibold mb-4">{title}</div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <div key={index} className="text-center">
            <div className="text-xs text-gray-500 mb-1">{metric.label}</div>
            <div className="text-lg font-bold text-[#1a9c7c]">{metric.value}</div>
            {metric.change && (
              <div className={`text-xs ${metric.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                {metric.isPositive ? '+' : ''}{metric.change}
              </div>
            )}
          </div>
        ))}
      </div>

      {showChart && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600 text-center">
            Performance chart would be displayed here
          </div>
        </div>
      )}
    </div>
  );
};

export default BondPerformance; 