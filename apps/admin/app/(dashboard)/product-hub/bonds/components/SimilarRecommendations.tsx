import React from "react";
import { SimilarRecommendationsProps } from "../types";

const SimilarRecommendations: React.FC<SimilarRecommendationsProps> = ({
  recommendations,
  onSelect,
}) => {
  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div className="text-[#1a9c7c] font-semibold mb-4">Similar Recommendations</div>
      <div className="flex gap-4 overflow-x-auto">
        {recommendations.map((bond, index) => (
          <div
            key={bond._id || index}
            className="flex-1 min-w-[300px] border rounded-xl p-4 flex flex-col gap-2 bg-[#f6faf9] cursor-pointer hover:bg-[#e6f7f2] transition-colors"
            onClick={() => onSelect?.(bond)}
          >
            <div className="flex items-center gap-2 mb-2">
              <span className="text-base font-semibold text-[#1a9c7c]">{bond.bondName}</span>
              <span className="ml-auto bg-gray-100 rounded-full px-2 py-1 text-xs font-medium">
                {bond.basicBondInfo.yieldType}
              </span>
            </div>
            <div className="flex items-center gap-4 mb-2">
              <div>
                <div className="text-xs text-gray-500">Yield</div>
                <div className="text-lg font-bold text-[#1a9c7c]">{bond.basicBondInfo.yield}%</div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Credit Rating</div>
                <div className="text-sm font-semibold">{bond.basicBondInfo.creditRating}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Face Value</div>
                <div className="text-sm font-semibold">
                  {bond.basicBondInfo.currency} {bond.basicBondInfo.faceValue.toLocaleString()}
                </div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Payment Frequency</div>
                <div className="text-sm font-semibold">{bond.basicBondInfo.paymentFrequency}</div>
              </div>
            </div>
            <div>
              <div className="text-xs text-gray-500">Maturity Date</div>
              <div className="text-sm font-semibold">{bond.basicBondInfo.maturityDate}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SimilarRecommendations; 