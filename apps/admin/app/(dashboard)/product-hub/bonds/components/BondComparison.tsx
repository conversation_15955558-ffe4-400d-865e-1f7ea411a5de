import React from "react";

interface BondComparisonData {
  name: string;
  type: string;
  yield: string;
  creditRating: string;
  faceValue: string;
  maturityDate: string;
  couponRate: string;
  riskLevel: string;
}

interface BondComparisonProps {
  bonds: BondComparisonData[];
  title?: string;
}

const BondComparison: React.FC<BondComparisonProps> = ({
  bonds,
  title = "Bond Comparison",
}) => {
  const comparisonFields = [
    { key: "yield", label: "Yield", isHighlight: true },
    { key: "creditRating", label: "Credit Rating" },
    { key: "faceValue", label: "Face Value" },
    { key: "maturityDate", label: "Maturity Date" },
    { key: "couponRate", label: "Coupon Rate" },
    { key: "riskLevel", label: "Risk Level" },
  ];

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div className="text-[#1a9c7c] font-semibold mb-4">{title}</div>
      
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left font-medium text-gray-500 pb-2">Bond</th>
              {bonds.map((bond, index) => (
                <th key={index} className="text-left font-medium text-gray-500 pb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-[#1a9c7c]">{bond.name}</span>
                    <span className="bg-gray-100 rounded-full px-2 py-1 text-xs font-medium">
                      {bond.type}
                    </span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {comparisonFields.map((field) => (
              <tr key={field.key} className="border-b border-gray-100">
                <td className="py-3 text-gray-600 font-medium">{field.label}</td>
                {bonds.map((bond, index) => (
                  <td key={index} className="py-3">
                    <span className={field.isHighlight ? "text-lg font-bold text-[#1a9c7c]" : "font-semibold"}>
                      {bond[field.key as keyof BondComparisonData]}
                    </span>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BondComparison; 