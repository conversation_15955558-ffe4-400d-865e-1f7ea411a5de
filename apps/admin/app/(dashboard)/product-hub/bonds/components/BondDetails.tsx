import React, { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

interface DetailItem {
  label: string;
  value: string;
  description?: string;
}

interface BondDetailsProps {
  title: string;
  details: DetailItem[];
  isExpanded?: boolean;
}

const BondDetails: React.FC<BondDetailsProps> = ({
  title,
  details,
  isExpanded = false,
}) => {
  const [expanded, setExpanded] = useState(isExpanded);

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div 
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="text-[#1a9c7c] font-semibold">{title}</div>
        {expanded ? (
          <ChevronUp className="h-5 w-5 text-gray-500" />
        ) : (
          <ChevronDown className="h-5 w-5 text-gray-500" />
        )}
      </div>
      
      {expanded && (
        <div className="mt-4 space-y-3">
          {details.map((detail, index) => (
            <div key={index} className="border-b border-gray-100 pb-3 last:border-b-0">
              <div className="flex justify-between items-start">
                <div className="text-sm text-gray-600">{detail.label}</div>
                <div className="text-sm font-semibold text-right">{detail.value}</div>
              </div>
              {detail.description && (
                <div className="text-xs text-gray-500 mt-1">{detail.description}</div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BondDetails; 