import React, { useState } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Edit2, Save, X } from "lucide-react";
import { BondSummaryProps } from "../types";

const BondSummary: React.FC<BondSummaryProps> = ({ 
  bond, 
  onEdit, 
  isEditing = false 
}) => {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>("");

  const handleEdit = (field: string, currentValue: any) => {
    setEditingField(field);
    setEditValue(String(currentValue));
  };

  const handleSave = () => {
    if (editingField && onEdit) {
      onEdit(editingField, editValue);
    }
    setEditingField(null);
    setEditValue("");
  };

  const handleCancel = () => {
    setEditingField(null);
    setEditValue("");
  };

  const renderEditableField = (
    label: string,
    value: any,
    field: string,
    type: "text" | "number" = "text",
    width: string = "w-40"
  ) => {
    const isFieldEditing = editingField === field;
    
    return (
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="text-sm text-gray-600 mb-2">{label}</div>
        {isFieldEditing ? (
          <div className="flex items-center gap-2">
            <Input
              type={type}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className={"text-lg font-semibold h-10 " + width}
              placeholder={"Enter " + label}
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleSave} className="h-10 w-10 bg-green-600 hover:bg-green-700 text-white">
                <Save className="h-4 w-4" />
              </Button>
              <Button size="sm" onClick={handleCancel} className="h-10 w-10 bg-gray-200 hover:bg-gray-300 text-gray-700">
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <span className="text-lg font-semibold">
              {type === "number" && typeof value === "number" ? value.toLocaleString() : value}
            </span>
            {isEditing && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleEdit(field, value)}
                className="h-8 w-8 p-0 hover:bg-gray-200 rounded-full"
              >
                <Edit2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderFaceValueSection = () => {
    return (
      <div className="grid grid-cols-2 gap-4">
        {/* Currency Field */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="text-sm text-gray-600 mb-2">Currency</div>
          {editingField === "basicBondInfo.currency" ? (
            <div className="flex items-center gap-2">
              <Input
                type="text"
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="text-lg font-semibold h-10 w-24"
                placeholder="e.g. USD"
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={handleSave} className="h-10 w-10 bg-green-600 hover:bg-green-700 text-white">
                  <Save className="h-4 w-4" />
                </Button>
                <Button size="sm" onClick={handleCancel} className="h-10 w-10 bg-gray-200 hover:bg-gray-300 text-gray-700">
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold">
                {bond.basicBondInfo.currency}
              </span>
              {isEditing && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleEdit("basicBondInfo.currency", bond.basicBondInfo.currency)}
                  className="h-8 w-8 p-0 hover:bg-gray-200 rounded-full"
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Face Value Field */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="text-sm text-gray-600 mb-2">Face Value</div>
          {editingField === "basicBondInfo.faceValue" ? (
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="text-lg font-semibold h-10 w-full"
                placeholder="Enter Face Value"
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={handleSave} className="h-10 w-10 bg-green-600 hover:bg-green-700 text-white">
                  <Save className="h-4 w-4" />
                </Button>
                <Button size="sm" onClick={handleCancel} className="h-10 w-10 bg-gray-200 hover:bg-gray-300 text-gray-700">
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold">
                {bond.basicBondInfo.faceValue.toLocaleString()}
              </span>
              {isEditing && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleEdit("basicBondInfo.faceValue", bond.basicBondInfo.faceValue)}
                  className="h-8 w-8 p-0 hover:bg-gray-200 rounded-full"
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-2xl p-6 flex flex-col shadow-sm">
      {/* Bond Name and Yield Type */}
      <div className="flex items-center gap-4 mb-6 bg-gray-50 p-4 rounded-lg">
        <div className="flex-1">
          {editingField === "bondName" ? (
            <div className="flex items-center gap-2">
              <Input
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="text-lg font-semibold text-[#1a9c7c] flex-1 h-10"
                placeholder="Enter Bond Name"
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={handleSave} className="h-10 w-10 bg-green-600 hover:bg-green-700 text-white">
                  <Save className="h-4 w-4" />
                </Button>
                <Button size="sm" onClick={handleCancel} className="h-10 w-10 bg-gray-200 hover:bg-gray-300 text-gray-700">
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold text-[#1a9c7c]">{bond.bondName}</span>
              {isEditing && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleEdit("bondName", bond.bondName)}
                  className="h-8 w-8 p-0 hover:bg-gray-200 rounded-full"
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>

        <div>
          {editingField === "basicBondInfo.yieldType" ? (
            <div className="flex items-center gap-2">
              <Input
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="text-sm font-medium h-10 w-36"
                placeholder="Enter Yield Type"
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={handleSave} className="h-10 w-10 bg-green-600 hover:bg-green-700 text-white">
                  <Save className="h-4 w-4" />
                </Button>
                <Button size="sm" onClick={handleCancel} className="h-10 w-10 bg-gray-200 hover:bg-gray-300 text-gray-700">
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span className="bg-[#e6f7f2] text-[#1a9c7c] rounded-full px-4 py-2 text-sm font-medium">
                {bond.basicBondInfo.yieldType}
              </span>
              {isEditing && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleEdit("basicBondInfo.yieldType", bond.basicBondInfo.yieldType)}
                  className="h-8 w-8 p-0 hover:bg-gray-200 rounded-full"
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Basic Info Grid */}
      <div className="grid grid-cols-2 gap-6 mb-6">
        {renderEditableField("Yield", `${bond.basicBondInfo.yield}%`, "basicBondInfo.yield", "number", "w-32")}
        {renderEditableField("Credit Rating", bond.basicBondInfo.creditRating, "basicBondInfo.creditRating", "text", "w-24")}
      </div>

      {/* Additional Info - Single Column */}
      <div className="space-y-6 mb-6">
        {renderEditableField("Payment Frequency", bond.basicBondInfo.paymentFrequency, "basicBondInfo.paymentFrequency")}
        {renderEditableField("Maturity Date", bond.basicBondInfo.maturityDate, "basicBondInfo.maturityDate")}
        {renderFaceValueSection()}
      </div>
    </div>
  );
};

export default BondSummary;