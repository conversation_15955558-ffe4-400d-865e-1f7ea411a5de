"use client";
import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { Input } from "@admin/components/ui/input";
import { Button } from "@admin/components/ui/button";
import { Textarea } from "@admin/components/ui/textarea";
import { AboutSection } from "../data";

interface EquityAboutFormProps {
  aboutSection: AboutSection;
  onSave: (aboutSection: AboutSection) => void;
}

export default function EquityAboutForm({ aboutSection, onSave }: EquityAboutFormProps) {
  const [formData, setFormData] = useState<AboutSection>(aboutSection);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (field: keyof AboutSection, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCompanyDetailsChange = (field: keyof NonNullable<AboutSection['companyDetails']>, value: any) => {
    setFormData(prev => ({
      ...prev,
      companyDetails: {
        ...prev.companyDetails,
        [field]: value
      }
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>About Section</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">Description</label>
            <Textarea
              id="description"
              value={formData.description || ""}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleChange("description", e.target.value)}
              placeholder="Enter company description..."
              rows={4}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="showMoreUrl" className="text-sm font-medium">Show More URL</label>
            <Input
              id="showMoreUrl"
              value={formData.showMoreUrl || ""}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("showMoreUrl", e.target.value)}
              placeholder="https://example.com/more-info"
            />
          </div>
          
          <div className="border-t pt-4">
            <h4 className="font-semibold mb-4">Company Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="ceo" className="text-sm font-medium">CEO</label>
                <Input
                  id="ceo"
                  value={formData.companyDetails?.ceo || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleCompanyDetailsChange("ceo", e.target.value)}
                  placeholder="Andrew R. Jassy"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="employees" className="text-sm font-medium">Employees</label>
                <Input
                  id="employees"
                  value={formData.companyDetails?.employees || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleCompanyDetailsChange("employees", e.target.value)}
                  placeholder="1,556,000"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="employeesNumeric" className="text-sm font-medium">Employees (Numeric)</label>
                <Input
                  id="employeesNumeric"
                  type="number"
                  value={formData.companyDetails?.employeesNumeric || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleCompanyDetailsChange("employeesNumeric", parseInt(e.target.value) || 0)}
                  placeholder="1556000"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="headquarters" className="text-sm font-medium">Headquarters</label>
                <Input
                  id="headquarters"
                  value={formData.companyDetails?.headquarters || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleCompanyDetailsChange("headquarters", e.target.value)}
                  placeholder="Seattle, Washington"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="founded" className="text-sm font-medium">Founded</label>
                <Input
                  id="founded"
                  value={formData.companyDetails?.founded || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleCompanyDetailsChange("founded", e.target.value)}
                  placeholder="1994"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="foundedNumeric" className="text-sm font-medium">Founded (Numeric)</label>
                <Input
                  id="foundedNumeric"
                  type="number"
                  value={formData.companyDetails?.foundedNumeric || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleCompanyDetailsChange("foundedNumeric", parseInt(e.target.value) || 0)}
                  placeholder="1994"
                />
              </div>
            </div>
          </div>
          <Button type="submit" className="w-full">Save About Section</Button>
        </form>
      </CardContent>
    </Card>
  );
} 