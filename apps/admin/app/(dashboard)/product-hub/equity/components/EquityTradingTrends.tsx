import React from 'react';

const EquityTradingTrends = () => {
  return (
    <div className="bg-white rounded-xl shadow p-6">
      {/* TODO: Add trading trends chart and tabs */}
      <div className="flex items-center gap-2 mb-2">
        <span className="text-green-600 font-bold">Trading Trends</span>
        <div className="ml-4 flex gap-2 text-xs">
          <button className="px-2 py-1 rounded bg-green-100 text-green-700 font-semibold">Valura</button>
          <button className="px-2 py-1 rounded bg-gray-100">Hedge Funds</button>
          <button className="px-2 py-1 rounded bg-gray-100">Insiders</button>
        </div>
      </div>
      <div className="text-xs text-gray-500 mb-2">Jul 2: Our customers' buy volume percentage increased by 14% vs the last trading day. This is not investment advice.</div>
      <div className="h-32 bg-gray-100 rounded flex items-center justify-center">[Trading Trends Chart]</div>
    </div>
  );
};

export default EquityTradingTrends; 