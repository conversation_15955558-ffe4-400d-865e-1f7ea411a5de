import React from 'react';

const EquityPerformance = () => {
  return (
    <div className="bg-white rounded-xl shadow p-6">
      {/* TODO: Add performance chart and time range selector */}
      <div className="flex items-center gap-2 mb-2">
        <span className="text-green-600 font-bold">Performance</span>
        <div className="ml-auto flex gap-2 text-xs">
          <button className="px-2 py-1 rounded bg-gray-100">1D</button>
          <button className="px-2 py-1 rounded bg-green-100 text-green-700 font-semibold">1W</button>
          <button className="px-2 py-1 rounded bg-gray-100">1M</button>
          <button className="px-2 py-1 rounded bg-gray-100">YTD</button>
          <button className="px-2 py-1 rounded bg-gray-100">1Y</button>
          <button className="px-2 py-1 rounded bg-gray-100">5Y</button>
          <button className="px-2 py-1 rounded bg-gray-100">Max</button>
        </div>
      </div>
      <div className="h-32 bg-gray-100 rounded flex items-center justify-center">[Performance Chart]</div>
    </div>
  );
};

export default EquityPerformance; 