import React from 'react';

const EquitySummary = () => {
  return (
    <div className="bg-white rounded-xl shadow p-6 flex flex-col gap-2">
      {/* TODO: Add actual summary content (logo, name, ticker, price, chart, etc.) */}
      <div className="flex items-center justify-between">
        <div>
          <div className="text-lg font-bold">AMZN</div>
          <div className="text-gray-500">Amazon Inc</div>
        </div>
        <div className="bg-gray-100 rounded-full w-10 h-10 flex items-center justify-center">
          <span>Logo</span>
        </div>
      </div>
      <div className="text-green-600 font-semibold text-xl mt-2">+5.63%</div>
      <div className="text-gray-700 text-sm">Current Value</div>
      <div className="text-2xl font-bold">AUD 203.65</div>
      {/* Placeholder for mini chart */}
      <div className="h-12 bg-gray-100 rounded mt-2 flex items-center justify-center">[Chart]</div>
    </div>
  );
};

export default EquitySummary; 