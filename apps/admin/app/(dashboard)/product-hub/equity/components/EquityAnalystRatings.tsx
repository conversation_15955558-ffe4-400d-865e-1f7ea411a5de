import React from 'react';

const EquityAnalystRatings = () => {
  return (
    <div className="bg-white rounded-xl shadow p-6 h-full flex flex-col items-center justify-center">
      {/* TODO: Add analyst ratings donut chart and stats */}
      <div className="text-green-600 font-bold mb-2">Analyst Ratings</div>
      <div className="flex flex-col items-center">
        <div className="w-24 h-24 rounded-full bg-green-100 flex items-center justify-center text-2xl font-bold text-green-700 mb-2">97%</div>
        <div className="text-xs text-gray-500">of 71 ratings</div>
        <div className="flex gap-2 mt-2 text-xs">
          <span className="text-green-700">Buy 97.2%</span>
          <span className="text-yellow-600">Hold 2.8%</span>
          <span className="text-gray-400">Sell 0%</span>
        </div>
      </div>
    </div>
  );
};

export default EquityAnalystRatings; 