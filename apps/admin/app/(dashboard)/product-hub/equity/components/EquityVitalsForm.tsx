"use client";
import React, { useState } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@admin/components/ui/card";
import { Input } from "@admin/components/ui/input";
import { But<PERSON> } from "@admin/components/ui/button";
import { Vitals } from "../data";

interface EquityVitalsFormProps {
  vitals: Vitals;
  onSave: (vitals: Vitals) => void;
}

export default function EquityVitalsForm({ vitals, onSave }: EquityVitalsFormProps) {
  const [formData, setFormData] = useState<Vitals>(vitals);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (field: keyof Vitals, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Vitals</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="averageVolume" className="text-sm font-medium">Average Volume</label>
              <Input
                id="averageVolume"
                value={formData.averageVolume || ""}
                onChange={(e) => handleChange("averageVolume", e.target.value)}
                placeholder="52.71M"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="volume" className="text-sm font-medium">Volume</label>
              <Input
                id="volume"
                value={formData.volume || ""}
                onChange={(e) => handleChange("volume", e.target.value)}
                placeholder="39.26M"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="fiftyTwoWeekHigh" className="text-sm font-medium">52 Week High</label>
              <Input
                id="fiftyTwoWeekHigh"
                value={formData.fiftyTwoWeekHigh || ""}
                onChange={(e) => handleChange("fiftyTwoWeekHigh", e.target.value)}
                placeholder="242.52"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="fiftyTwoWeekLow" className="text-sm font-medium">52 Week Low</label>
              <Input
                id="fiftyTwoWeekLow"
                value={formData.fiftyTwoWeekLow || ""}
                onChange={(e) => handleChange("fiftyTwoWeekLow", e.target.value)}
                placeholder="151.61"
              />
            </div>
          </div>
          <Button type="submit" className="w-full">Save Vitals</Button>
        </form>
      </CardContent>
    </Card>
  );
} 