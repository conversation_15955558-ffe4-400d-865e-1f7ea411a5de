import React from 'react';

const SimilarRecommendations = () => {
  return (
    <div className="bg-white rounded-xl shadow p-6">
      {/* TODO: Add similar recommendations cards */}
      <div className="text-green-600 font-bold mb-4">Similar Recommendations</div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-green-50 rounded-lg p-4 flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <span className="font-bold">NVDA</span>
            <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded">Big Cap</span>
          </div>
          <div className="text-gray-700 font-semibold">Nvidia</div>
          <div className="text-green-600 font-bold">+5.63%</div>
          <div className="text-xs text-gray-500">Current Value</div>
          <div className="text-lg font-bold">AUD 203.65</div>
        </div>
        <div className="bg-green-50 rounded-lg p-4 flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <span className="font-bold">TSLA</span>
            <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded">Big Cap</span>
          </div>
          <div className="text-gray-700 font-semibold">Tesla Inc</div>
          <div className="text-green-600 font-bold">+5.63%</div>
          <div className="text-xs text-gray-500">Current Value</div>
          <div className="text-lg font-bold">AUD 203.65</div>
        </div>
      </div>
    </div>
  );
};

export default SimilarRecommendations; 