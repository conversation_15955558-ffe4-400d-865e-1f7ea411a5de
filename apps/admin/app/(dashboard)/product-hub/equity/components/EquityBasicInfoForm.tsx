"use client";
import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@admin/components/ui/card";
import { Input } from "@admin/components/ui/input";
import { Button } from "@admin/components/ui/button";
import { CustomSelect } from "@admin/components/ui/CustomSelect";
import { BasicInfo } from "../data";

interface EquityBasicInfoFormProps {
  basicInfo: BasicInfo;
  onSave: (basicInfo: BasicInfo) => void;
}

export default function EquityBasicInfoForm({ basicInfo, onSave }: EquityBasicInfoFormProps) {
  const [formData, setFormData] = useState<BasicInfo>(basicInfo);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (field: keyof BasicInfo, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Information</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="symbol" className="text-sm font-medium">Symbol *</label>
              <Input
                id="symbol"
                value={formData.symbol}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("symbol", e.target.value)}
                placeholder="e.g., AMZN"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="companyName" className="text-sm font-medium">Company Name</label>
              <Input
                id="companyName"
                value={formData.companyName || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("companyName", e.target.value)}
                placeholder="e.g., Amazon Inc"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="category" className="text-sm font-medium">Category</label>
              <Input
                id="category"
                value={formData.category || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("category", e.target.value)}
                placeholder="e.g., Big Cap"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="logoUrl" className="text-sm font-medium">Logo URL</label>
              <Input
                id="logoUrl"
                value={formData.logoUrl || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("logoUrl", e.target.value)}
                placeholder="https://example.com/logo.png"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="price" className="text-sm font-medium">Current Price</label>
              <Input
                id="price"
                type="number"
                step="0.01"
                value={formData.price || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("price", parseFloat(e.target.value) || 0)}
                placeholder="203.65"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Currency</label>
              <CustomSelect
                options={["USD", "AUD", "EUR", "GBP"]}
                value={formData.currency || ""}
                onChange={(value: string) => handleChange("currency", value)}
                placeholder="Select currency"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="priceChangeNumeric" className="text-sm font-medium">Price Change (%)</label>
              <Input
                id="priceChangeNumeric"
                type="number"
                step="0.01"
                value={formData.priceChangeNumeric || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("priceChangeNumeric", parseFloat(e.target.value) || 0)}
                placeholder="5.63"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Price Direction</label>
              <CustomSelect
                options={["up", "down", "neutral"]}
                value={formData.priceDirection || ""}
                onChange={(value: string) => handleChange("priceDirection", value)}
                placeholder="Select direction"
              />
            </div>
          </div>
          <Button type="submit" className="w-full">Save Basic Information</Button>
        </form>
      </CardContent>
    </Card>
  );
} 