"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@admin/components/ui/card";
import { EquityProduct } from "../data";

interface EquityPreviewProps {
  equity: EquityProduct;
}

export default function EquityPreview({ equity }: EquityPreviewProps) {
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Equity Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {equity.basicInfo.symbol || "Symbol"}
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Company Name:</span>
                  <span className="font-medium">{equity.basicInfo.companyName || "Not specified"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Category:</span>
                  <span className="font-medium">{equity.basicInfo.category || "Not specified"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Current Price:</span>
                  <span className="font-medium">
                    {equity.basicInfo.price ? `${equity.basicInfo.currency} ${equity.basicInfo.price}` : "Not specified"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Price Change:</span>
                  <span className={`font-medium ${
                    equity.basicInfo.priceDirection === "up" ? "text-green-600" : 
                    equity.basicInfo.priceDirection === "down" ? "text-red-600" : "text-gray-600"
                  }`}>
                    {equity.basicInfo.priceChangeNumeric ? `${equity.basicInfo.priceChangeNumeric}%` : "Not specified"}
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Essentials</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Market Cap:</span>
                  <span className="font-medium">
                    {equity.essentials.marketCap ? `$${(equity.essentials.marketCap / 1e12).toFixed(2)}T` : "Not specified"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">P/E Ratio:</span>
                  <span className="font-medium">{equity.essentials.priceEarningsRatio || "Not specified"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Dividend Yield:</span>
                  <span className="font-medium text-green-600">
                    {equity.essentials.dividendYield ? `${equity.essentials.dividendYield}%` : "Not specified"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Open Price:</span>
                  <span className="font-medium">{equity.essentials.openPrice || "Not specified"}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vitals */}
      <Card>
        <CardHeader>
          <CardTitle>Vitals</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Average Volume:</span>
              <span className="font-medium">{equity.vitals.averageVolume || "Not specified"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Volume:</span>
              <span className="font-medium">{equity.vitals.volume || "Not specified"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">52 Week High:</span>
              <span className="font-medium">{equity.vitals.fiftyTwoWeekHigh || "Not specified"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">52 Week Low:</span>
              <span className="font-medium">{equity.vitals.fiftyTwoWeekLow || "Not specified"}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* About Section */}
      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Description</h4>
              <p className="text-gray-700">{equity.aboutSection.description || "No description available"}</p>
            </div>
            {equity.aboutSection.companyDetails && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-gray-600">CEO:</span>
                  <p className="font-medium">{equity.aboutSection.companyDetails.ceo || "Not specified"}</p>
                </div>
                <div>
                  <span className="text-gray-600">Employees:</span>
                  <p className="font-medium">{equity.aboutSection.companyDetails.employees || "Not specified"}</p>
                </div>
                <div>
                  <span className="text-gray-600">Headquarters:</span>
                  <p className="font-medium">{equity.aboutSection.companyDetails.headquarters || "Not specified"}</p>
                </div>
                <div>
                  <span className="text-gray-600">Founded:</span>
                  <p className="font-medium">{equity.aboutSection.companyDetails.founded || "Not specified"}</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Analyst Ratings */}
      <Card>
        <CardHeader>
          <CardTitle>Analyst Ratings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Ratings:</span>
              <span className="font-medium">{equity.analystRatings.totalRatings || "Not specified"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Rating Score:</span>
              <span className="font-medium">{equity.analystRatings.ratingScore || "Not specified"}</span>
            </div>
            {equity.analystRatings.ratingBreakdown && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-600">Buy:</span>
                  <span className="font-medium text-green-600">{equity.analystRatings.ratingBreakdown.Buy_cnt || "0"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Hold:</span>
                  <span className="font-medium text-yellow-600">{equity.analystRatings.ratingBreakdown.Hold_cnt || "0"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Sell:</span>
                  <span className="font-medium text-red-600">{equity.analystRatings.ratingBreakdown.Sell_cnt || "0"}</span>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Earnings */}
      <Card>
        <CardHeader>
          <CardTitle>Earnings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Next Earnings Date:</span>
                <span className="font-medium">
                  {equity.earnings.nextEarningsDate ? 
                    new Date(equity.earnings.nextEarningsDate).toLocaleDateString() : "Not specified"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Estimated Earnings:</span>
                <span className="font-medium">{equity.earnings.estimatedEarnings || "Not specified"}</span>
              </div>
            </div>
            {equity.earnings.quarterlyEarnings && equity.earnings.quarterlyEarnings.length > 0 && (
              <div>
                <h4 className="font-semibold mb-2">Quarterly Earnings</h4>
                <div className="space-y-2">
                  {equity.earnings.quarterlyEarnings.map((quarter, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-3">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                        <div><span className="text-gray-600">Quarter:</span> {quarter.quarter || "Not specified"}</div>
                        <div><span className="text-gray-600">Estimated:</span> {quarter.estimated || "Not specified"}</div>
                        <div><span className="text-gray-600">Actual:</span> {quarter.actual || "Not specified"}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 