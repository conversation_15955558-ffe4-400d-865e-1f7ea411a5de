"use client";
import React, { useState } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@admin/components/ui/card";
import { Input } from "@admin/components/ui/input";
import { Button } from "@admin/components/ui/button";
import { Essentials } from "../data";

interface EquityEssentialsFormProps {
  essentials: Essentials;
  onSave: (essentials: Essentials) => void;
}

export default function EquityEssentialsForm({ essentials, onSave }: EquityEssentialsFormProps) {
  const [formData, setFormData] = useState<Essentials>(essentials);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (field: keyof Essentials, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Essentials</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="marketCap" className="text-sm font-medium">Market Cap (in billions)</label>
              <Input
                id="marketCap"
                type="number"
                step="0.01"
                value={formData.marketCap || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("marketCap", parseFloat(e.target.value) || 0)}
                placeholder="2340"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="priceEarningsRatio" className="text-sm font-medium">Price-Earnings Ratio</label>
              <Input
                id="priceEarningsRatio"
                value={formData.priceEarningsRatio || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("priceEarningsRatio", e.target.value)}
                placeholder="35.78"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="dividendYield" className="text-sm font-medium">Dividend Yield (%)</label>
              <Input
                id="dividendYield"
                type="number"
                step="0.01"
                value={formData.dividendYield || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("dividendYield", parseFloat(e.target.value) || null)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="openPrice" className="text-sm font-medium">Open Price</label>
              <Input
                id="openPrice"
                value={formData.openPrice || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("openPrice", e.target.value)}
                placeholder="219.65"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="highToday" className="text-sm font-medium">High Today</label>
              <Input
                id="highToday"
                value={formData.highToday || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("highToday", e.target.value)}
                placeholder="221.88"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="lowToday" className="text-sm font-medium">Low Today</label>
              <Input
                id="lowToday"
                value={formData.lowToday || ""}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange("lowToday", e.target.value)}
                placeholder="220.26"
              />
            </div>
          </div>
          <Button type="submit" className="w-full">Save Essentials</Button>
        </form>
      </CardContent>
    </Card>
  );
} 