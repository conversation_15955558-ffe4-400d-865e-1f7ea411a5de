import React from 'react';

const EquityEssentials = () => {
  return (
    <div className="bg-white rounded-xl shadow p-4 flex flex-col gap-2">
      {/* TODO: Add essentials data (Market Cap, P/E, Open, High, Low, etc.) */}
      <div className="font-semibold text-green-700">Essentials:</div>
      <div className="flex flex-col gap-1 text-sm">
        <div className="flex justify-between"><span>Market Cap:</span><span>2.3T</span></div>
        <div className="flex justify-between"><span>Price-Earnings ratio:</span><span>35.78</span></div>
        <div className="flex justify-between"><span>Open price:</span><span>213.65</span></div>
        <div className="flex justify-between"><span>High today:</span><span>221.88</span></div>
        <div className="flex justify-between"><span>Low today:</span><span>220.26</span></div>
      </div>
    </div>
  );
};

export default EquityEssentials; 