"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from "@admin/components/ui/button";
import { ArrowLeft, Download, MessageCircle, Save, Eye, Send } from "lucide-react";
import { sampleEquity, EquityProduct } from "./data";
import EquityPreview from "./components/EquityPreview";
import EquityBasicInfoForm from "./components/EquityBasicInfoForm";
import EquityEssentialsForm from "./components/EquityEssentialsForm";
import EquityVitalsForm from "./components/EquityVitalsForm";
import EquityAboutForm from "./components/EquityAboutForm";
import axios from 'axios';
import { createEquity } from '@admin/app/lib/productApiService';

export default function EquityDetailPage() {
  const searchParams = useSearchParams();
  const [mode, setMode] = useState<'form' | 'review'>('form');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [equity, setEquity] = useState<EquityProduct>(sampleEquity);
  const [kdUrl, setKdUrl] = useState("");
  const [showKdInput, setShowKdInput] = useState(false);
  const [contactNumber, setContactNumber] = useState("");
  const [showContactInput, setShowContactInput] = useState(false);
  const [vendorId, setVendorId] = useState<string>("");
  const router = useRouter();

  useEffect(() => {
    const vendorParam = searchParams.get('vendor');
    if (vendorParam) {
      setVendorId(vendorParam);
      setEquity(prev => ({
        ...prev,
        vendor_id: vendorParam
      }));
    }
  }, [searchParams]);

  const handleBasicInfoSave = (basicInfo: EquityProduct['basicInfo']) => {
    setEquity(prev => ({ ...prev, basicInfo }));
  };

  const handleEssentialsSave = (essentials: EquityProduct['essentials']) => {
    setEquity(prev => ({ ...prev, essentials }));
  };

  const handleVitalsSave = (vitals: EquityProduct['vitals']) => {
    setEquity(prev => ({ ...prev, vitals }));
  };

  const handleAboutSave = (aboutSection: EquityProduct['aboutSection']) => {
    setEquity(prev => ({ ...prev, aboutSection }));
  };

  const validateForm = () => {
    const errors = [];
    
    // Check required fields from basicInfo
    if (!equity.basicInfo?.symbol) errors.push("Symbol is required");
    if (!equity.basicInfo?.companyName) errors.push("Company Name is required");
    if (!equity.basicInfo?.price) errors.push("Current Price is required");
    if (!equity.basicInfo?.currency) errors.push("Currency is required");
    
    // Check required fields from essentials
    if (!equity.essentials?.marketCap) errors.push("Market Cap is required");
    if (!equity.essentials?.priceEarningsRatio) errors.push("Price-Earnings Ratio is required");
    
    // Check required fields from vitals
    if (!equity.vitals?.averageVolume) errors.push("Average Volume is required");
    if (!equity.vitals?.volume) errors.push("Volume is required");
    
    // Check required fields from about
    if (!equity.aboutSection?.description) errors.push("Company Description is required");
    if (!equity.aboutSection?.companyDetails?.ceo) errors.push("CEO is required");
    if (!equity.aboutSection?.companyDetails?.headquarters) errors.push("Headquarters is required");
    
    return errors;
  };

  const getFormProgress = () => {
    const errors = validateForm();
    const totalFields = 10; // Approximate number of required fields
    const filledFields = totalFields - errors.length;
    return Math.max(0, Math.min(100, (filledFields / totalFields) * 100));
  };

  const handleReview = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    setMode('review');
  };

  const handleSubmit = async () => {
    console.log('Starting submission...');
    setIsSubmitting(true);
    try {
      if (!vendorId) {
        throw new Error('Vendor ID is required');
      }

      console.log('Preparing data for submission...');
      const equityDataToSave = {
        vendor_id: vendorId,
        basicInfo: {
          ...equity.basicInfo,
          price: parseFloat(equity.basicInfo.price?.toString() || '0'),
          priceChangeNumeric: parseFloat(equity.basicInfo.priceChangeNumeric?.toString() || '0'),
        },
        essentials: {
          ...equity.essentials,
          marketCap: parseFloat(equity.essentials.marketCap?.toString() || '0'),
          dividendYield: parseFloat(equity.essentials.dividendYield?.toString() || '0'),
        },
        vitals: equity.vitals,
        aboutSection: {
          ...equity.aboutSection,
          companyDetails: {
            ...equity.aboutSection?.companyDetails,
            employeesNumeric: parseInt(equity.aboutSection?.companyDetails?.employeesNumeric?.toString() || '0'),
            foundedNumeric: parseInt(equity.aboutSection?.companyDetails?.foundedNumeric?.toString() || '0'),
          }
        },
        analystRatings: {
          totalRatings: parseInt(equity?.analystRatings?.totalRatings?.toString() || '0'),
          ratingBreakdown: {
            Buy_cnt: equity?.analystRatings?.ratingBreakdown?.Buy_cnt || '0',
            Sell_cnt: equity?.analystRatings?.ratingBreakdown?.Sell_cnt || '0',
            Hold_cnt: equity?.analystRatings?.ratingBreakdown?.Hold_cnt || '0'
          },
          ratingScore: equity?.analystRatings?.ratingScore || ''
        },
        earnings: {
          nextEarningsDate: equity?.earnings?.nextEarningsDate,
          estimatedEarnings: equity?.earnings?.estimatedEarnings,
          quarterlyEarnings: equity?.earnings?.quarterlyEarnings?.map(earning => ({
            quarter: earning.quarter,
            estimated: parseFloat(earning.estimated?.toString() || '0'),
            actual: earning.actual ? parseFloat(earning.actual.toString()) : null,
            type: earning.type
          })) || []
        }
      };

      console.log('Sending data to API:', equityDataToSave);
      const response: any = await createEquity(equityDataToSave);
      console.log('API Response:', response);

      if (response && response.statusCode === 201) {
        alert('Equity created successfully!');
        setMode('form');
        router.push('/product-hub/');
      } else if (response && response.id) {
        // fallback: if API returns the created object
        alert('Equity created successfully!');
        setMode('form');
        router.push('/product-hub/');
      } else {
        throw new Error('Failed to create equity');
      }
    } catch (error) {
      console.error('Error saving Equity:', error);
      let message = 'Failed to save Equity. Please try again.';
      if (error && typeof error === 'object' && 'message' in error && typeof (error as any).message === 'string') {
        message = (error as any).message;
      }
      alert(message);
    } finally {
      console.log('Submission completed');
      setIsSubmitting(false);
    }
  };

  const handleSubmitWithValidation = async () => {
    console.log('Starting validation...');
    const errors = validateForm();
    if (errors.length > 0) {
      alert(`Please fix the following errors:\n\n${errors.join('\n')}`);
      return;
    }
    if (!vendorId) {
      alert('Vendor ID is required. Please ensure you have accessed this page with a valid vendor parameter.');
      return;
    }
    console.log('Validation passed, proceeding with submission...');
    await handleSubmit();
  };

  const handleKdUrlSave = () => {
    if (kdUrl.trim()) {
      setShowKdInput(false);
      console.log('KD URL saved:', kdUrl);
      alert('KD URL saved successfully!');
    } else {
      alert('Please enter a valid KD URL');
    }
  };

  const handleKdUrlCancel = () => {
    setKdUrl("");
    setShowKdInput(false);
  };

  const handleContactSave = () => {
    if (contactNumber.trim()) {
      setShowContactInput(false);
      console.log('Contact number saved:', contactNumber);
      alert('Contact number saved successfully!');
    } else {
      alert('Please enter a valid contact number');
    }
  };

  const handleContactCancel = () => {
    setContactNumber("");
    setShowContactInput(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {mode === 'form' ? 'Edit Equity Product' : 'Review Equity Product'}
              </h1>
              <p className="text-sm text-gray-600">
                {equity.basicInfo.symbol || 'New Equity Product'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {mode === 'form' && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowKdInput(true)}
                  className="flex items-center space-x-2"
                >
                  <Download className="h-4 w-4" />
                  <span>KD URL</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowContactInput(true)}
                  className="flex items-center space-x-2"
                >
                  <MessageCircle className="h-4 w-4" />
                  <span>Contact</span>
                </Button>
                <Button
                  onClick={handleReview}
                  className="flex items-center space-x-2"
                >
                  <Eye className="h-4 w-4" />
                  <span>Review</span>
                </Button>
              </>
            )}
            
            {mode === 'review' && (
              <>
                <Button
                  variant="outline"
                  onClick={() => setMode('form')}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Edit</span>
                </Button>
                <Button
                  onClick={() => {
                    console.log('Save button clicked');
                    handleSubmitWithValidation();
                  }}
                  disabled={isSubmitting}
                  className="flex items-center space-x-2"
                >
                  <Send className="h-4 w-4" />
                  <span>{isSubmitting ? 'Saving...' : 'Save & Publish'}</span>
                </Button>
              </>
            )}
          </div>
        </div>
        
        {/* Progress Bar */}
        {mode === 'form' && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
              <span>Form Progress</span>
              <span>{Math.round(getFormProgress())}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getFormProgress()}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-6">
        {mode === 'form' ? (
          <div className="space-y-6">
            <EquityBasicInfoForm
              basicInfo={equity.basicInfo}
              onSave={handleBasicInfoSave}
            />
            <EquityEssentialsForm
              essentials={equity.essentials}
              onSave={handleEssentialsSave}
            />
            <EquityVitalsForm
              vitals={equity.vitals}
              onSave={handleVitalsSave}
            />
            <EquityAboutForm
              aboutSection={equity.aboutSection}
              onSave={handleAboutSave}
            />
          </div>
        ) : (
          <EquityPreview equity={equity} />
        )}
      </div>

      {/* KD URL Modal */}
      {showKdInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Enter KD URL</h3>
            <input
              type="text"
              value={kdUrl}
              onChange={(e) => setKdUrl(e.target.value)}
              placeholder="Enter KD URL"
              className="w-full p-2 border border-gray-300 rounded mb-4"
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleKdUrlCancel}>
                Cancel
              </Button>
              <Button onClick={handleKdUrlSave}>
                Save
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Contact Number Modal */}
      {showContactInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Enter Contact Number</h3>
            <input
              type="text"
              value={contactNumber}
              onChange={(e) => setContactNumber(e.target.value)}
              placeholder="Enter contact number"
              className="w-full p-2 border border-gray-300 rounded mb-4"
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleContactCancel}>
                Cancel
              </Button>
              <Button onClick={handleContactSave}>
                Save
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}