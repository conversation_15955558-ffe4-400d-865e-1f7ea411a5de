// Equity Product Types based on MongoDB Schema
export interface BasicInfo {
  symbol: string;
  companyName?: string;
  category?: string;
  logoUrl?: string;
  price?: number;
  currency?: string;
  priceChangeNumeric?: number;
  priceDirection?: "up" | "down" | "neutral";
}

export interface Essentials {
  marketCap?: number;
  priceEarningsRatio?: string;
  dividendYield?: number | null;
  openPrice?: string;
  highToday?: string;
  lowToday?: string;
}

export interface Vitals {
  averageVolume?: string;
  volume?: string;
  fiftyTwoWeekHigh?: string;
  fiftyTwoWeekLow?: string;
}

export interface CompanyDetails {
  ceo?: string;
  employees?: string;
  employeesNumeric?: number;
  headquarters?: string;
  founded?: string;
  foundedNumeric?: number;
}

export interface AboutSection {
  description?: string;
  showMoreUrl?: string;
  companyDetails?: CompanyDetails;
}

export interface RatingBreakdown {
  Buy_cnt?: string;
  Sell_cnt?: string;
  Hold_cnt?: string;
}

export interface AnalystRatings {
  totalRatings?: number;
  ratingBreakdown?: RatingBreakdown;
  ratingScore?: string;
}

export interface QuarterlyEarnings {
  quarter?: string;
  estimated?: number;
  actual?: number | null;
  type?: "historical" | "upcoming";
}

export interface Earnings {
  nextEarningsDate?: Date;
  estimatedEarnings?: string;
  quarterlyEarnings?: QuarterlyEarnings[];
}

export interface EquityProduct {
  productId: string;
  basicInfo: BasicInfo;
  essentials: Essentials;
  vitals: Vitals;
  aboutSection: AboutSection;
  analystRatings: AnalystRatings;
  earnings: Earnings;
}

// Sample data with empty/default values
export const sampleEquity: EquityProduct = {
  productId: "",
  basicInfo: {
    symbol: "",
    companyName: "",
    category: "",
    logoUrl: "",
    price: 0,
    currency: "USD",
    priceChangeNumeric: 0,
    priceDirection: "neutral",
  },
  essentials: {
    marketCap: 0,
    priceEarningsRatio: "",
    dividendYield: null,
    openPrice: "",
    highToday: "",
    lowToday: "",
  },
  vitals: {
    averageVolume: "",
    volume: "",
    fiftyTwoWeekHigh: "",
    fiftyTwoWeekLow: "",
  },
  aboutSection: {
    description: "",
    showMoreUrl: "",
    companyDetails: {
      ceo: "",
      employees: "",
      employeesNumeric: 0,
      headquarters: "",
      founded: "",
      foundedNumeric: 0,
    },
  },
  analystRatings: {
    totalRatings: 0,
    ratingBreakdown: {
      Buy_cnt: "",
      Sell_cnt: "",
      Hold_cnt: "",
    },
    ratingScore: "",
  },
  earnings: {
    nextEarningsDate: new Date(),
    estimatedEarnings: "",
    quarterlyEarnings: [],
  },
}; 