"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import {
  PlusIcon,
  CheckCircle,
  HelpCircle,
  Monitor,
  Zap,
  Clock,
  Loader2,
} from "lucide-react";
import { TechnicalSupportSummaryCard } from "./components/TechnicalSupportSummaryCard";
import { TechnicalSupportTable } from "./components/TechnicalSupportTable";
import { TechnicalSupportFilters } from "./components/TechnicalSupportFilters";
import { IssueCategory } from "./components/IssueCategory";
import { supportTicketService, SupportTicket } from "@admin/app/lib/supportTicketService";
import { TechnicalSupportData } from "./types";
import { getTimeAgo } from "@admin/app/lib/utils";

const iconClass = "h-6 w-6 text-red-500";

export const TechnicalSupport = () => {
  const [search, setSearch] = useState("");
  const [category, setCategory] = useState("All Categories");
  const [priority, setPriority] = useState("All Priorities");
  const [status, setStatus] = useState("All Status");
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch tickets on component mount
  useEffect(() => {
    fetchTickets();
  }, []);

  // Listen for ticket assignment updates
  useEffect(() => {
    const handleTicketAssignmentUpdate = (event: CustomEvent) => {
      // Consider using a logging service if needed
      fetchTickets();
    };

    // Listen for custom event
    window.addEventListener('ticketAssignmentUpdated', handleTicketAssignmentUpdate as EventListener);

    return () => {
      window.removeEventListener('ticketAssignmentUpdated', handleTicketAssignmentUpdate as EventListener);
    };
  }, []);

  const fetchTickets = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await supportTicketService.getAllTickets({
        limit: 100, // Get more tickets for technical support view
      });
      setTickets(response.tickets);
    } catch (err: any) {
      // Log error to monitoring service if needed
      setError(err.response?.data?.message || 'Failed to fetch tickets');
    } finally {
      setLoading(false);
    }
  };

  // Transform API tickets to TechnicalSupportData format
  const transformTicketsToTechnicalSupportData = (tickets: SupportTicket[]): TechnicalSupportData[] => {
    return tickets
      .filter(ticket => ticket.type === 'TECHNICAL_ISSUE') // Only show technical issues
      .map((ticket) => {
        // Determine icon based on category
        const getIcon = (category: string) => {
          switch (category.toLowerCase()) {
            case 'ui/ux issues':
            case 'dashboard':
              return <Monitor className={iconClass} />;
            case 'performance issues':
              return <Clock className={iconClass} />;
            case 'real-time data':
            case 'payments':
              return <Zap className={iconClass} />;
            default:
              return <HelpCircle className={iconClass} />;
          }
        };

        return {
          title: ticket.title,
          id: ticket.ticket_number,
          reporter: `${ticket.user?.first_name || 'Unknown'} ${ticket.user?.last_name || 'User'}`,
          category: ticket.category,
          priority: ticket.priority.charAt(0) + ticket.priority.slice(1).toLowerCase(),
          status: ticket.status.charAt(0) + ticket.status.slice(1).toLowerCase(),
          assignee: ticket.assignee_team || 'Unassigned',
          activity: getTimeAgo(ticket.updated_at),
          comments: 0, // This would need to be added to the API if needed
          attachments: ticket.support_ticket_docs?.length || 0,
          icon: getIcon(ticket.category),
        };
      });
  };

  const technicalSupportData = transformTicketsToTechnicalSupportData(tickets);

  const filtered = technicalSupportData.filter((t) => {
    const matchSearch =
      !search || t.title.toLowerCase().includes(search.toLowerCase());
    const matchCategory =
      category === "All Categories" || t.category === category;
    const matchPriority =
      priority === "All Priorities" || t.priority === priority;
    const matchStatus = status === "All Status" || t.status === status;
    return matchSearch && matchCategory && matchPriority && matchStatus;
  });

  // Generate summary data from filtered data (not all tickets)
  const technicalSupportSummary = [
    {
      label: "Total Issues",
      value: technicalSupportData.length.toString(),
      icon: "AlertCircle",
      color: "bg-blue-100 text-blue-600",
    },
    {
      label: "Open",
      value: technicalSupportData.filter(t => t.status === 'Open').length.toString(),
      icon: "AlertCircle",
      color: "bg-blue-100 text-blue-600",
    },
    {
      label: "In Progress",
      value: technicalSupportData.filter(t => t.status === 'In Progress').length.toString(),
      icon: "Clock",
      color: "bg-yellow-100 text-yellow-600",
    },
    {
      label: "Critical",
      value: technicalSupportData.filter(t => t.priority === 'Critical').length.toString(),
      icon: "AlertTriangle",
      color: "bg-red-100 text-red-600",
    },
    {
      label: "Resolved",
      value: technicalSupportData.filter(t => t.status === 'Resolved').length.toString(),
      icon: "CheckCircle",
      color: "bg-[#EDF8F2] text-[#05A049]",
    },
  ];

  // Generate categories from filtered data
  const technicalCategories = Array.from(new Set(technicalSupportData.map(t => t.category)));
  const technicalPriorities = ["All Priorities", "Critical", "High", "Medium", "Low"];
  const technicalStatuses = ["All Status", "Open", "In Progress", "Escalated", "Resolved"];



  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading technical support data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchTickets} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto">
      <div className="flex justify-between items-center px-6 py-4 ">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Technical Support
          </h1>
          <p className="text-gray-600 text-sm">
            Issues reported by users from the main website platform
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="rounded-[8px]">
            Export List
          </Button>
        </div>
      </div>
      <div className="p-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          {technicalSupportSummary.map((item, i) => (
            <TechnicalSupportSummaryCard key={i} item={item} />
          ))}
        </div>

        <IssueCategory />

        {/* Filters */}
        <TechnicalSupportFilters
          search={search}
          setSearch={setSearch}
          category={category}
          setCategory={setCategory}
          priority={priority}
          setPriority={setPriority}
          status={status}
          setStatus={setStatus}
          categories={technicalCategories}
          priorities={technicalPriorities}
          statuses={technicalStatuses}
          filteredCount={filtered.length}
        />

        {/* Technical Support Table */}
        <div className="mt-6 bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">User-Reported Issues</h2>
            <p className="text-gray-600 text-sm">Issues reported by users from the main website platform</p>
          </div>
          <TechnicalSupportTable 
            issues={filtered} 
            originalTickets={tickets} 
          />
        </div>

        {/* User Issue Resolution Guidelines */}
        <div className="bg-[#F2FFED] rounded-xl p-6 mt-8 mb-2 shadow-sm border border-[#d1f5d1]">
          <h3 className="font-bold text-[#05A049] mb-6 flex items-center gap-2 text-lg">
            <CheckCircle className="w-6 h-6 text-[#05A049]" />
            User Issue Resolution Guidelines
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-3">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[#05A049] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#20AA5D] text-sm leading-relaxed">
                  Prioritize security and login issues immediately
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[#05A049] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#20AA5D] text-sm leading-relaxed">
                  Reproduce issues in staging environment first
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[#05A049] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#20AA5D] text-sm leading-relaxed">
                  Contact users for additional information if needed
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[#05A049] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#20AA5D] text-sm leading-relaxed">
                  Document solutions for knowledge base
                </span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[#05A049] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#20AA5D] text-sm leading-relaxed">
                  Escalate critical issues to senior developers
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[#05A049] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#20AA5D] text-sm leading-relaxed">
                  Test fixes thoroughly before deployment
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[#05A049] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#20AA5D] text-sm leading-relaxed">
                  Update ticket status promptly
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[#05A049] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#20AA5D] text-sm leading-relaxed">
                  Follow up with users after resolution
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};