import { AlertCircle, HelpCircle, Monitor, Zap, Clock } from 'lucide-react';
import { ReactNode } from 'react';

const iconClass = "h-6 w-6 text-red-500";

export const getTechnicalSupportIcon = (category: string): ReactNode => {
  switch (category) {
    case 'Payments':
      return <AlertCircle className={iconClass} />;
    case 'Authentication':
      return <HelpCircle className={iconClass} />;
    case 'Database':
      return <Monitor className={iconClass} />;
    case 'Performance':
      return <Zap className={iconClass} />;
    default:
      return <Clock className={iconClass} />;
  }
};
