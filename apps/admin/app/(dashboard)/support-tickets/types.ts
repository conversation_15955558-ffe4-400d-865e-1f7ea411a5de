import { LucideIcon } from "lucide-react";
import { ReactNode } from "react";

export interface SummaryData {
  title: string;
  value: string;
  change: string;
  icon: string;
  color: string;
  bgColor: string;
}

export interface SupportTypeData {
  title: string;
  description: string;
  issuesCount: string;
  icon: string;
  iconBg: string;
  route?: string;
}

export interface TypeCategory {
  type: string;
  category: string;
}

export interface Ticket {
  id: string;
  ticket_number?: string;
  details: string;
  typeCategory: TypeCategory;
  priority: "Critical" | "High" | "Medium" | "Low";
  status: "Open" | "In Progress" | "Escalated" | "Resolved";
  assignee: string;
  created: string;
  description?: string;
  email?: string;
  support_ticket_docs?: Array<{
    image_id: string;
    image_url: string;
    uploaded_at: string;
  }>;
}

export interface TechnicalSupportSummary {
  label: string;
  value: string;
  icon: string;
  color: string;
}

export interface TechnicalSupportData {
  title: string;
  id: string;
  reporter: string;
  category: string;
  priority: string;
  status: string;
  assignee: string;
  activity: string;
  comments: number;
  attachments: number;
  icon: ReactNode;
}

export interface TeamWorkload {
  team: string;
  activeTickets: number;
  avgResolution: string;
  efficiency: string;
  efficiencyColor: string;
}

export interface BugSummary {
  label: string;
  value: number;
  color: string;
  icon: string;
}

export interface BugData {
  title: string;
  id: string;
  category: string;
  severity: "Critical" | "High" | "Medium" | "Low";
  status: "Open" | "In Progress" | "Resolved";
  assignee: string;
  activity: string;
  comments: number;
  attachments: number;
}

export interface ProductSummary {
  label: string;
  value: number;
  color: string;
  icon: string;
}

export interface ProductData {
  title: string;
  id: string;
  category: string;
  severity: "Critical" | "High" | "Medium" | "Low";
  status: "Open" | "In Progress" | "Resolved";
  assignee: string;
  activity: string;
  comments: number;
  attachments: number;
  description: string;
  impact: string;
  impactSeverity: "Critical" | "High" | "Medium" | "Low";
  user?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  support_ticket_docs?: Array<{
    image_id: string;
    image_url: string;
    uploaded_at: string;
  }>;
}

export interface IssueCategory {
  title: string;
  description: string;
  total: number;
  open: number;
  icon: string;
}
