import { HelpCircle, Monitor, Zap, Clock } from 'lucide-react';
import { ReactNode } from 'react';

const iconClass = "h-6 w-6 text-red-500";

export const getUserReportedIcon = (category: string): ReactNode => {
  switch (category) {
    case 'UI/UX Issues':
      return <Monitor className={iconClass} />;
    case 'Performance Issues':
      return <Clock className={iconClass} />;
    case 'Real-time Data':
      return <Zap className={iconClass} />;
    case 'Others':
    default:
      return <HelpCircle className={iconClass} />;
  }
};
