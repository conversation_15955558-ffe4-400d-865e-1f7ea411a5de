"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { PlusIcon, CheckCircleIcon, Loader2 } from "lucide-react";
import { ProductSummaryCard } from "./components/ProductSummaryCard";
import { ProductTable } from "./components/ProductTable";
import { ProductFilters } from "./components/ProductFilters";
import { supportTicketService, SupportTicket } from "@admin/app/lib/supportTicketService";
import { ProductData } from "./types";
import { getTimeAgo } from "@admin/app/lib/utils";

export const ProductSupport = () => {
  const [search, setSearch] = useState("");
  const [category, setCategory] = useState("All Categories");
  const [severity, setSeverity] = useState("All Severities");
  const [status, setStatus] = useState("All Status");
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch tickets on component mount
  useEffect(() => {
    fetchTickets();
  }, []);

  // Listen for ticket assignment updates
  useEffect(() => {
    const handleTicketAssignmentUpdate = (event: CustomEvent) => {
      console.log('ProductSupport: Ticket assignment updated', event.detail);
      fetchTickets();
    };

    // Listen for custom event
    window.addEventListener('ticketAssignmentUpdated', handleTicketAssignmentUpdate as EventListener);

    return () => {
      window.removeEventListener('ticketAssignmentUpdated', handleTicketAssignmentUpdate as EventListener);
    };
  }, []);

  const fetchTickets = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await supportTicketService.getAllTickets({
        limit: 100, // Get more tickets for product support view
      });
      setTickets(response.tickets);
    } catch (err: any) {
      console.error('Error fetching tickets:', err);
      setError(err.response?.data?.message || 'Failed to fetch tickets');
    } finally {
      setLoading(false);
    }
  };

  // Transform API tickets to ProductData format (focusing on bug reports)
  const transformTicketsToProductData = (tickets: SupportTicket[]): ProductData[] => {
    return tickets
      .filter(ticket => ticket.type === 'BUG_REPORT') // Only show bug reports in product support
      .map((ticket) => {


        return {
          title: ticket.title,
          id: ticket.ticket_number,
          category: ticket.category,
          severity: (() => {
            switch (ticket.priority.toUpperCase()) {
              case 'CRITICAL': return 'Critical' as const;
              case 'HIGH': return 'High' as const;
              case 'MEDIUM': return 'Medium' as const;
              case 'LOW': return 'Low' as const;
              default: return 'Medium' as const;
            }
          })(),
          status: (() => {
            switch (ticket.status.toUpperCase()) {
              case 'OPEN': return 'Open' as const;
              case 'IN_PROGRESS': return 'In Progress' as const;
              case 'RESOLVED': return 'Resolved' as const;
              default: return 'Open' as const;
            }
          })(),
          assignee: ticket.assignee_team || 'Unassigned',
          activity: getTimeAgo(ticket.updated_at),
          comments: 0, // This would need to be added to the API if needed
          attachments: ticket.support_ticket_docs?.length || 0,
          description: ticket.description || 'No description provided',
          impact: 'User Experience', // Default impact
          impactSeverity: (() => {
            switch (ticket.priority.toUpperCase()) {
              case 'CRITICAL': return 'Critical' as const;
              case 'HIGH': return 'High' as const;
              case 'MEDIUM': return 'Medium' as const;
              case 'LOW': return 'Low' as const;
              default: return 'Medium' as const;
            }
          })(),
          user: ticket.user,
          support_ticket_docs: ticket.support_ticket_docs,
        };
      });
  };
  
  const productData = transformTicketsToProductData(tickets);

  const filtered = productData.filter((p) => {
    const matchSearch =
      !search || p.title.toLowerCase().includes(search.toLowerCase());
    const matchCategory =
      category === "All Categories" || p.category === category;
    const matchSeverity =
      severity === "All Severities" || p.severity === severity;
    const matchStatus = status === "All Status" || p.status === status;
    return matchSearch && matchCategory && matchSeverity && matchStatus;
  });

  // Generate summary data from real tickets
  const productSummary = [
    {
      label: "Total Bugs",
      value: productData.length,
      color: "bg-blue-100 text-blue-600",
      icon: "Bug",
    },
    {
      label: "Open",
      value: productData.filter(p => p.status === 'Open').length,
      color: "bg-blue-100 text-blue-600",
      icon: "AlertCircle",
    },
    {
      label: "In Progress",
      value: productData.filter(p => p.status === 'In Progress').length,
      color: "bg-yellow-100 text-yellow-600",
      icon: "Clock",
    },
    {
      label: "Critical",
      value: productData.filter(p => p.severity === 'Critical').length,
      color: "bg-red-100 text-red-600",
      icon: "AlertTriangle",
    },
    {
      label: "Resolved",
      value: productData.filter(p => p.status === 'Resolved').length,
      color: "bg-[#EDF8F2] text-[#05A049]",
      icon: "CheckCircle",
    },
  ];

  // Generate categories from real data
  const categories = Array.from(new Set(productData.map(p => p.category)));
  const severities = ["All Severities", "Critical", "High", "Medium", "Low"];
  const statuses = ["All Status", "Open", "In Progress", "Resolved"];

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading product support data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchTickets} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto">
      <div className="flex justify-between items-center px-6 py-4 ">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">All Product Reports</h1>
          <p className="text-gray-600 text-sm">
            Comprehensive list of all reported product issues and concerns
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="rounded-[8px]">
            Export List
          </Button>
        </div>
      </div>
      <div className="p-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          {productSummary.map((item, i) => (
            <ProductSummaryCard key={i} item={item} />
          ))}
        </div>

        {/* Filters */}
        <ProductFilters
          search={search}
          setSearch={setSearch}
          category={category}
          setCategory={setCategory}
          severity={severity}
          setSeverity={setSeverity}
          status={status}
          setStatus={setStatus}
          categories={categories}
          severities={severities}
          statuses={statuses}
          filteredCount={filtered.length}
        />

        {/* Product Table */}
        <ProductTable products={filtered} />

        {/* Product Issue Reporting Guidelines */}
        <div className="bg-[#F2FFED] rounded-xl p-6 mt-8 mb-2 shadow-sm border border-[#d1f5d1]">
          <h3 className="font-bold text-[#05A049] mb-4 flex items-center gap-2">
            <span className="inline-block">
              {/* Wrench icon using Lucide or fallback emoji */}
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-[#05A049]" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a5.5 5.5 0 01-7.78 7.78l-4.95 4.95a2.121 2.121 0 003 3l4.95-4.95a5.5 5.5 0 017.78-7.78z" /></svg>
            </span>
            Product Issue Reporting Guidelines
          </h3>
          <ul className="text-[#20AA5D] text-sm grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-1 pl-0">
            <li>• Include specific product names and versions</li>
            <li>• Provide pricing or calculation examples</li>
            <li>• Attach relevant screenshots or documents</li>
            <li>• Specify customer impact and urgency</li>
            <li>• Check for existing similar issues</li>
            <li>• Include steps to reproduce the problem</li>
            <li>• Mention affected client accounts (if applicable)</li>
            <li>• Provide expected vs actual behavior</li>
          </ul>
        </div>
      </div>
    </div>
  );
}; 