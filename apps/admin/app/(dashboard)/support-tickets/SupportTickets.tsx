"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { BugIcon, Loader2 } from "lucide-react";
import { SummaryCards } from "./components/SummaryCards";
import { SupportTypeCards } from "./components/SupportTypeCards";
import { TicketsTable } from "./components/TicketsTable";
import { TeamWorkloadCards } from "./components/TeamWorkloadCards";
import { ReportIssueModal } from "./components/ReportIssueModal";
import { supportTicketService, SupportTicket } from "@admin/app/lib/supportTicketService";
import { Card } from "@admin/components/ui/card";
import { useAuth } from "@admin/app/lib/AuthContext";

// Add custom CSS for select elements
const selectStyles = {
  appearance: "none" as const,
  backgroundImage: "none",
  paddingRight: "2.5rem",
};

export const SupportTickets = (): JSX.Element => {
  const [searchTerm, setSearchTerm] = useState("");
  const [priorityFilter, setPriorityFilter] = useState("All Priorities");
  const [statusFilter, setStatusFilter] = useState("All Status");
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [showReportModal, setShowReportModal] = useState(false);
  const [stats, setStats] = useState({
    totalTickets: 0,
    openTickets: 0,
    criticalTickets: 0,
    resolvedTickets: 0,
    avgResolutionTime: "0h",
  });
  const { error: authError, clearError } = useAuth();

  // Fetch tickets and stats on component mount
  useEffect(() => {
    fetchTickets();
    fetchStats();
  }, []);

  const fetchTickets = async () => {
    try {
      setLoading(true);
      const response = await supportTicketService.getAllTickets({
        limit: 50, // Get more tickets for better overview
      });
      setTickets(response.tickets);
    } catch (err: any) {
      console.error('Error fetching tickets:', err);
      // Use AuthContext error for toast notification
      if (err?.response?.data?.message) {
        // @ts-ignore: setError is not exported, so use clearError + set local error if needed
        setError({ message: err.response.data.message, type: "error" });
      } else {
        // @ts-ignore
        setError({ message: "Failed to fetch tickets.", type: "error" });
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const ticketStats = await supportTicketService.getTicketStats();
      setStats(ticketStats);
    } catch (err: any) {
      console.error('Error fetching stats:', err);
      // Don't set error for stats, just log it
    }
  };

  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearchTerm =
      searchTerm === "" ||
      ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.ticket_number.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesPriority =
      priorityFilter === "All Priorities" || ticket.priority === priorityFilter.toUpperCase();
    
    const matchesStatus =
      statusFilter === "All Status" || ticket.status === statusFilter.toUpperCase();
    
    return matchesSearchTerm && matchesPriority && matchesStatus;
  });

  // Transform API data to match the expected format for components
  const summaryData = [
    {
      title: "Total Tickets",
      value: stats.totalTickets.toString(),
      change: "+12 today",
      icon: "FileTextIcon",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Product Reports",
      value: tickets.filter(t => t.type === 'BUG_REPORT').length.toString(),
      change: "+5 today",
      icon: "BugIcon",
      color: "text-[#05A049]",
      bgColor: "bg-[#EDF8F2]",
    },
    {
      title: "Technical Issues",
      value: tickets.filter(t => t.type === 'TECHNICAL_ISSUE').length.toString(),
      change: "+3 today",
      icon: "AlertTriangleIcon",
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      title: "Avg Resolution Time",
      value: stats.avgResolutionTime,
      change: "-0.8h improved",
      icon: "ClockIcon",
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
  ];

  const supportTypeData = [
    {
      title: "Product Support",
      description: "Report platform product issues, UI issues, and logic errors",
      issuesCount: `${tickets.filter(t => t.type === 'BUG_REPORT').length} Open Issues`,
      icon: "BugIcon",
      iconBg: "bg-orange-100",
      route: "/support-tickets/product-support",
    },
    {
      title: "Technical Support",
      description: "Critical operational issues affecting user funds and transactions",
      issuesCount: `${tickets.filter(t => t.type === 'TECHNICAL_ISSUE').length} Open Issues`,
      icon: "AlertTriangleIcon",
      iconBg: "bg-red-100",
      route: "/support-tickets/technical-support",
    },
  ];

  // Transform tickets to match the expected format
  const transformedTickets = filteredTickets.map((ticket) => ({
    id: ticket.ticket_id,
    ticket_number: ticket.ticket_number,
    details: ticket.title,
    typeCategory: { 
      type: ticket.type === 'BUG_REPORT' ? 'Product Support' : 
            ticket.type === 'TECHNICAL_ISSUE' ? 'Technical Support' : 
            'Other Support', 
      category: ticket.category 
    },
    priority: (() => {
      switch (ticket.priority.toUpperCase()) {
        case 'CRITICAL': return 'Critical' as const;
        case 'HIGH': return 'High' as const;
        case 'MEDIUM': return 'Medium' as const;
        case 'LOW': return 'Low' as const;
        default: return 'Medium' as const;
      }
    })(),
    status: (() => {
      switch (ticket.status.toUpperCase()) {
        case 'OPEN': return 'Open' as const;
        case 'IN_PROGRESS': return 'In Progress' as const;
        case 'ESCALATED': return 'Escalated' as const;
        case 'RESOLVED': return 'Resolved' as const;
        default: return 'Open' as const;
      }
    })(),
    assignee: ticket.assignee_team || 'Unassigned',
    created: new Date(ticket.created_at).toLocaleString(),
    description: ticket.description,
    email: ticket.user?.email,
    support_ticket_docs: ticket.support_ticket_docs,
  }));

  const teamWorkloadData = [
    {
      team: "DevOps Team",
      activeTickets: tickets.filter(t => t.assignee_team === 'DevOps Team').length,
      avgResolution: "3.2h",
      efficiency: "94%",
      efficiencyColor: "text-[#05A049]",
    },
    {
      team: "Frontend Team",
      activeTickets: tickets.filter(t => t.assignee_team === 'Frontend Team').length,
      avgResolution: "5.1h",
      efficiency: "87%",
      efficiencyColor: "text-yellow-600",
    },
    {
      team: "Backend Team",
      activeTickets: tickets.filter(t => t.assignee_team === 'Backend Team').length,
      avgResolution: "4.8h",
      efficiency: "91%",
      efficiencyColor: "text-[#05A049]",
    },
    {
      team: "Security Team",
      activeTickets: tickets.filter(t => t.assignee_team === 'Security Team').length,
      avgResolution: "2.9h",
      efficiency: "96%",
      efficiencyColor: "text-[#05A049]",
    },
  ];

  const handleDeleteTicket = async (ticketId: string) => {
    try {
      await supportTicketService.deleteTicket(ticketId);
      setTickets((prev) => prev.filter((t) => t.ticket_number !== ticketId));
    } catch (err: any) {
      // Use AuthContext error for toast notification
      if (err?.response?.data?.message) {
        // @ts-ignore: setError is not exported, so use clearError + set local error if needed
        setError({ message: err.response.data.message, type: "error" });
      } else {
        // @ts-ignore
        setError({ message: "Failed to delete ticket.", type: "error" });
      }
      // Optionally, keep console.error for debugging
      console.error("Failed to delete ticket:", err);
    }
  };

  const handleTicketCreated = () => {
    fetchTickets();
    fetchStats();
  };

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading support tickets...</span>
        </div>
      </div>
    );
  }

  if (authError) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{authError.message}</p>
          <Button onClick={fetchTickets} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto">
      {/* Header */}
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Support Tickets
            </h1>
            <p className="text-gray-600 text-sm">
              Centralized technical and operational issue tracking
            </p>
          </div>
          <Button 
            className="bg-[#EA580C] hover:bg-[#C2410C] rounded-[8px] text-white flex items-center gap-2"
            onClick={() => setShowReportModal(true)}
          >
            <BugIcon className="w-4 h-4" />
            Report Issue
          </Button>
        </div>
      </div>

      <div className="p-6">
        {/* Summary Cards */}
        <SummaryCards data={summaryData} />

        {/* Bug Support & Technical Support Cards */}
        <SupportTypeCards data={supportTypeData} />

        {/* Search and Filters */}
        <div className="">
          <Card className="mb-6 p-6 bg-white">
            <div className="flex gap-4 mb-6">
              <div className="relative flex-1">
                <Input
                  placeholder="Search tickets..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 border border-gray-200 p-5 rounded-[10px] relative"
                />
              </div>
              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
                style={selectStyles}
              >
                <option>All Priorities</option>
                <option>Critical</option>
                <option>High</option>
                <option>Medium</option>
                <option>Low</option>
              </select>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
                style={selectStyles}
              >
                <option>All Status</option>
                <option>Open</option>
                <option>In Progress</option>
                <option>Escalated</option>
                <option>Resolved</option>
              </select>
              <span className="text-sm text-gray-500 flex items-center">
                {filteredTickets.length} tickets found
              </span>
            </div>
          </Card>
        </div>

        {/* Recent Support Tickets Table */}
        <TicketsTable 
          tickets={transformedTickets} 
          onDelete={handleDeleteTicket} 
          onTicketUpdate={fetchTickets}
        />

        {/* Team Workload Overview */}
        <TeamWorkloadCards data={teamWorkloadData} />
      </div>

      {/* Report Issue Modal */}
      <ReportIssueModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        onTicketCreated={handleTicketCreated}
      />
    </div>
  );
};
