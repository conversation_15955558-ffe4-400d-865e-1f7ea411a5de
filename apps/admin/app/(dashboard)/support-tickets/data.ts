import {
  SummaryData,
  SupportTypeData,
  Ticket,
  TeamWorkload,
  BugSummary,
  BugData,
  ProductSummary,
  ProductData,
  TechnicalSupportSummary,
  TechnicalSupportData,
  IssueCategory,
} from "./types";
import { ReactNode } from "react";
import { AlertCircle } from "lucide-react";
import { getTechnicalSupportIcon } from "./TechnicalSupportIcon";

const iconClass = "h-6 w-6 text-red-500";

export const summaryData: SummaryData[] = [
  {
    title: "Total Tickets",
    value: "247",
    change: "+12 today",
    icon: "FileTextIcon",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
  {
    title: "Product Reports",
    value: "89",
    change: "+5 today",
    icon: "BugIcon",
    color: "text-[#05A049]",
    bgColor: "bg-[#EDF8F2]",
  },
  {
    title: "Critical Issues",
    value: "12",
    change: "-2 resolved",
    icon: "AlertTriangleIcon",
    color: "text-red-600",
    bgColor: "bg-red-50",
  },
  {
    title: "Avg Resolution Time",
    value: "4.2h",
    change: "-0.8h improved",
    icon: "ClockIcon",
    color: "text-purple-600",
    bgColor: "bg-purple-50",
  },
];

export const supportTypeData: SupportTypeData[] = [
  {
    title: "Product Support",
    description: "Report platform product issues, UI issues, and logic errors",
    issuesCount: "89 Open Issues",
    icon: "BugIcon",
    iconBg: "bg-orange-100",
    route: "/support-tickets/product-support",
  },
  {
    title: "Technical Support",
    description:
      "Critical operational issues affecting user funds and transactions",
    issuesCount: "12 Critical Issues",
    icon: "AlertTriangleIcon",
    iconBg: "bg-red-100",
    route: "/support-tickets/technical-support",
  },
];

export const recentTicketsData: Ticket[] = [
  {
    id: "SUP-001",
    details: "Payment gateway timeout errors",
    typeCategory: { type: "Technical Support", category: "Payments" },
    priority: "Critical",
    status: "In Progress",
    assignee: "DevOps Team",
    created: "2024-01-15 14:30",
  },
  {
    id: "SUP-002",
    details: "UI rendering issue on mobile dashboard",
    typeCategory: { type: "Product Support", category: "UI/UX" },
    priority: "Medium",
    status: "Open",
    assignee: "Frontend Team",
    created: "2024-01-15 13:45",
  },
  {
    id: "SUP-003",
    details: "Authentication service intermittent failures",
    typeCategory: { type: "Technical Support", category: "Auth" },
    priority: "High",
    status: "Escalated",
    assignee: "Security Team",
    created: "2024-01-15 12:20",
  },
  {
    id: "SUP-004",
    details: "Reconciliation report generation error",
    typeCategory: { type: "Product Support", category: "Reconciliation" },
    priority: "Low",
    status: "Resolved",
    assignee: "Backend Team",
    created: "2024-01-15 10:15",
  },
];

export const teamWorkloadData: TeamWorkload[] = [
  {
    team: "DevOps Team",
    activeTickets: 15,
    avgResolution: "3.2h",
    efficiency: "94%",
    efficiencyColor: "text-[#05A049]",
  },
  {
    team: "Frontend Team",
    activeTickets: 8,
    avgResolution: "5.1h",
    efficiency: "87%",
    efficiencyColor: "text-yellow-600",
  },
  {
    team: "Backend Team",
    activeTickets: 12,
    avgResolution: "4.8h",
    efficiency: "91%",
    efficiencyColor: "text-[#05A049]",
  },
  {
    team: "Security Team",
    activeTickets: 6,
    avgResolution: "2.9h",
    efficiency: "96%",
    efficiencyColor: "text-[#05A049]",
  },
];

export const bugSummary: BugSummary[] = [
  {
    label: "Total Bugs",
    value: 8,
    color: "bg-blue-100 text-blue-600",
    icon: "Bug",
  },
  {
    label: "Open",
    value: 3,
    color: "bg-blue-100 text-blue-600",
    icon: "AlertCircle",
  },
  {
    label: "In Progress",
    value: 3,
    color: "bg-yellow-100 text-yellow-600",
    icon: "Clock",
  },
  {
    label: "Critical",
    value: 1,
    color: "bg-red-100 text-red-600",
    icon: "AlertTriangle",
  },
  {
    label: "Resolved",
    value: 2,
    color: "bg-[#EDF8F2] text-[#05A049]",
    icon: "CheckCircle",
  },
];

export const bugData: BugData[] = [
  {
    title: "Dashboard charts not loading on Safari",
    id: "BUG-001",
    category: "UI/UX Issues",
    severity: "Medium",
    status: "In Progress",
    assignee: "Frontend Team",
    activity: "2 hours ago",
    comments: 3,
    attachments: 2,
  },
  {
    title: "Customer export CSV contains corrupted data",
    id: "BUG-002",
    category: "Report Generation",
    severity: "High",
    status: "Open",
    assignee: "Backend Team",
    activity: "3 hours ago",
    comments: 5,
    attachments: 1,
  },
  {
    title: "Mobile menu not responsive on tablets",
    id: "BUG-003",
    category: "Mobile Responsiveness",
    severity: "Low",
    status: "Resolved",
    assignee: "Frontend Team",
    activity: "1 day ago",
    comments: 2,
    attachments: 1,
  },
  {
    title: "Payment processing timeout errors",
    id: "BUG-004",
    category: "Payment Processing",
    severity: "Critical",
    status: "In Progress",
    assignee: "DevOps Team",
    activity: "30 minutes ago",
    comments: 8,
    attachments: 4,
  },
  {
    title: "API rate limiting not working correctly",
    id: "BUG-005",
    category: "API Errors",
    severity: "Medium",
    status: "Open",
    assignee: "Backend Team",
    activity: "1 day ago",
    comments: 1,
    attachments: 0,
  },
  {
    title: "Database connection pool exhaustion",
    id: "BUG-006",
    category: "Database Issues",
    severity: "High",
    status: "Resolved",
    assignee: "DevOps Team",
    activity: "2 days ago",
    comments: 4,
    attachments: 1,
  },
  {
    title: "User session expires prematurely",
    id: "BUG-007",
    category: "Authentication Problems",
    severity: "Medium",
    status: "Open",
    assignee: "Security Team",
    activity: "2 days ago",
    comments: 4,
    attachments: 1,
  },
  {
    title: "Performance degradation on large datasets",
    id: "BUG-008",
    category: "Performance Issues",
    severity: "High",
    status: "In Progress",
    assignee: "Backend Team",
    activity: "3 days ago",
    comments: 2,
    attachments: 0,
  },
];

export const productSummary: ProductSummary[] = [
  {
    label: "Total Products",
    value: 8,
    color: "bg-blue-100 text-blue-600",
    icon: "Bug",
  },
  {
    label: "Open",
    value: 3,
    color: "bg-blue-100 text-blue-600",
    icon: "AlertCircle",
  },
  {
    label: "In Progress",
    value: 3,
    color: "bg-yellow-100 text-yellow-600",
    icon: "Clock",
  },
  {
    label: "Critical",
    value: 1,
    color: "bg-red-100 text-red-600",
    icon: "AlertTriangle",
  },
  {
    label: "Resolved",
    value: 2,
    color: "bg-[#EDF8F2] text-[#05A049]",
    icon: "CheckCircle",
  },
];

export const productData: ProductData[] = [
  {
    title: "KYC documents rejected due to poor image quality",
    id: "PROD-001",
    category: "KYC Rejection",
    severity: "High",
    status: "In Progress",
    assignee: "Compliance Team",
    activity: "2 hours ago",
    comments: 5,
    attachments: 3,
    description:
      "Client KYC documents were rejected due to blurry passport images and unclear address proof.",
    impact: "blocking account activation",
    impactSeverity: "High",
  },
  {
    title: "Automated KYC verification failing for certain document types",
    id: "PROD-002",
    category: "KYC Rejection",
    severity: "Medium",
    status: "Open",
    assignee: "Compliance Team",
    activity: "3 hours ago",
    comments: 3,
    attachments: 2,
    description:
      "System not recognizing newer passport formats from certain countries.",
    impact: "affecting new customer onboarding",
    impactSeverity: "Medium",
  },
  {
    title: "KYC renewal process unclear for existing clients",
    id: "PROD-003",
    category: "KYC Rejection",
    severity: "Low",
    status: "Resolved",
    assignee: "Customer Support",
    activity: "1 day ago",
    comments: 2,
    attachments: 1,
    description:
      "Clients confused about KYC renewal requirements and documentation needed.",
    impact: "process clarification needed",
    impactSeverity: "Low",
  },
  {
    title: "Investment allocation not reflecting client risk profile",
    id: "PROD-004",
    category: "Investment Issue",
    severity: "Critical",
    status: "In Progress",
    assignee: "Investment Team",
    activity: "30 minutes ago",
    comments: 8,
    attachments: 5,
    description:
      "Conservative risk profile clients being shown aggressive investment options.",
    impact: "potential regulatory compliance issue",
    impactSeverity: "Critical",
  },
  {
    title: "Minimum investment amounts not clearly displayed",
    id: "PROD-005",
    category: "Investment Issue",
    severity: "Medium",
    status: "Open",
    assignee: "Product Team",
    activity: "1 day ago",
    comments: 4,
    attachments: 2,
    description:
      "Clients attempting to invest below minimum thresholds without clear warnings.",
    impact: "causing transaction failures",
    impactSeverity: "Medium",
  },
];

export const categories = [
  "All Categories",
  ...Array.from(new Set(productData.map((p) => p.category))),
];

export const severities = [
  "All Severities",
  "Critical",
  "High",
  "Medium",
  "Low",
];
export const statuses = ["All Status", "Open", "In Progress", "Resolved"];

export const technicalSupportSummary: TechnicalSupportSummary[] = [
  {
    label: "Total Technical Issues",
    value: "12",
    icon: "AlertTriangle",
    color: "bg-blue-100 text-blue-600",
  },
  {
    label: "Open",
    value: "3",
    color: "bg-blue-100 text-blue-600",
    icon: "AlertCircle",
  },
  {
    label: "In Progress",
    value: "4",
    color: "bg-yellow-100 text-yellow-600",
    icon: "Clock",
  },
  {
    label: "Critical",
    value: "3",
    color: "bg-red-100 text-red-600",
    icon: "AlertTriangle",
  },
  {
    label: "Resolved",
    value: "2",
    color: "bg-[#EDF8F2] text-[#05A049]",
    icon: "CheckCircle",
  },
];

export const technicalSupportData: TechnicalSupportData[] = [
  {
    title: "Payment gateway timeout errors affecting transactions",
    id: "TECH-001",
    reporter: "System Monitor",
    category: "Payments",
    priority: "Critical",
    status: "In Progress",
    assignee: "DevOps Team",
    activity: "30 minutes ago",
    comments: 5,
    attachments: 2,
    icon: getTechnicalSupportIcon("Payments"),
  },
  {
    title: "Authentication service intermittent failures",
    id: "TECH-002",
    reporter: "Security Monitor",
    category: "Authentication",
    priority: "High",
    status: "Escalated",
    assignee: "Security Team",
    activity: "1 hour ago",
    comments: 5,
    attachments: 2,
    icon: getTechnicalSupportIcon("Authentication"),
  },
  {
    title: "Database connection pool exhaustion",
    id: "TECH-003",
    reporter: "Database Monitor",
    category: "Database",
    priority: "Critical",
    status: "Open",
    assignee: "Backend Team",
    activity: "2 hours ago",
    comments: 3,
    attachments: 1,
    icon: getTechnicalSupportIcon("Database"),
  },
  {
    title: "API rate limiting not working correctly",
    id: "TECH-004",
    reporter: "API Gateway",
    category: "API",
    priority: "Medium",
    status: "In Progress",
    assignee: "Backend Team",
    activity: "3 hours ago",
    comments: 2,
    attachments: 1,
    icon: getTechnicalSupportIcon("API"),
  },
  {
    title: "SSL certificate expiration warning",
    id: "TECH-005",
    reporter: "Security Scanner",
    category: "Security",
    priority: "High",
    status: "Resolved",
    assignee: "DevOps Team",
    activity: "1 day ago",
    comments: 4,
    attachments: 2,
    icon: getTechnicalSupportIcon("Security"),
  },
  {
    title: "Load balancer health check failures",
    id: "TECH-006",
    reporter: "Infrastructure Monitor",
    category: "Infrastructure",
    priority: "Medium",
    status: "Open",
    assignee: "DevOps Team",
    activity: "1 day ago",
    comments: 2,
    attachments: 1,
    icon: getTechnicalSupportIcon("Infrastructure"),
  },
  {
    title: "Redis cache connection issues",
    id: "TECH-007",
    reporter: "Cache Monitor",
    category: "Cache",
    priority: "High",
    status: "In Progress",
    assignee: "Backend Team",
    activity: "2 days ago",
    comments: 3,
    attachments: 2,
    icon: getTechnicalSupportIcon("Cache"),
  },
  {
    title: "CDN edge server performance degradation",
    id: "TECH-008",
    reporter: "CDN Monitor",
    category: "Performance",
    priority: "Medium",
    status: "Resolved",
    assignee: "DevOps Team",
    activity: "3 days ago",
    comments: 5,
    attachments: 3,
    icon: getTechnicalSupportIcon("Performance"),
  },
];

export const technicalCategories = [
  "All Categories",
  "Payments",
  "Auth",
  "API",
  "Database",
];
export const technicalPriorities = [
  "All Priorities",
  "Critical",
  "High",
  "Medium",
  "Low",
];
export const technicalStatuses = [
  "All Status",
  "Open",
  "In Progress",
  "Escalated",
  "Resolved",
];

export const issueCategoriesData: IssueCategory[] = [
  {
    title: "UI/UX Issues",
    description: "Interface problems, layout issues, usability concerns",
    total: 12,
    open: 4,
    icon: "Computer",
  },
  {
    title: "Login Issues",
    description: "Authentication failures, password resets, account access",
    total: 15,
    open: 3,
    icon: "Lock",
  },
  {
    title: "Real-time Data",
    description: "Data feed issues, price updates, market data delays",
    total: 10,
    open: 3,
    icon: "Zap",
  },
  {
    title: "Performance Issues",
    description: "Slow loading, timeouts, system responsiveness",
    total: 8,
    open: 2,
    icon: "Clock",
  },
  {
    title: "Security Issues",
    description: "Security vulnerabilities, suspicious activity, data breaches",
    total: 11,
    open: 1,
    icon: "Shield",
  },
  {
    title: "Others",
    description: "General issues not covered by other categories",
    total: 19,
    open: 4,
    icon: "HelpCircle",
  },
];
