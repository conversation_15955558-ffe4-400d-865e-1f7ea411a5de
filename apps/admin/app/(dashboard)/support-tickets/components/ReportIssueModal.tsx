"use client";
import React, { useState, useEffect, useRef } from "react";
import { Button } from "@admin/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@admin/components/ui/dialog";
import { StepIndicator } from "./ReportIssueSteps/StepIndicator";
import { StepOne } from "./ReportIssueSteps/StepOne";
import { StepTwo } from "./ReportIssueSteps/StepTwo";
import { StepThree } from "./ReportIssueSteps/StepThree";
import { StepFour } from "./ReportIssueSteps/StepFour";
import { StepFive } from "./ReportIssueSteps/StepFive";
import { NavigationButtons } from "./ReportIssueSteps/NavigationButtons";
import { supportTicketService } from "@admin/app/lib/supportTicketService";
import { useAuth } from "@admin/app/lib/AuthContext";
import { Loader2, X } from "lucide-react";

interface ReportIssueModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTicketCreated?: () => void;
}

export const ReportIssueModal: React.FC<ReportIssueModalProps> = ({
  isOpen,
  onClose,
  onTicketCreated
}) => {
  const { user } = useAuth();
  const [step, setStep] = useState<number>(1);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string>("");
  
  // Form data
  const [message, setMessage] = useState<string>("");
  const [category, setCategory] = useState<string>("");
  const [selectedIssue, setSelectedIssue] = useState<string>("");
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleNext = async () => {
    if (step === 1) {
      if (!message.trim()) {
        setSubmitError("Please provide a description of the issue");
        return;
      }
      setSubmitError("");
      setStep(2);
    } else if (step === 2) {
      if (!category) {
        setSubmitError("Please select a bug type");
        return;
      }
      setSubmitError("");
      setStep(3);
    } else if (step === 3) {
      if (!selectedIssue) {
        setSubmitError("Please select a specific issue");
        return;
      }
      setSubmitError("");
      setStep(4);
    } else if (step === 4) {
      await submitTicket();
    } else {
      onClose();
      resetForm();
    }
  };

  const submitTicket = async () => {
    if (!user?.user_id) {
      setSubmitError("User authentication required. Please log in again.");
      return;
    }

    setIsSubmitting(true);
    setSubmitError("");

    try {
      const formData = new FormData();
      formData.append("user_id", user.user_id);
      formData.append("title", selectedIssue ? `Bug Report: ${selectedIssue}` : "Bug Report");
      formData.append("description", message);
      const ticketType = category === 'technical' ? 'TECHNICAL_ISSUE' : 'BUG_REPORT';
      formData.append("type", ticketType);
      formData.append("category", selectedIssue || category);
      const priority = category === 'technical' ? 'HIGH' : 'MEDIUM';
      formData.append("priority", priority);

      if (selectedFiles.length > 0) {
        selectedFiles.forEach((file) => {
          formData.append("files", file);
        });
      }

      await supportTicketService.createTicket(formData);
      setIsAnimating(true);
      if (onTicketCreated) onTicketCreated();
    } catch (error: any) {
      setSubmitError(
        error.response?.data?.message ||
        "Failed to submit ticket. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (isAnimating) {
      const timer = setTimeout(() => {
        setIsAnimating(false);
        setStep(5);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isAnimating]);

  const handleBack = () => {
    if (step === 2) {
      setStep(1);
    } else if (step === 3) {
      setStep(2);
    } else if (step === 4) {
      setStep(3);
    } else if (step === 5) {
      setStep(4);
    }
  };

  const handleDiscard = () => {
    onClose();
    resetForm();
  };

  const handleSkip = () => {
    setStep(4);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const validFiles = Array.from(files).filter(
        (file) =>
          ["image/png", "image/jpeg", "image/gif"].includes(file.type) &&
          file.size <= 10 * 1024 * 1024
      );
      setSelectedFiles((prev) => [...prev, ...validFiles]);
    }
  };

  const resetForm = () => {
    setStep(1);
    setMessage("");
    setCategory("");
    setSelectedIssue("");
    setSelectedFiles([]);
    setSubmitError("");
    setIsAnimating(false);
    setIsSubmitting(false);
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0">
        <DialogHeader className="p-6 pb-0">
          <div className="flex justify-between items-center">
            <DialogTitle className="text-xl font-semibold">Report Issue</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="p-6 pt-0">
          <StepIndicator currentStep={step} />

          <div className="flex flex-col justify-between min-h-[500px]">
            <div>
              {isAnimating ? (
                <div className="flex items-center justify-center py-20">
                  <div className="flex items-center gap-4">
                    <Loader2 className="w-8 h-8 animate-spin text-green-600" />
                    <span className="text-lg font-medium">Submitting ticket...</span>
                  </div>
                </div>
              ) : step === 1 ? (
                <StepOne message={message} onMessageChange={setMessage} />
              ) : step === 2 ? (
                <StepTwo bugType={category} setBugType={setCategory} />
              ) : step === 3 ? (
                <StepThree 
                  selectedIssue={selectedIssue} 
                  setSelectedIssue={setSelectedIssue} 
                  category={category} 
                />
              ) : step === 4 ? (
                <StepFour
                  selectedFiles={selectedFiles}
                  onBrowseClick={handleBrowseClick}
                  onFileChange={handleFileChange}
                  fileInputRef={fileInputRef}
                />
              ) : (
                <StepFive />
              )}

              {/* Error message */}
              {submitError && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">{submitError}</p>
                </div>
              )}
            </div>

            <NavigationButtons
              step={step}
              onBack={handleBack}
              onNext={handleNext}
              onSkip={handleSkip}
              onDiscard={handleDiscard}
              isSubmitting={isSubmitting}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 