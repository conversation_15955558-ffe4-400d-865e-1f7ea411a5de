import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { SummaryData } from "../types";
import {
  BugIcon,
  AlertTriangleIcon,
  ClockIcon,
  FileTextIcon,
} from "lucide-react";

interface SummaryCardsProps {
  data: SummaryData[];
}

const iconMap: { [key: string]: React.ReactNode } = {
  FileTextIcon: <FileTextIcon className="w-6 h-6" />,
  BugIcon: <BugIcon className="w-6 h-6" />,
  AlertTriangleIcon: <AlertTriangleIcon className="w-6 h-6" />,
  ClockIcon: <ClockIcon className="w-6 h-6" />,
};

export const SummaryCards: React.FC<SummaryCardsProps> = ({ data }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      {data.map((item, index) => (
        <Card key={index} className="bg-white">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div
                className={`w-10 h-10 ${item.bgColor} rounded-[8px] flex items-center justify-center`}
              >
                <span className={item.color}>{iconMap[item.icon]}</span>
              </div>
              <div>
                <p className="text-sm text-gray-600">{item.title}</p>
                <p className={`text-2xl font-bold ${item.color}`}>
                  {item.value}
                </p>
                <p className="text-xs text-gray-500 mt-1">{item.change}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
