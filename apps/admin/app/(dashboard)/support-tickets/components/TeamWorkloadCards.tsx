import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { TeamWorkload } from "../types";
import { UsersIcon } from "lucide-react";

interface TeamWorkloadCardsProps {
  data: TeamWorkload[];
}

export const TeamWorkloadCards: React.FC<TeamWorkloadCardsProps> = ({
  data,
}) => {
  return (
    <Card className="bg-white">
      <CardContent className="p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          Team Workload Overview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {data.map((team, index) => (
            <div key={index} className="border rounded p-4">
              <div className="flex items-start">
                <UsersIcon className="w-6 h-6 text-gray-500 mr-4" />
                <div>
                  <p className="text-lg font-bold text-gray-900 mb-2">
                    {team.team}
                  </p>
                  <div className="text-sm space-y-1">
                    <p className="text-gray-600">
                      Active Tickets:{" "}
                      <span className="font-medium text-gray-900">
                        {team.activeTickets}
                      </span>
                    </p>
                    <p className="text-gray-600">
                      Avg Resolution:{" "}
                      <span className="font-medium text-gray-900">
                        {team.avgResolution}
                      </span>
                    </p>
                    <p className="text-gray-600">
                      Efficiency:{" "}
                      <span className={`font-medium ${team.efficiencyColor}`}>
                        {team.efficiency}
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
