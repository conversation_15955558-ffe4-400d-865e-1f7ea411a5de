import React from "react";
import { Input } from "@admin/components/ui/input";
import { SearchIcon } from "lucide-react";
import { Card } from "@admin/components/ui/card";

// Add custom CSS for select elements
const selectStyles = {
  appearance: "none" as const,
  backgroundImage: "none",
  paddingRight: "2.5rem",
};

interface ProductFiltersProps {
  search: string;
  setSearch: (value: string) => void;
  category: string;
  setCategory: (value: string) => void;
  severity: string;
  setSeverity: (value: string) => void;
  status: string;
  setStatus: (value: string) => void;
  categories: string[];
  severities: string[];
  statuses: string[];
  filteredCount: number;
}

export const ProductFilters: React.FC<ProductFiltersProps> = ({
  search,
  setSearch,
  category,
  setCategory,
  severity,
  setSeverity,
  status,
  setStatus,
  categories,
  severities,
  statuses,
  filteredCount,
}) => {
  return (
    <Card className="mb-6 p-6 bg-white">
      <div className="flex gap-4 mb-6">
        <div className="relative flex-1">
          <Input
            placeholder="Search products..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-12 border border-gray-200 p-5 rounded-[10px] relative"
          />
        </div>
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
          style={selectStyles}
        >
          {categories.map((c) => (
            <option key={c}>{c}</option>
          ))}
        </select>
        <select
          value={severity}
          onChange={(e) => setSeverity(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
          style={selectStyles}
        >
          {severities.map((s) => (
            <option key={s}>{s}</option>
          ))}
        </select>
        <select
          value={status}
          onChange={(e) => setStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
          style={selectStyles}
        >
          {statuses.map((s) => (
            <option key={s}>{s}</option>
          ))}
        </select>
        <span className="text-sm text-gray-500 flex items-center">
          {filteredCount} products found
        </span>
      </div>
    </Card>
  );
}; 