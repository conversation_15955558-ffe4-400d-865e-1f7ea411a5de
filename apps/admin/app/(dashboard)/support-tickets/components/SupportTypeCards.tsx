import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { SupportTypeData } from "../types";
import { BugIcon, AlertTriangleIcon } from "lucide-react";
import { Button } from "@admin/components/ui/button";
import { useRouter } from "next/navigation";

interface SupportTypeCardsProps {
  data: SupportTypeData[];
}

const iconMap: { [key: string]: React.ReactNode } = {
  BugIcon: <BugIcon className="w-6 h-6" />,
  AlertTriangleIcon: <AlertTriangleIcon className="w-6 h-6" />,
};

export const SupportTypeCards: React.FC<SupportTypeCardsProps> = ({ data }) => {
  const router = useRouter();

  const handleCardClick = (route?: string) => {
    if (route) {
      router.push(route);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      {data.map((item, index) => (
        <Card
          key={index}
          className="bg-white cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => handleCardClick(item.route)}
        >
          <CardContent className="p-4">
            <div className="flex items-start">
              <div
                className={`w-10 h-10 ${item.iconBg} rounded-[8px] flex items-center justify-center mr-4`}
              >
                {iconMap[item.icon]}
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 mb-1">
                  {item.title}
                </h3>
                <p className="text-sm text-gray-600 mb-3">{item.description}</p>
                <Button
                  variant="link"
                  className="p-0 text-blue-600 hover:text-blue-700"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCardClick(item.route);
                  }}
                >
                  {item.issuesCount}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
