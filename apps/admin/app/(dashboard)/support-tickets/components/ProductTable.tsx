import React, { useState } from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { But<PERSON> } from "@admin/components/ui/button";
import { Badge } from "@admin/components/ui/badge";
import { ProductData } from "../types";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@admin/components/ui/table";
import { Eye, Pencil, Trash2, X, Copy, Share2, Download, FileText, Image as ImageIcon } from "lucide-react";
import { Avatar, AvatarFallback } from "@admin/components/ui/avatar";
import { MessageCircle, Paperclip, User, Wrench } from "lucide-react";

interface ProductTableProps {
  products: ProductData[];
}

export const ProductTable: React.FC<ProductTableProps> = ({ products }) => {
  const [selectedProduct, setSelectedProduct] = useState<ProductData | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [imagePreview, setImagePreview] = useState<{ url: string; title: string } | null>(null);

  const getFileIcon = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <FileText className="w-5 h-5 text-red-600" />;
      case 'doc':
      case 'docx':
        return <FileText className="w-5 h-5 text-blue-600" />;
      case 'xls':
      case 'xlsx':
        return <FileText className="w-5 h-5 text-green-600" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <ImageIcon className="w-5 h-5 text-purple-600" />;
      default:
        return <Paperclip className="w-5 h-5 text-gray-600" />;
    }
  };

  const getFileType = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'PDF Document';
      case 'doc':
      case 'docx':
        return 'Word Document';
      case 'xls':
      case 'xlsx':
        return 'Excel Spreadsheet';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return 'Image File';
      default:
        return 'Document';
    }
  };

  const handleView = (product: ProductData) => {
    setSelectedProduct(product);
    setModalOpen(true);
  };

  const handleClose = () => {
    setModalOpen(false);
    setSelectedProduct(null);
  };

  return (
    <>
      <div className="bg-white rounded overflow-hidden">
        <Table className="rounded">
          <TableHeader>
            <TableRow className="hover:bg-transparent">
              <TableHead className="py-4 px-6">Issue details</TableHead>
              <TableHead className="py-4 px-6">Category</TableHead>
              <TableHead className="py-4 px-6">Severity</TableHead>
              <TableHead className="py-4 px-6">Status</TableHead>
              <TableHead className="py-4 px-6">Assignee</TableHead>
              <TableHead className="py-4 px-6">Activity</TableHead>
              <TableHead className="py-4 px-6 text-center">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.map((product, i) => (
              <TableRow key={i}>
                <TableCell className="py-5 px-6">
                  <div className="flex items-start gap-4">
                    <span className="w-7 h-7 mt-1 flex items-center justify-center rounded-full bg-[#E6F4EA]">
                      <Wrench className="w-4 h-4 text-[#05A049]" />
                    </span>
                    <div>
                      <div className="text-sm font-medium text-gray-900">{product.title}</div>
                      <div className="text-xs text-gray-500 mt-1">{product.id}</div>
                      <div className="text-xs text-gray-500 mt-1">{product.description}</div>
                      <div className="text-xs text-gray-500 mt-2">
                        <span className="font-semibold">Impact:</span> <span className={`font-medium ${product.impactSeverity === "Critical" ? "text-red-700" : product.impactSeverity === "High" ? "text-orange-700" : product.impactSeverity === "Medium" ? "text-yellow-700" : "text-green-700"}`}>{product.impactSeverity}</span> - {product.impact}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="py-5 px-6">
                <Badge className="bg-[#E6E6FA] text-[#7B2CBF] rounded-full px-3 py-1">
                    {product.category}
                  </Badge>
                </TableCell>
                <TableCell className="py-5 px-6">
                  <Badge
                    className={`text-sm rounded-full px-3 py-1 ${product.severity === "Critical" ? "bg-red-100 text-red-800" : product.severity === "High" ? "bg-orange-100 text-orange-800" : product.severity === "Medium" ? "bg-yellow-100 text-yellow-800" : "bg-[#EDF8F2] text-[#05A049]"}`}
                  >
                    {product.severity}
                  </Badge>
                </TableCell>
                <TableCell className="py-5 px-6">
                  <Badge
                    className={`text-sm rounded-full px-3 py-1 ${product.status === "In Progress" ? "bg-yellow-100 text-yellow-800" : product.status === "Open" ? "bg-blue-100 text-blue-800" : "bg-[#EDF8F2] text-[#05A049]"}`}
                  >
                    {product.status}
                  </Badge>
                </TableCell>
                <TableCell className="py-5 px-6 text-sm text-gray-900">
                  <div className="flex items-center gap-2">
                    {(product.assignee.includes("Team") || product.assignee === "Customer Support") ? (
                      <User className="h-7 w-7 text-gray-400 bg-gray-100 rounded-full p-1" />
                    ) : (
                      <Avatar className="h-7 w-7">
                        <AvatarFallback>
                          {product.assignee
                            .split(" ")
                            .filter(w => w.length > 0)
                            .map(w => w[0])
                            .join("")
                            .toUpperCase() || "?"}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <span>{product.assignee}</span>
                  </div>
                </TableCell>
                <TableCell className="py-5 px-6 text-xs text-gray-500">
                  <div>{product.activity}</div>
                  <div className="flex gap-4 mt-1">
                    <span className="flex items-center gap-1"><MessageCircle className="w-4 h-4" />{product.comments}</span>
                    <span className="flex items-center gap-1"><Paperclip className="w-4 h-4" />{product.attachments}</span>
                  </div>
                </TableCell>
                <TableCell className="py-5 px-6 text-center">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-green-600 hover:bg-green-50 flex items-center gap-1 mx-auto"
                    onClick={() => handleView(product)}
                  >
                    <Eye className="h-4 w-4" />
                    <span className="text-green-700 font-medium">View</span>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Modal for product issue details */}
      {modalOpen && selectedProduct && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-2xl max-h-[90vh] overflow-y-auto relative">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-700"
              onClick={handleClose}
            >
              <X className="w-5 h-5" />
            </button>
            
            <div className="mb-6">
              <h3 className="text-xl font-bold mb-2 text-gray-900">Product Issue Details</h3>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <span className="w-8 h-8 flex items-center justify-center rounded-full bg-[#E6F4EA]">
                    <Wrench className="w-4 h-4 text-[#05A049]" />
                  </span>
                  <div>
                    <span className="text-sm font-medium text-gray-900">{selectedProduct.id}</span>
                    <div className="text-xs text-gray-500">Ticket ID</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">Last Updated:</span> {selectedProduct.activity}
                  </div>
                  <div className="text-xs text-gray-500">Activity</div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-4">
                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Issue Title:</span>
                  <div className="text-gray-800 bg-gray-50 p-3 rounded-lg">
                    {selectedProduct.title}
                  </div>
                </div>

                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Description:</span>
                  <div className="text-gray-800 bg-gray-50 p-3 rounded-lg whitespace-pre-line min-h-[100px]">
                    {selectedProduct.description || 'No description provided.'}
                  </div>
                </div>

                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Impact Assessment:</span>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">Impact:</span>
                      <span className="text-gray-800">{selectedProduct.impact}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Severity:</span>
                      <Badge
                        className={`text-sm rounded-full px-3 py-1 ${selectedProduct.impactSeverity === "Critical" ? "bg-red-100 text-red-800" : selectedProduct.impactSeverity === "High" ? "bg-orange-100 text-orange-800" : selectedProduct.impactSeverity === "Medium" ? "bg-yellow-100 text-yellow-800" : "bg-[#EDF8F2] text-[#05A049]"}`}
                      >
                        {selectedProduct.impactSeverity}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Category:</span>
                  <Badge className="bg-[#E6E6FA] text-[#7B2CBF] rounded-full px-3 py-1">
                    {selectedProduct.category}
                  </Badge>
                </div>

                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Severity Level:</span>
                  <Badge
                    className={`text-sm rounded-full px-3 py-1 ${selectedProduct.severity === "Critical" ? "bg-red-100 text-red-800" : selectedProduct.severity === "High" ? "bg-orange-100 text-orange-800" : selectedProduct.severity === "Medium" ? "bg-yellow-100 text-yellow-800" : "bg-[#EDF8F2] text-[#05A049]"}`}
                  >
                    {selectedProduct.severity}
                  </Badge>
                </div>

                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Status:</span>
                  <Badge
                    className={`text-sm rounded-full px-3 py-1 ${selectedProduct.status === "In Progress" ? "bg-yellow-100 text-yellow-800" : selectedProduct.status === "Open" ? "bg-blue-100 text-blue-800" : "bg-[#EDF8F2] text-[#05A049]"}`}
                  >
                    {selectedProduct.status}
                  </Badge>
                </div>

                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Assigned To:</span>
                  <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-lg">
                    {(selectedProduct.assignee.includes("Team") || selectedProduct.assignee === "Customer Support") ? (
                      <User className="h-6 w-6 text-gray-400 bg-gray-100 rounded-full p-1" />
                    ) : (
                      <Avatar className="h-6 w-6">
                        <AvatarFallback>
                          {selectedProduct.assignee
                            .split(" ")
                            .filter(w => w.length > 0)
                            .map(w => w[0])
                            .join("")
                            .toUpperCase() || "?"}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <span className="text-gray-800">{selectedProduct.assignee}</span>
                  </div>
                </div>

                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Activity:</span>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="text-gray-800 mb-2">{selectedProduct.activity}</div>
                    <div className="flex gap-4 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <MessageCircle className="w-4 h-4" />
                        {selectedProduct.comments} comments
                      </span>
                      <span className="flex items-center gap-1">
                        <Paperclip className="w-4 h-4" />
                        {selectedProduct.attachments} attachments
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <span className="font-semibold text-gray-700 block mb-2">Reported By:</span>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    {selectedProduct.user ? (
                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="bg-blue-100 text-blue-600">
                              {selectedProduct.user.first_name?.[0]}{selectedProduct.user.last_name?.[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="text-gray-800 font-medium">
                              {selectedProduct.user.first_name} {selectedProduct.user.last_name}
                            </div>
                            <div className="text-xs text-gray-500">Reporter</div>
                          </div>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center gap-2 text-gray-600">
                            <span className="font-medium">Email:</span>
                            <a 
                              href={`mailto:${selectedProduct.user.email}`}
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              {selectedProduct.user.email}
                            </a>
                          </div>
                          <div className="text-gray-600">
                            <span className="font-medium">User ID:</span> {selectedProduct.user.id}
                          </div>
                        </div>
                        <div className="pt-2 border-t border-gray-200">
                          <Button 
                            size="sm" 
                            variant="outline" 
                            className="text-blue-600 border-blue-200 hover:bg-blue-50"
                            onClick={() => selectedProduct.user && window.open(`mailto:${selectedProduct.user.email}?subject=Re: ${selectedProduct.title}`, '_blank')}
                          >
                            <MessageCircle className="w-4 h-4 mr-1" />
                            Contact User
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-gray-500 text-sm flex items-center gap-2">
                        <User className="w-4 h-4" />
                        User information not available
                      </div>
                    )}
                  </div>
                </div>



                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-semibold text-gray-700">Attached Documents:</span>
                    {selectedProduct.support_ticket_docs && selectedProduct.support_ticket_docs.length > 0 && (
                      <span className="text-sm text-gray-500">
                        {selectedProduct.support_ticket_docs.length} document{selectedProduct.support_ticket_docs.length > 1 ? 's' : ''}
                      </span>
                    )}
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    {selectedProduct.support_ticket_docs && selectedProduct.support_ticket_docs.length > 0 ? (
                      <div className="space-y-3">
                        {selectedProduct.support_ticket_docs.map((doc, index) => (
                          <div key={doc.image_id} className="border border-gray-200 rounded-lg p-3 bg-white">
                            <div className="flex items-center justify-between">
                                                           <div className="flex items-center gap-3">
                               <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                 {getFileIcon(doc.image_url)}
                               </div>
                               <div>
                                 <div className="text-sm font-medium text-gray-900">
                                   {getFileType(doc.image_url)} {index + 1}
                                 </div>
                                 <div className="text-xs text-gray-500">
                                   Uploaded {new Date(doc.uploaded_at).toLocaleDateString()}
                                 </div>
                               </div>
                             </div>
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-blue-600 border-blue-200 hover:bg-blue-50"
                                  onClick={() => setImagePreview({ url: doc.image_url, title: `Document ${index + 1}` })}
                                >
                                  <Eye className="w-3 h-3 mr-1" />
                                  View
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-gray-600 border-gray-200 hover:bg-gray-50"
                                  onClick={() => {
                                    const link = document.createElement('a');
                                    link.href = doc.image_url;
                                    const extension = doc.image_url.split('.').pop() || 'file';
                                    link.download = `document_${index + 1}.${extension}`;
                                    link.click();
                                  }}
                                >
                                  <Download className="w-3 h-3 mr-1" />
                                  Download
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <Paperclip className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-500">No documents attached</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 mt-6 pt-6 border-t border-gray-200">
              <Button variant="outline" className="flex-1">
                <Pencil className="w-4 h-4 mr-2" />
                Edit Issue
              </Button>
              <Button className="flex-1 bg-[#05A049] hover:bg-[#048A3F]">
                Update Status
              </Button>
            </div>
            
            {/* Quick Actions */}
            <div className="mt-4 pt-4 border-t border-gray-100">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h4>
              <div className="flex flex-wrap gap-2">
                {selectedProduct.user && (
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                    onClick={() => selectedProduct.user && window.open(`mailto:${selectedProduct.user.email}?subject=Re: ${selectedProduct.title}`, '_blank')}
                  >
                    <MessageCircle className="w-3 h-3 mr-1" />
                    Contact User
                  </Button>
                )}
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="text-gray-600 border-gray-200 hover:bg-gray-50"
                  onClick={() => {
                    navigator.clipboard.writeText(selectedProduct.id);
                    // You could add a toast notification here
                  }}
                >
                  <Copy className="w-3 h-3 mr-1" />
                  Copy Ticket ID
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="text-gray-600 border-gray-200 hover:bg-gray-50"
                >
                  <Share2 className="w-3 h-3 mr-1" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Preview Modal */}
      {imagePreview && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl max-h-[90vh] overflow-hidden relative">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">{imagePreview.title}</h3>
              <button
                className="text-gray-400 hover:text-gray-700"
                onClick={() => setImagePreview(null)}
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 overflow-auto max-h-[calc(90vh-80px)]">
              <img
                src={imagePreview.url}
                alt={imagePreview.title}
                className="max-w-full h-auto rounded-lg"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04MCAxMDBDODAgODkuNTQ0IDg4LjU0NCA4MSA5OSA4MUgxMDFDMTExLjQ1NiA4MSAxMjAgODkuNTQ0IDEyMCAxMDBDMTIwIDExMC40NTYgMTExLjQ1NiAxMTkgMTAxIDExOUg5OUM4OC41NDQgMTE5IDgwIDExMC40NTYgODAgMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTIwIDgwSDgwVjEyMEgxMjBWODBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K';
                  target.alt = 'Image not available';
                }}
              />
            </div>
            <div className="p-4 border-t border-gray-200 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = imagePreview.url;
                  const extension = imagePreview.url.split('.').pop() || 'file';
                  link.download = `${imagePreview.title}.${extension}`;
                  link.click();
                }}
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
              <Button
                onClick={() => window.open(imagePreview.url, '_blank')}
              >
                Open in New Tab
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}; 