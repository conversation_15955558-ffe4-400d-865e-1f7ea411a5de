import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { TechnicalSupportSummary } from "../types";
import {
  AlertTriangle,
  AlertCircle,
  Clock,
  CheckCircle,
  Activity,
} from "lucide-react";

interface TechnicalSupportSummaryCardProps {
  item: TechnicalSupportSummary;
}

const iconMap = {
  AlertTriangle,
  AlertCircle,
  Clock,
  CheckCircle,
  Activity,
};

export const TechnicalSupportSummaryCard: React.FC<
  TechnicalSupportSummaryCardProps
> = ({ item }) => {
  const IconComponent = iconMap[item.icon as keyof typeof iconMap];

  return (
    <Card className="bg-white shadow-sm border border-gray-200">
      <CardContent className="p-4 flex items-center gap-3">
        <div
          className={`w-10 h-10 rounded-[8px] flex items-center justify-center ${item.color}`}
        >
          <IconComponent className="w-5 h-5" />
        </div>
        <div className="flex flex-col">
          <span className="text-sm text-gray-600">{item.label}</span>
          <span className="text-lg font-bold text-gray-900">{item.value}</span>
        </div>
      </CardContent>
    </Card>
  );
};
