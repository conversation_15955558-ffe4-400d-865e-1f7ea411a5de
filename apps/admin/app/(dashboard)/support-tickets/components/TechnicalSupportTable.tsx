import React, { useState } from "react";
import { <PERSON><PERSON> } from "@admin/components/ui/button";
import { Badge } from "@admin/components/ui/badge";
import { TechnicalSupportData } from "../types";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@admin/components/ui/table";
import { Eye, Pencil, Trash2 } from "lucide-react";
import { TicketDetailsModal } from "./TicketDetailsModal";
import { SupportTicket } from "@admin/app/lib/supportTicketService";

interface TechnicalSupportTableProps {
  issues: TechnicalSupportData[];
  originalTickets: SupportTicket[];
}

export const TechnicalSupportTable: React.FC<TechnicalSupportTableProps> = ({
  issues,
  originalTickets,
}) => {
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleViewTicket = (issue: TechnicalSupportData) => {
    // Find the original ticket data using the ticket number
    const originalTicket = originalTickets.find(ticket => ticket.ticket_number === issue.id);
    if (originalTicket) {
      setSelectedTicket(originalTicket);
      setIsModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedTicket(null);
  };
  return (
    <div className="bg-white rounded overflow-hidden">
      <Table className="rounded">
        <TableHeader>
          <TableRow className="hover:bg-transparent">
            <TableHead className="py-4 px-6">Issue Details</TableHead>
            <TableHead className="py-4 px-6">Category</TableHead>
            <TableHead className="py-4 px-6">Urgency</TableHead>
            <TableHead className="py-4 px-6">Status</TableHead>
            <TableHead className="py-4 px-6">Assignee</TableHead>
            <TableHead className="py-4 px-6">Activity</TableHead>
            <TableHead className="py-4 px-6 text-center">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {issues.map((issue, i) => (
            <TableRow key={i}>
              <TableCell className="py-5 px-6">
                <div className="flex items-center gap-4">
                  <div>{issue.icon}</div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {issue.title}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {issue.id}
                    </div>
                    {issue.reporter && (
                      <div className="text-xs text-gray-500 mt-1">
                        Reporter: {issue.reporter}
                      </div>
                    )}
                  </div>
                </div>
              </TableCell>
              <TableCell className="py-5 px-6">
                <Badge
                  className={`text-sm rounded-full px-3 py-1 ${
                    issue.category === "UI/UX Issues"
                      ? "bg-blue-100 text-blue-800"
                      : issue.category === "Real-time Data"
                      ? "bg-yellow-100 text-yellow-800"
                      : issue.category === "Performance Issues"
                      ? "bg-orange-100 text-orange-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {issue.category}
                </Badge>
              </TableCell>
              <TableCell className="py-5 px-6">
                <Badge
                  className={`text-sm rounded-full px-3 py-1 ${issue.priority === "Critical" ? "bg-red-100 text-red-800" : issue.priority === "High" ? "bg-orange-100 text-orange-800" : issue.priority === "Medium" ? "bg-yellow-100 text-yellow-800" : "bg-[#EDF8F2] text-[#05A049]"}`}
                >
                  {issue.priority}
                </Badge>
              </TableCell>
              <TableCell className="py-5 px-6">
                <Badge
                  className={`text-sm rounded-full px-3 py-1 ${issue.status === "In Progress" ? "bg-yellow-100 text-yellow-800" : issue.status === "Open" ? "bg-blue-100 text-blue-800" : issue.status === "Escalated" ? "bg-red-100 text-red-800" : "bg-[#EDF8F2] text-[#05A049]"}`}
                >
                  {issue.status}
                </Badge>
              </TableCell>
              <TableCell className="py-5 px-6 text-sm text-gray-900">
                {issue.assignee}
              </TableCell>
              <TableCell className="py-5 px-6 text-xs text-gray-500">
                {issue.activity}
              </TableCell>
              <TableCell className="py-5 px-6 text-center">
                <span className="inline-flex gap-3">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="text-blue-600 hover:bg-blue-50"
                    onClick={() => handleViewTicket(issue)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="text-[#05A049] hover:bg-[#EDF8F2]"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {/* Ticket Details Modal */}
      <TicketDetailsModal
        ticket={selectedTicket}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};
