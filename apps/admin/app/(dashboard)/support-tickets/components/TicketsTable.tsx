import React, { useState, useEffect, useRef } from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { Button } from "@admin/components/ui/button";
import { Ticket } from "../types";
import { EyeIcon, EditIcon, TrashIcon, X, ChevronDown, User } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@admin/components/ui/table";
import { supportTicketService } from "../../../lib/supportTicketService";

interface TicketsTableProps {
  tickets: Ticket[];
  onDelete?: (ticketId: string) => void;
  onTicketUpdate?: () => void; // Callback to refresh tickets after assignment
}

export const TicketsTable: React.FC<TicketsTableProps> = ({ tickets, onDelete, onTicketUpdate }) => {
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [assigningTicketId, setAssigningTicketId] = useState<string | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleView = (ticket: Ticket) => {
    setSelectedTicket(ticket);
    setModalOpen(true);
  };

  const handleClose = () => {
    setModalOpen(false);
    setSelectedTicket(null);
  };

  const handleDelete = async (ticket: Ticket) => {
    if (!window.confirm('Are you sure you want to delete this ticket?')) return;
    setDeletingId(ticket.id);
    try {
      if (onDelete) {
        await onDelete(ticket.ticket_number || ticket.id);
      }
    } finally {
      setDeletingId(null);
    }
  };

  const handleAssignTicket = async (ticketId: string, assigneeTeam: string) => {
    setAssigningTicketId(ticketId);
    try {
      await supportTicketService.assignTicket(ticketId, {
        assignee_team: assigneeTeam,
        notes: `Ticket assigned to ${assigneeTeam}`
      });
      
      // Refresh the tickets list
      if (onTicketUpdate) {
        onTicketUpdate();
      }
    } catch (error) {
      console.error('Error assigning ticket:', error);
      alert('Failed to assign ticket. Please try again.');
    } finally {
      setAssigningTicketId(null);
      setOpenDropdownId(null);
    }
  };

  const toggleDropdown = (ticketId: string) => {
    setOpenDropdownId(openDropdownId === ticketId ? null : ticketId);
  };

  const getAssigneeDisplay = (assignee: string) => {
    if (!assignee || assignee === 'Unassigned') {
      return (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-gray-400" />
          <span className="text-gray-500 italic">Unassigned</span>
        </div>
      );
    }
    
    return (
      <div className="flex items-center gap-2">
        <User className="h-4 w-4 text-gray-600" />
        <span className="text-gray-900">{assignee}</span>
      </div>
    );
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <>
      <Card className="mb-6 bg-white p-8">
        <CardContent className="p-0">
          <h2 className="text-xl font-bold text-gray-900 mb-8">
            Recent Support Tickets
          </h2>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50 hover:bg-gray-50">
                  <TableHead className="font-medium text-gray-500 py-4 px-6">
                    Ticket Details
                  </TableHead>
                  <TableHead className="font-medium text-gray-500 py-4 px-6">
                    Type & Category
                  </TableHead>
                  <TableHead className="font-medium text-gray-500 py-4 px-6">
                    Priority
                  </TableHead>
                  <TableHead className="font-medium text-gray-500 py-4 px-6">
                    Status
                  </TableHead>
                  <TableHead className="font-medium text-gray-500 py-4 px-6">
                    Assignee
                  </TableHead>
                  <TableHead className="font-medium text-gray-500 py-4 px-6">
                    Created
                  </TableHead>
                  <TableHead className="font-medium text-gray-500 py-4 px-6">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tickets.slice(0, 4).map((ticket, index) => (
                  <TableRow key={index} className="hover:bg-gray-50 h-16">
                    <TableCell className="py-4 px-6 align-top">
                      <div className="text-sm font-medium text-gray-900">
                        {ticket.details}
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        {ticket.ticket_number}
                      </div>
                    </TableCell>
                    <TableCell className="py-4 px-6 align-top">
                      <div className="text-sm font-medium text-gray-900">
                        {ticket.typeCategory.type}
                      </div>
                      <Badge className="mt-2 bg-blue-100 text-blue-800 rounded-full px-3 py-1">
                        {ticket.typeCategory.category}
                      </Badge>
                    </TableCell>
                    <TableCell className="py-4 px-6 align-top">
                      <Badge
                        className={`text-sm rounded-full px-3 py-1 ${
                          ticket.priority === "Critical"
                            ? "bg-red-100 text-red-800"
                            : ticket.priority === "High"
                              ? "bg-orange-100 text-orange-800"
                              : ticket.priority === "Medium"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-[#EDF8F2] text-[#05A049]"
                        }`}
                      >
                        {ticket.priority}
                      </Badge>
                    </TableCell>
                    <TableCell className="py-4 px-6 align-top">
                      <Badge
                        className={`text-sm rounded-full px-3 py-1 ${
                          ticket.status === "In Progress"
                            ? "bg-yellow-100 text-yellow-800"
                            : ticket.status === "Open"
                              ? "bg-blue-100 text-blue-800"
                              : ticket.status === "Escalated"
                                ? "bg-red-100 text-red-800"
                                : "bg-[#EDF8F2] text-[#05A049]"
                        }`}
                      >
                        {ticket.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="py-4 px-6 align-top">
                      <div className="relative" ref={dropdownRef}>
                        <div className="flex items-center gap-2">
                          {getAssigneeDisplay(ticket.assignee)}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-gray-100"
                            onClick={() => toggleDropdown(ticket.id)}
                            disabled={assigningTicketId === ticket.id}
                          >
                            <ChevronDown className={`h-3 w-3 transition-transform ${openDropdownId === ticket.id ? 'rotate-180' : ''}`} />
                          </Button>
                        </div>
                        
                        {/* Dropdown Menu */}
                        {openDropdownId === ticket.id && (
                          <div className="absolute z-50 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg">
                            <div className="py-1">
                              <button
                                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                                onClick={() => handleAssignTicket(ticket.id, 'Product Team')}
                                disabled={assigningTicketId === ticket.id}
                              >
                                <User className="h-4 w-4" />
                                Product Team
                                {assigningTicketId === ticket.id && (
                                  <div className="ml-auto animate-spin rounded-full h-3 w-3 border-b-2 border-gray-900"></div>
                                )}
                              </button>
                              <button
                                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                                onClick={() => handleAssignTicket(ticket.id, 'Technical Team')}
                                disabled={assigningTicketId === ticket.id}
                              >
                                <User className="h-4 w-4" />
                                Technical Team
                                {assigningTicketId === ticket.id && (
                                  <div className="ml-auto animate-spin rounded-full h-3 w-3 border-b-2 border-gray-900"></div>
                                )}
                              </button>
                              <button
                                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                                onClick={() => handleAssignTicket(ticket.id, 'Unassigned')}
                                disabled={assigningTicketId === ticket.id}
                              >
                                <User className="h-4 w-4" />
                                Unassign
                                {assigningTicketId === ticket.id && (
                                  <div className="ml-auto animate-spin rounded-full h-3 w-3 border-b-2 border-gray-900"></div>
                                )}
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-4 px-6 text-sm text-gray-500 align-top">
                      {ticket.created}
                    </TableCell>
                    <TableCell className="py-4 px-6 align-top">
                      <div className="flex gap-4">
                        <button
                          title="View"
                          className="hover:text-[#05A049] transition-colors"
                          onClick={() => handleView(ticket)}
                        >
                          <EyeIcon className="w-5 h-5 text-[#05A049]" />
                        </button>
                        <button
                          title="Edit"
                          className="hover:text-gray-700 transition-colors"
                        >
                          <EditIcon className="w-5 h-5 text-gray-600" />
                        </button>
                        <button
                          title="Delete"
                          className={`hover:text-red-700 transition-colors ${deletingId === ticket.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={() => handleDelete(ticket)}
                          disabled={deletingId === ticket.id}
                        >
                          <TrashIcon className="w-5 h-5 text-red-600" />
                        </button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modal for ticket details */}
      {modalOpen && selectedTicket && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-md relative">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-700"
              onClick={handleClose}
            >
              <X className="w-5 h-5" />
            </button>
            <h3 className="text-lg font-bold mb-4 text-gray-900">Ticket Details</h3>
            <div className="mb-2">
              <span className="font-semibold text-gray-700">Description:</span>
              <div className="text-gray-800 mt-1 whitespace-pre-line">
                {selectedTicket.description || 'No description provided.'}
              </div>
            </div>
            <div className="mb-2">
              <span className="font-semibold text-gray-700">Email:</span>
              <div className="text-gray-800 mt-1">
                {selectedTicket.email || 'No email provided.'}
              </div>
            </div>
            <div className="mb-2">
              <span className="font-semibold text-gray-700">Uploaded Documents:</span>
              <div className="mt-1 space-y-2">
                {Array.isArray(selectedTicket.support_ticket_docs) && selectedTicket.support_ticket_docs.length > 0 ? (
                  selectedTicket.support_ticket_docs.map((doc) => (
                    <div key={doc.image_id} className="flex items-center gap-2">
                      {/* If it's an image, show preview, else show link */}
                      {doc.image_url.match(/\.(jpg|jpeg|png|gif)$/i) ? (
                        <a href={doc.image_url} target="_blank" rel="noopener noreferrer">
                          <img src={doc.image_url} alt="Uploaded" className="w-16 h-16 object-cover rounded border" />
                        </a>
                      ) : (
                        <a
                          href={doc.image_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 underline break-all max-w-[220px] inline-block truncate"
                          title={doc.image_url}
                          style={{ wordBreak: 'break-all' }}
                        >
                          {(doc.image_url.split('/').pop() || doc.image_url).slice(0, 40) + ((doc.image_url.split('/').pop() || doc.image_url).length > 40 ? '...' : '')}
                        </a>
                      )}
                      <span className="text-xs text-gray-400">{new Date(doc.uploaded_at).toLocaleString()}</span>
                    </div>
                  ))
                ) : (
                  <span className="text-gray-500">No documents uploaded.</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
