import React from "react";
import { Input } from "@admin/components/ui/input";
import { SearchIcon } from "lucide-react";
import { Card } from "@admin/components/ui/card";

// Add custom CSS for select elements
const selectStyles = {
  appearance: "none" as const,
  backgroundImage: "none",
  paddingRight: "2.5rem",
};

interface TechnicalSupportFiltersProps {
  search: string;
  setSearch: (value: string) => void;
  category: string;
  setCategory: (value: string) => void;
  priority: string;
  setPriority: (value: string) => void;
  status: string;
  setStatus: (value: string) => void;
  categories: string[];
  priorities: string[];
  statuses: string[];
  filteredCount: number;
}

export const TechnicalSupportFilters: React.FC<
  TechnicalSupportFiltersProps
> = ({
  search,
  setSearch,
  category,
  setCategory,
  priority,
  setPriority,
  status,
  setStatus,
  categories,
  priorities,
  statuses,
  filteredCount,
}) => {
  return (
    <Card className="mb-6 p-6 bg-white">
      <div className="flex gap-4 mb-6">
        <div className="relative flex-1">
          <Input
            placeholder="Search technical issues..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-12 border border-gray-200 p-5 rounded-[10px] relative"
          />
        </div>
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
          style={selectStyles}
        >
          <option value="All Categories">All Categories</option>
          {categories
            .filter((c) => c !== "All Categories")
            .map((c) => (
              <option key={c} value={c}>
                {c}
              </option>
            ))}
        </select>
        <select
          value={priority}
          onChange={(e) => setPriority(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
          style={selectStyles}
        >
          <option value="All Priorities">All Priorities</option>
          {priorities
            .filter((p) => p !== "All Priorities")
            .map((p) => (
              <option key={p} value={p}>
                {p}
              </option>
            ))}
        </select>
        <select
          value={status}
          onChange={(e) => setStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
          style={selectStyles}
        >
          <option value="All Status">All Status</option>
          {statuses
            .filter((s) => s !== "All Status")
            .map((s) => (
              <option key={s} value={s}>
                {s}
              </option>
            ))}
        </select>
        <span className="text-sm text-gray-500 flex items-center">
          {filteredCount} technical issues found
        </span>
      </div>
    </Card>
  );
};
