import React from 'react';

export const StepFive: React.FC = () => {
  return (
    <div className="flex items-center justify-center py-20">
      <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4">
        <div className="bg-[#05A049] w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center">
          <svg
            className="w-5 h-5 sm:w-6 sm:h-6 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M5 13l4 4L19 7"
            ></path>
          </svg>
        </div>
        <h2 className="text-base sm:text-lg md:text-2xl font-semibold text-[#1F2937] text-center sm:text-left">
          Ticket Submitted Successfully
        </h2>
      </div>
    </div>
  );
}; 