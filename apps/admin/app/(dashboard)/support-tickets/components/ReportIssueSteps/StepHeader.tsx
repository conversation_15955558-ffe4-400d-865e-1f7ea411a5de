import React from 'react';

interface StepHeaderProps {
  title: string;
  description: string;
}

export const StepHeader: React.FC<StepHeaderProps> = ({ title, description }) => {
  return (
    <div className="mb-4 sm:mb-6 flex flex-col sm:flex-row justify-between items-start gap-4">
      <div className="flex-1">
        <h2 className="text-base sm:text-lg lg:text-3xl md:text-3xl font-bold text-[#1F2937] mb-2 text-left">
          {title}
        </h2>
        <p className="text-xs sm:text-sm md:text-base text-[#4B5563] text-left mt-6">
          {description}
        </p>
      </div>
      <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-[90.96px] md:h-[90.96px] bg-green-100 rounded-full flex items-center justify-center mx-auto sm:mx-0">
        <svg
          className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 text-green-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
          />
        </svg>
      </div>
    </div>
  );
}; 