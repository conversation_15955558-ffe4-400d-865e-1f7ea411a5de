import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
interface NavigationButtonsProps {
  step: number;
  onBack: () => void;
  onNext: () => void;
  onSkip?: () => void;
  onDiscard: () => void;
  isSubmitting?: boolean;
}

export const NavigationButtons: React.FC<NavigationButtonsProps> = ({
  step,
  onBack,
  onNext,
  onSkip,
  onDiscard,
  isSubmitting = false
}) => {
  return (
    <>
      <div className="mb-6 sm:mb-8 mt-auto">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-3 sm:gap-4">
          <div className="order-2 sm:order-1">
            {(step >= 2 && step <= 4) && (
              <button
                onClick={onBack}
                disabled={isSubmitting}
                className="bg-white text-gray-700 px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 hover:cursor-pointer rounded-full border border-gray-300 hover:bg-gray-50 transition-colors font-medium flex items-center text-xs sm:text-sm md:text-base disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="mr-1 sm:mr-2"><ArrowLeft className="w-4 h-4"/></span> Back
              </button>
            )}
          </div>
          <div className="order-1 sm:order-2">
            {step >= 1 && step <= 3 && (
              <button
                onClick={onNext}
                disabled={isSubmitting}
                className="bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] hover:cursor-pointer text-white px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 rounded-full hover:bg-[linear-gradient(91.8deg,rgba(5,160,73,1)_0%,rgba(5,160,73,0.8)_100%)] transition-colors font-medium flex items-center text-xs sm:text-sm md:text-base disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next <span className="ml-1 sm:ml-2"><ArrowRight className="w-4 h-4"/></span>
              </button>
            )}
            {step === 4 && (
              <div className="flex items-center gap-3 sm:gap-4">
                {onSkip && (
                  <button
                    onClick={onSkip}
                    disabled={isSubmitting}
                    className="text-xs sm:text-sm mr-2 md:text-base text-[#4B5563] hover:cursor-pointer hover:text-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Skip
                  </button>
                )}
                <button
                  onClick={onNext}
                  disabled={isSubmitting}
                  className="bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] hover:cursor-pointer text-white px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 rounded-full hover:bg-[linear-gradient(91.8deg,rgba(5,160,73,1)_0%,rgba(5,160,73,0.8)_100%)] transition-colors font-medium flex items-center text-xs sm:text-sm md:text-base disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>Submit</>
                  )}
                </button>
              </div>
            )}
            {step === 5 && (
              <button
                onClick={onNext}
                className="hover:cursor-pointer bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] text-white px-3 hover:cursor-pointer sm:px-4 md:px-6 py-2 sm:py-2.5 rounded-full hover:bg-[linear-gradient(91.8deg,rgba(5,160,73,1)_0%,rgba(5,160,73,0.8)_100%)] transition-colors font-medium flex items-center text-xs sm:text-sm md:text-base"
              >
                Close <span className="ml-1 sm:ml-2"><ArrowRight className="w-4 h-4"/></span>
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="bg-[#05A0490D] p-4 flex justify-center">
        <button
          onClick={onDiscard}
          disabled={isSubmitting}
          className="sm:text-sm text-[#4B5563] transition-colors flex items-center hover:cursor-pointer hover:text-black disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <p className="text-md">Discard Ticket</p><ArrowDown className='h-4 w-4 text-[#4B5563] ml-1 '/>
        </button>
      </div>
    </>
  );
}; 