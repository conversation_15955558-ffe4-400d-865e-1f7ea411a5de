import React from "react";
import { StepHeader } from "./StepHeader";

interface StepThreeProps {
  category: string;
  selectedIssue: string;
  setSelectedIssue: (selectedIssue: string) => void;
}

export const StepThree: React.FC<StepThreeProps> = ({ category, selectedIssue, setSelectedIssue }) => {
  const technicalOptions = [
    'UI/UX Issues',
    'Login Issues',
    'Real-time Data',
    'Performance Issues',
    'Security Issues',
    'Others'
  ];

  const productOptions = [
    'KYC Issue',
    'KYC Rejection',
    'Investment Issue',
    'Others'
  ];

  const options = category === 'technical' ? technicalOptions : productOptions;
  const title = category === 'technical' ? 'Technical Issue Details' : 'Product Issue Details';
  const subtitle = category === 'technical'
    ? 'Select the specific technical issue you\'re experiencing'
    : 'Select the specific product issue you\'re experiencing';

  return (
    <>
      <StepHeader
        title={title}
        description={subtitle}
      />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {options.map((option) => (
          <button
            key={option}
            onClick={() => setSelectedIssue(option)}
            className={`p-6 border-2 rounded-2xl text-left transition-all duration-200 hover:shadow-md hover:cursor-pointer${
              selectedIssue === option
                ? 'border-green-500 shadow-md hover:cursor-pointer'
                : 'border-gray-200 hover:border-gray-300 hover:cursor-pointer'
            }`}
          >
            <div className="flex items-center space-x-4">
              <div className={`w-5 h-5 rounded-full border-2 transition-colors duration-200 ${
                selectedIssue === option
                  ? 'border-green-500 bg-green-500'
                  : 'border-gray-300'
              }`}>
                {selectedIssue === option && (
                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                )}
              </div>
              <span className="text-lg font-bold text-gray-900">{option}</span>
            </div>
          </button>
        ))}
      </div>
    </>
  );
}; 