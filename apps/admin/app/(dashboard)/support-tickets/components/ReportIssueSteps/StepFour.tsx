import React from "react";
import { Upload } from "lucide-react";
import { StepHeader } from "./StepHeader";

interface StepFourProps {
  selectedFiles: File[];
  onBrowseClick: () => void;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
}

export const StepFour: React.FC<StepFourProps> = ({ 
  selectedFiles, 
  onBrowseClick, 
  onFileChange, 
  fileInputRef 
}) => {
  return (
    <>
      <StepHeader
        title="Add Screenshots"
        description="Any sensitive info will be automatically scrubbed"
      />
      <div className="border-2 border-dashed border-gray-300 rounded-[1.5rem] sm:rounded-[2rem] h-[120px] sm:h-[150px] md:h-[184px] p-4 sm:p-6 md:p-8 text-center mb-4 sm:mb-6 bg-[linear-gradient(91.47deg,#FFFFFC_0.06%,rgba(255,255,252,0.3)_100%)]">
        <div className="flex justify-center mb-2">
          <Upload className="w-12 h-12 text-[#9CA3AF]"/>
        </div>
        <p className="text-xs sm:text-sm md:text-base text-[#4B5563] mt-6">
          Drag and drop your screenshots here, or{" "}
          <span 
            className="text-[#05A049] cursor-pointer" 
            onClick={onBrowseClick}
          >
            browse
          </span>
        </p>
        <p className="text-sm text-[#6B7280] mt-1">Supports: PNG, JPG, GIF (max 10MB)</p>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept="image/png,image/jpeg,image/gif"
          multiple
          onChange={onFileChange}
        />
        {selectedFiles && selectedFiles.length > 0 && (
          <div className="mt-2 text-xs text-gray-600 break-words">
            Selected files: {selectedFiles.map((file: File) => file.name).join(', ')}
          </div>
        )}
      </div>
    </>
  );
}; 