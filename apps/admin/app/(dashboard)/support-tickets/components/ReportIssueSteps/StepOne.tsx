import React from 'react';
import { StepHeader } from './StepHeader';

interface StepOneProps {
  message: string;
  onMessageChange: (message: string) => void;
}

export const StepOne: React.FC<StepOneProps> = ({ message, onMessageChange }) => {
  return (
    <>
      <StepHeader
        title="Report a bug"
        description="Add a short description to help us investigate. For issues with your account, <NAME_EMAIL>"
      />
      <div className="mb-4 sm:mb-6">
        <textarea
          rows={4}
          value={message}
          onChange={(e) => onMessageChange(e.target.value)}
          className="w-full p-3 sm:p-4 border-2 border-neutral-200 rounded-[1.5rem] sm:rounded-[2rem] bg-white h-[120px] sm:h-[150px] md:h-[184px] text-xs sm:text-sm md:text-base resize-none"
          placeholder="Describe the bug in detail. What happened? What did you expect to happen?"
        />
      </div>
    </>
  );
}; 