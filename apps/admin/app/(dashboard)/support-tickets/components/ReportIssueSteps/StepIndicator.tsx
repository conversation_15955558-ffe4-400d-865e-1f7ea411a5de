import React from 'react';

interface StepIndicatorProps {
  currentStep: number;
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep }) => {
  return (
    <div className="w-full mb-4 sm:mb-6">
      <div className="flex gap-2">
        <div className="h-2 flex-1 bg-gray-200 rounded-full overflow-hidden">
          <div
            className={`h-full ${currentStep >= 1 ? "bg-[#05A049]" : "bg-gray-200"} transition-all`}
          ></div>
        </div>
        <div className="h-2 flex-1 bg-gray-200 rounded-full overflow-hidden">
          <div
            className={`h-full ${currentStep >= 2 ? "bg-[#05A049]" : "bg-gray-200"} transition-all`}
          ></div>
        </div>
        <div className="h-2 flex-1 bg-gray-200 rounded-full overflow-hidden">
          <div
            className={`h-full ${currentStep >= 3 ? "bg-[#05A049]" : "bg-gray-200"} transition-all`}
          ></div>
        </div>
        <div className="h-2 flex-1 bg-gray-200 rounded-full overflow-hidden">
          <div
            className={`h-full ${currentStep >= 4 ? "bg-[#05A049]" : "bg-gray-200"} transition-all`}
          ></div>
        </div>
      </div>
    </div>
  );
}; 