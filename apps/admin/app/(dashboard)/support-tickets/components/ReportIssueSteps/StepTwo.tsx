import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Wren<PERSON> } from "lucide-react";
import { StepHeader } from './StepHeader';

interface StepTwoProps {
  bugType: string;
  setBugType: (bugType: string) => void;
}

export const StepTwo: React.FC<StepTwoProps> = ({ bugType, setBugType }) => {
  return (
    <>
      <StepHeader
        title="What type of issue is this?"
        description="Select the category that best describes your issue"
      />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <button
          className={`p-8 border-2 rounded-3xl text-left transition-all duration-200 hover:shadow-lg hover:cursor-pointer ${bugType === 'technical' ? 'border-green-600 bg-green-400/10' : ''}`}
          onClick={() => setBugType("technical")}
          type="button"
        >
          <div className="space-y-3">
            <div className="w-12 h-12 bg-orange-100 rounded-2xl flex items-center justify-center">
              <AlertTriangle size={24} className="text-orange-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900">Technical Support</h3>
            <p className="text-gray-600 font-normal">
              Performance problems, crashes, errors, or system-related issues
            </p>
          </div>
        </button>

        <button
          className={`p-8 border-2 rounded-3xl text-left transition-all duration-200 hover:shadow-lg hover:cursor-pointer ${bugType === 'product' ? 'border-green-600 bg-green-400/10' : ''}`}
          onClick={() => setBugType("product")}
          type="button"
        >
          <div className="space-y-3">
            <div className="w-12 h-12 bg-blue-100 rounded-2xl flex items-center justify-center">
              <Wrench size={24} className="text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900">Product Support</h3>
            <p className="text-gray-600 font-normal">
              Issues related to features, functionality, or user experience problems
            </p>
          </div>
        </button>
      </div>
    </>
  );
}; 