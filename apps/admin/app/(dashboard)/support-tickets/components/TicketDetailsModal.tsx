import React from "react";
import { <PERSON><PERSON> } from "@admin/components/ui/button";
import { Badge } from "@admin/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@admin/components/ui/dialog";
import { SupportTicket } from "@admin/app/lib/supportTicketService";
import { 
  Calendar, 
  User, 
  Mail, 
  FileText, 
  Image, 
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  Paperclip
} from "lucide-react";
import { getTimeAgo } from "@admin/app/lib/utils";

interface TicketDetailsModalProps {
  ticket: SupportTicket | null;
  isOpen: boolean;
  onClose: () => void;
}

export const TicketDetailsModal: React.FC<TicketDetailsModalProps> = ({
  ticket,
  isOpen,
  onClose,
}) => {
  if (!ticket) return null;

  const getStatusIcon = (status: string) => {
    switch (status.toUpperCase()) {
      case 'OPEN':
        return <AlertCircle className="h-4 w-4 text-blue-600" />;
      case 'IN_PROGRESS':
        return <Loader2 className="h-4 w-4 text-yellow-600" />;
      case 'RESOLVED':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'ESCALATED':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toUpperCase()) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-[#EDF8F2] text-[#05A049]';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'OPEN':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'RESOLVED':
        return 'bg-[#EDF8F2] text-[#05A049]';
      case 'ESCALATED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Ticket Details
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {ticket.title}
                </h3>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    <span>#{ticket.ticket_number}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>Created {getTimeAgo(ticket.created_at)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>Updated {getTimeAgo(ticket.updated_at)}</span>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Badge className={`text-sm rounded-full px-3 py-1 ${getPriorityColor(ticket.priority)}`}>
                  {ticket.priority.charAt(0) + ticket.priority.slice(1).toLowerCase()}
                </Badge>
                <Badge className={`text-sm rounded-full px-3 py-1 ${getStatusColor(ticket.status)}`}>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(ticket.status)}
                    {ticket.status.charAt(0) + ticket.status.slice(1).toLowerCase()}
                  </div>
                </Badge>
              </div>
            </div>
          </div>

          {/* User Information */}
          {ticket.user && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <User className="h-4 w-4" />
                Reporter Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Name</label>
                  <p className="text-sm text-gray-900">
                    {ticket.user.first_name} {ticket.user.last_name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                    <Mail className="h-4 w-4" />
                    Email
                  </label>
                  <p className="text-sm text-gray-900">{ticket.user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Ticket Details */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-3">Ticket Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Category</label>
                <p className="text-sm text-gray-900">{ticket.category}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Type</label>
                <p className="text-sm text-gray-900">{ticket.type}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Assignee Team</label>
                <p className="text-sm text-gray-900">
                  {ticket.assignee_team || 'Unassigned'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Ticket ID</label>
                <p className="text-sm text-gray-900 font-mono">{ticket.ticket_id}</p>
              </div>
            </div>
          </div>

          {/* Description */}
          {ticket.description && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-3">Description</h4>
              <p className="text-sm text-gray-700 whitespace-pre-wrap">
                {ticket.description}
              </p>
            </div>
          )}

          {/* Attachments */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                <Image className="h-4 w-4" />
                User Attachments ({ticket.support_ticket_docs?.length || 0})
              </h4>
            </div>

            {/* Existing Attachments */}
            {ticket.support_ticket_docs && ticket.support_ticket_docs.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {ticket.support_ticket_docs.map((doc, index) => (
                  <div key={doc.image_id} className="border border-gray-200 rounded-lg overflow-hidden">
                    <img
                      src={doc.image_url}
                      alt={`Attachment ${index + 1}`}
                      className="w-full h-32 object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02MCA4MEgxNDBWMTIwSDYwVjgwWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K';
                      }}
                    />
                    <div className="p-2">
                      <p className="text-xs text-gray-500">
                        Uploaded {getTimeAgo(doc.uploaded_at)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* No attachments message */}
            {(!ticket.support_ticket_docs || ticket.support_ticket_docs.length === 0) && (
              <div className="text-center py-8 text-gray-500">
                <Paperclip className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No attachments provided by user</p>
                <p className="text-sm">The user did not upload any documents with this ticket</p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button className="bg-[#05A049] hover:bg-[#048A3F]">
              Edit Ticket
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 