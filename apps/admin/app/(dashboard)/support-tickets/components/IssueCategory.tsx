"use client";

import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { Computer, Lock, Zap, Clock, Shield, HelpCircle } from "lucide-react";
import type { LucideIcon } from "lucide-react";
import { issueCategoriesData } from "../data";

const iconMap: { [key: string]: LucideIcon } = {
  Computer,
  Lock,
  Zap,
  Clock,
  Shield,
  HelpCircle,
};

export const IssueCategory = () => {
  return (
    <div className="bg-white rounded-xl p-6 mt-6 mb-2 shadow-sm border">
      <h3 className="font-bold text-lg mb-2">Issue Categories</h3>
      <p className="text-gray-500 text-sm mb-6">
        Distribution of user-reported issues by category
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {issueCategoriesData.map((category, index) => {
          const Icon = iconMap[category.icon];
          return (
            <Card key={index} className="p-4">
              <CardContent className="flex flex-col p-0">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-4">
                    {Icon && <Icon className="w-6 h-6 text-gray-500" />}
                    <div>
                      <h4 className="font-bold text-md">{category.title}</h4>
                      <p className="text-xs text-gray-500">
                        {category.description}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-center mt-4">
                  <p className="text-sm text-gray-600 font-semibold">
                    {category.total} total
                  </p>
                  {category.open > 0 && (
                    <Badge
                      variant="outline"
                      className="text-red-500 border-red-200 bg-red-50 font-semibold py-1 px-2"
                    >
                      {category.open} open
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
