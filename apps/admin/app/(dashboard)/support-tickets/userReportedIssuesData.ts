import { TechnicalSupportData } from "./types";
import { getUserReportedIcon } from "./UserReportedIcon";

export const userReportedIssuesData: TechnicalSupportData[] = [
  {
    title: "Mobile app sync issues",
    id: "USR-064",
    reporter: "<PERSON>",
    category: "Others",
    priority: "Medium",
    status: "Escalated",
    assignee: "Support Team",
    activity: "1 hour ago",
    comments: 2,
    attachments: 2,
    icon: getUserReportedIcon("Others"),
  },
  {
    title: "Help documentation links broken",
    id: "USR-069",
    reporter: "<PERSON>",
    category: "Others",
    priority: "High",
    status: "In Progress",
    assignee: "Support Team",
    activity: "6 hours ago",
    comments: 2,
    attachments: 1,
    icon: getUserReportedIcon("Others"),
  },
  {
    title: "Dashboard charts not displaying correctly on mobile",
    id: "USR-009",
    reporter: "<PERSON>",
    category: "UI/UX Issues",
    priority: "Critical",
    status: "Resolved",
    assignee: "Frontend Team",
    activity: "13 hours ago",
    comments: 3,
    attachments: 0,
    icon: getUserReportedIcon("UI/UX Issues"),
  },
  {
    title: "Help documentation links broken",
    id: "USR-068",
    reporter: "<PERSON>",
    category: "Others",
    priority: "Medium",
    status: "Escalated",
    assignee: "Support Team",
    activity: "13 hours ago",
    comments: 1,
    attachments: 1,
    icon: getUserReportedIcon("Others"),
  },
  {
    title: "Price alerts not triggering correctly",
    id: "USR-031",
    reporter: "Emma Wilson",
    category: "Real-time Data",
    priority: "Critical",
    status: "In Progress",
    assignee: "Data Team",
    activity: "19 hours ago",
    comments: 1,
    attachments: 0,
    icon: getUserReportedIcon("Real-time Data"),
  },
  {
    title: "Application freezes during transactions",
    id: "USR-043",
    reporter: "David Park",
    category: "Performance Issues",
    priority: "High",
    status: "In Progress",
    assignee: "DevOps Team",
    activity: "20 hours ago",
    comments: 4,
    attachments: 2,
    icon: getUserReportedIcon("Performance Issues"),
  },
  {
    title: "Mobile app sync issues",
    id: "USR-066",
    reporter: "Christopher Davis",
    category: "Others",
    priority: "Critical",
    status: "Open",
    assignee: "Support Team",
    activity: "1 day ago",
    comments: 6,
    attachments: 1,
    icon: getUserReportedIcon("Others"),
  },
];