"use client";
import React, { useState, useMemo } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { PlusIcon, RefreshCwIcon } from "lucide-react";
import { Card } from "@admin/components/ui/card";
import { SummaryCards } from "./components/SummaryCards";
import { SearchAndFilters } from "./components/SearchAndFilters";
import { TicketsTable } from "./components/TicketsTable";
import { RMWorkload } from "./components/RMWorkload";
import { NewTicketModal } from "./components/NewTicketModal";
import {
  rmWorkloadData,
  mapApiTicketsToTickets,
  generateSummaryFromTickets,
} from "./mockData";
import {
  Ticket,
  SummaryCard,
  StatusFilter,
  PriorityFilter,
  TicketTypeFilter,
} from "./types";
import { useTickets, useDeleteTicket } from "@admin/app/lib/hooks/api-hooks";

export const TicketsManagement = (): JSX.Element => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("All Status");
  const [priorityFilter, setPriorityFilter] = useState<PriorityFilter>("All Priority");
  const [ticketTypeFilter, setTicketTypeFilter] = useState<TicketTypeFilter>("New ticket");
  const [showNewTicketModal, setShowNewTicketModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState("");
  const [selectedProductType, setSelectedProductType] = useState("");
  const [selectedPriority, setSelectedPriority] = useState("");
  const [selectedRisk, setSelectedRisk] = useState("");
  const [selectedCurrency, setSelectedCurrency] = useState("");
  const [selectedRM, setSelectedRM] = useState("");

  // React Query hooks
  const { data: ticketsResponse, isLoading, error, refetch } = useTickets();
  const deleteTicketMutation = useDeleteTicket();

  // Transform API data to tickets
  const tickets = useMemo(() => {
    if (!ticketsResponse?.data?.tickets) return [];
    return mapApiTicketsToTickets(ticketsResponse.data.tickets);
  }, [ticketsResponse]);

  // Generate summary data from tickets
  const summaryData = useMemo(() => {
    return generateSummaryFromTickets(tickets);
  }, [tickets]);

  const handleDeleteTicket = async (ticketId: string) => {
    try {
      await deleteTicketMutation.mutateAsync(ticketId);
    } catch (err) {
      console.error("Error deleting ticket:", err);
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  // Filter tickets based on search term, status, and priority
  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearch = 
      ticket.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.product.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "All Status" || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === "All Priority" || ticket.priority === priorityFilter;
    
    // Debug logging
    if (statusFilter !== "All Status") {
      console.log("Filtering by status:", {
        statusFilter,
        ticketStatus: ticket.status,
        matchesStatus,
        ticketId: ticket.id
      });
    }
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Generate summary data based on filtered tickets
  const currentSummaryData = generateSummaryFromTickets(filteredTickets);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Tickets Management
            </h1>
            <p className="text-gray-600 text-sm">
              Manage structured product requests and RM assignments
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="text-white border-gray-300 hover:bg-gray-50 rounded-xl bg-green-600"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCwIcon className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {/* <Button
              className="bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] hover:bg-[#05A049]/90 text-white rounded-[8px]"
              onClick={() => setShowNewTicketModal(true)}
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              New Ticket
            </Button> */}
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          {/* Error Message */}
          {error && (
            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 text-sm">{error.message || 'An error occurred'}</p>
            </div>
          )}

          {/* Summary Cards */}
          <SummaryCards data={summaryData} />

          {/* Search and Filters */}
          <SearchAndFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            priorityFilter={priorityFilter}
            onPriorityFilterChange={setPriorityFilter}
            ticketTypeFilter={ticketTypeFilter}
            onTicketTypeFilterChange={setTicketTypeFilter}
            ticketCount={filteredTickets.length}
          />

          {/* Tickets Table */}
          {isLoading ? (
            <Card className="mb-6 p-12 bg-white">
              <div className="text-center">
                <RefreshCwIcon className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Loading tickets...</p>
              </div>
            </Card>
          ) : (
            <>
              <TicketsTable 
                tickets={filteredTickets} 
                onDeleteTicket={handleDeleteTicket} 
                onTicketUpdated={handleRefresh}
              />

              <RMWorkload data={rmWorkloadData} />
            </>
          )}
        </div>
      </div>

      <NewTicketModal
        isOpen={showNewTicketModal}
        onClose={() => setShowNewTicketModal(false)}
        selectedCustomer={selectedCustomer}
        setSelectedCustomer={setSelectedCustomer}
        selectedProductType={selectedProductType}
        setSelectedProductType={setSelectedProductType}
        selectedPriority={selectedPriority}
        setSelectedPriority={setSelectedPriority}
        selectedRisk={selectedRisk}
        setSelectedRisk={setSelectedRisk}
        selectedCurrency={selectedCurrency}
        setSelectedCurrency={setSelectedCurrency}
        selectedRM={selectedRM}
        setSelectedRM={setSelectedRM}
      />
    </div>
  );
};
