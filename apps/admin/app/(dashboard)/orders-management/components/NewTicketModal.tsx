import React, { useState } from "react";
import { Button } from "@admin/components/ui/button";
import { X, Calendar, ChevronDown } from "lucide-react";

interface NewTicketModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
  selectedCustomer: string;
  setSelectedCustomer: (value: string) => void;
  selectedProductType: string;
  setSelectedProductType: (value: string) => void;
  selectedPriority: string;
  setSelectedPriority: (value: string) => void;
  selectedRisk: string;
  setSelectedRisk: (value: string) => void;
  selectedCurrency: string;
  setSelectedCurrency: (value: string) => void;
  selectedRM: string;
  setSelectedRM: (value: string) => void;
}

export const NewTicketModal: React.FC<NewTicketModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  selectedCustomer,
  setSelectedCustomer,
  selectedProductType,
  setSelectedProductType,
  selectedPriority,
  setSelectedPriority,
  selectedRisk,
  setSelectedRisk,
  selectedCurrency,
  setSelectedCurrency,
  selectedRM,
  setSelectedRM,
}) => {
  const [formData, setFormData] = useState({
    investmentAmount: "",
    quantity: "",
    maturityDate: "",
    currency: "USD - US Dollar",
  });

  if (!isOpen) return null;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit?.(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl w-full max-w-md relative overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-[#05A049]">
            Invest in Autocallable
          </h2>
          <button
            className="text-gray-400 hover:text-gray-600 transition-colors"
            onClick={onClose}
            aria-label="Close"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Investment Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Investment Amount
            </label>
            <div className="relative">
              <input
                type="number"
                value={formData.investmentAmount}
                onChange={(e) => handleInputChange("investmentAmount", e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                placeholder="Enter amount"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="flex flex-col">
                  <button type="button" className="text-gray-400 hover:text-gray-600 text-xs">▲</button>
                  <button type="button" className="text-gray-400 hover:text-gray-600 text-xs">▼</button>
                </div>
              </div>
            </div>
          </div>

          {/* Quantity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity
            </label>
            <input
              type="number"
              value={formData.quantity}
              onChange={(e) => handleInputChange("quantity", e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
              placeholder="Enter quantity"
            />
          </div>

          {/* Maturity Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Maturity Date
            </label>
            <div className="relative">
              <input
                type="text"
                value={formData.maturityDate}
                onChange={(e) => handleInputChange("maturityDate", e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                placeholder="dd-mm-yyyy"
              />
              <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>
          </div>

          {/* Currency */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Currency
            </label>
            <div className="relative">
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange("currency", e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] appearance-none bg-white"
              >
                <option value="USD - US Dollar">USD - US Dollar</option>
                <option value="EUR - Euro">EUR - Euro</option>
                <option value="GBP - British Pound">GBP - British Pound</option>
                <option value="JPY - Japanese Yen">JPY - Japanese Yen</option>
                <option value="SGD - Singapore Dollar">SGD - Singapore Dollar</option>
                <option value="AUD - Australian Dollar">AUD - Australian Dollar</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {/* Investment Summary */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <h3 className="font-semibold text-gray-900">Investment Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Yield:</span>
                <span className="font-semibold text-[#05A049]">14.01% P.A.</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Min. Investment:</span>
                <span className="font-semibold text-gray-900">USD 100k</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Duration:</span>
                <span className="font-semibold text-gray-900">13 Months</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              onClick={onClose}
              className="flex-1 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 rounded-lg py-3"
            >
              Discard
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-[#05A049] hover:bg-[#05A049]/90 text-white rounded-lg py-3"
            >
              Buy
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
