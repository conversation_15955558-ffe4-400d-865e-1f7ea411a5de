import React from "react";
import { Input } from "@admin/components/ui/input";
import { Card } from "@admin/components/ui/card";
import { StatusFilter, PriorityFilter, TicketTypeFilter } from "../types";

interface SearchAndFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  statusFilter: StatusFilter;
  onStatusFilterChange: (value: StatusFilter) => void;
  priorityFilter: PriorityFilter;
  onPriorityFilterChange: (value: PriorityFilter) => void;
  ticketTypeFilter: TicketTypeFilter;
  onTicketTypeFilterChange: (value: TicketTypeFilter) => void;
  ticketCount: number;
}
const selectStyles = {
  appearance: "none" as const,
  backgroundImage: "none",
  paddingRight: "2.5rem",
};

export const SearchAndFilters: React.FC<SearchAndFiltersProps> = ({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilter<PERSON>hange,
  priorityFilter,
  onPriorityFilterChange,
  ticketTypeFilter,
  onTicketTypeFilterChange,
  ticketCount,
}) => {
  return (
    <Card className="p-4 bg-white mb-6">
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Input
            placeholder="Search tickets..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-12 border border-gray-200 p-5 rounded-[10px] relative"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => onStatusFilterChange(e.target.value as StatusFilter)}
          className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
          style={selectStyles}
        >
          <option>All Status</option>
          <option>New</option>
          <option>AssignedToRM</option>
          <option>QuoteRequested</option>
          <option>QuoteReceived</option>
          <option>Approved</option>
          <option>Executed</option>
          <option>Rejected</option>
          <option>Cancelled</option>
          <option>Archived</option>
        </select>
        <select
          value={priorityFilter}
          onChange={(e) =>
            onPriorityFilterChange(e.target.value as PriorityFilter)
          }
          className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
          style={selectStyles}
        >
          <option>All Priority</option>
          <option>HIGH</option>
          <option>MEDIUM</option>
          <option>LOW</option>
          <option>Unassigned</option>
        </select>

        <span className="text-sm text-gray-500 whitespace-nowrap">
          {ticketCount} tickets found
        </span>
      </div>
    </Card>
  );
};
