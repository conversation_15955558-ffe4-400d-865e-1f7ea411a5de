import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { BadgeEuro, UserIcon } from "lucide-react";
import { RMWorkload as RMWorkloadType } from "../types";
import { Badge } from "@admin/components/ui/badge";

interface RMWorkloadProps {
  data: RMWorkloadType[];
}

export const RMWorkload: React.FC<RMWorkloadProps> = ({ data }) => {
  return (
    <Card className="bg-white">
      <div className="border-b p-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Relationship Manager Workload
        </h3>
      </div>
      <CardContent className="p-4">
        <div className="p-6 rounded-lg">
          <div className="grid grid-cols-4 gap-6">
            {data.map((rm, index) => (
              <div key={index} className=" p-4 border rounded-[8px]">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <UserIcon className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-gray-900 text-sm leading-tight">
                      {rm.name}
                    </h4>
                    <p className="text-xs text-blue-600 mt-1">{rm.role}</p>
                    <div className="flex justify-between mt-3">
                      <p className="text-xs text-gray-500">Active Tickets:</p>
                      <Badge className="rounded-full bg-red-100">
                        <span className={`text-xs font-bold `}>
                          {rm.activeTickets}
                        </span>
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
