import React from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { Badge } from "@admin/components/ui/badge";
import { X, UserIcon, CalendarIcon, DollarSignIcon, PackageIcon, AlertTriangleIcon } from "lucide-react";
import { Ticket } from "../types";

interface OrderSummaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  ticket: Ticket | null;
}

export const OrderSummaryModal: React.FC<OrderSummaryModalProps> = ({
  isOpen,
  onClose,
  ticket,
}) => {
  if (!isOpen || !ticket) return null;

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "QuoteReceived":
        return "bg-blue-100 text-blue-800";
      case "AssignedToRM":
        return "bg-yellow-100 text-yellow-800";
      case "Approved":
        return "bg-[#EDF8F2] text-[#05A049]";
      case "New":
        return "bg-purple-100 text-purple-800";
      case "Archived":
        return "bg-orange-100 text-orange-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-[#EDF8F2] text-[#05A049]";
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case "HIGH":
        return "bg-red-100 text-red-800";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800";
      case "LOW":
        return "bg-[#EDF8F2] text-[#05A049]";
      case "Unassigned":
        return "bg-gray-100 text-gray-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const getRiskLevel = () => {
    if (ticket.riskHigh) return { level: "High Risk", color: "bg-red-100 text-red-800" };
    if (ticket.riskMedium) return { level: "Medium Risk", color: "bg-yellow-100 text-yellow-800" };
    if (ticket.riskLow) return { level: "Low Risk", color: "bg-[#EDF8F2] text-[#05A049]" };
    return { level: "Not Specified", color: "bg-gray-100 text-gray-600" };
  };

  const riskInfo = getRiskLevel();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto relative shadow-2xl">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 sticky top-0 bg-white z-10">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Order Summary</h2>
            <p className="text-sm text-gray-600 mt-1">Ticket ID: {ticket.id}</p>
          </div>
          <button
            className="text-gray-400 hover:text-gray-600 transition-colors"
            onClick={onClose}
            aria-label="Close"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Order Status Section */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-[#05A049] rounded-full flex items-center justify-center">
                  <PackageIcon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Order Status</h3>
                  <p className="text-sm text-gray-600">Current order status and priority</p>
                </div>
              </div>
              <div className="flex gap-2">
                <Badge className={`${getStatusBadgeColor(ticket.status)} text-xs rounded-full`}>
                  {ticket.status}
                </Badge>
                <Badge className={`${getPriorityBadgeColor(ticket.priority)} text-xs rounded-full`}>
                  {ticket.priority}
                </Badge>
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                <UserIcon className="w-5 h-5 text-[#05A049]" />
                Customer Information
              </h3>
              <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">Customer Name</p>
                  <p className="text-gray-900">{ticket.customer}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Risk Level</p>
                  <Badge className={`${riskInfo.color} text-xs rounded-full`}>
                    {riskInfo.level}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Product Information */}
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                <PackageIcon className="w-5 h-5 text-[#05A049]" />
                Product Information
              </h3>
              <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">Product</p>
                  <p className="text-gray-900">{ticket.product}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Investment Amount</p>
                  <p className="text-gray-900 font-semibold">{ticket.amount}</p>
                </div>
              </div>
            </div>
          </div>

          {/* RM Assignment */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
              <UserIcon className="w-5 h-5 text-[#05A049]" />
              Relationship Manager Assignment
            </h3>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              {ticket.rmAssigned === "Unassigned" ? (
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    <AlertTriangleIcon className="w-5 h-5 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-gray-900 font-medium">Unassigned</p>
                    <p className="text-sm text-gray-600">No RM has been assigned to this order</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-50 rounded-full flex items-center justify-center">
                    <UserIcon className="w-5 h-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-gray-900 font-medium">{ticket.rmAssigned}</p>
                    <p className="text-sm text-gray-600">Assigned Relationship Manager</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Order Timeline */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
              <CalendarIcon className="w-5 h-5 text-[#05A049]" />
              Order Timeline
            </h3>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-700">Last Updated</p>
                  <p className="text-gray-900">{ticket.lastUpdate}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-700">Created</p>
                  <p className="text-gray-900">{ticket.lastUpdate}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              onClick={onClose}
              className="flex-1 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 rounded-lg py-3"
            >
              Close
            </Button>
            <Button
              type="button"
              className="flex-1 bg-[#05A049] hover:bg-[#05A049]/90 text-white rounded-lg py-3"
            >
              Edit Order
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}; 