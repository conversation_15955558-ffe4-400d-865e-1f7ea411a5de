import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@admin/components/ui/dialog";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { Badge } from "@admin/components/ui/badge";
import { Loader2, CalendarIcon, DollarSignIcon, UserIcon, CheckCircle, AlertCircle } from "lucide-react";
import { useTicketDetail, useUpdateTicket } from "@admin/app/lib/hooks/api-hooks";

// Define the API response interface based on the provided data structure
interface TicketDetailResponse {
  statusCode: number;
  message: string;
  data: {
    ticket_id: string;
    user_id: string;
    rm_id: string | null;
    product_id: string;
    priority_level: string | null;
    currency: string;
    maturity_date: string;
    amount: number;
    status: string;
    assignment_notes: string | null;
    created_at: string;
    resolved_at: string | null;
    updated_at: string;
    product: {
      product_id: string;
      vendor_id: string;
      issuer_id: string | null;
      name: string;
      type: string;
      product_type: string;
      risk_level: string;
      description: string | null;
      availability: string;
      minimum_investment: number | null;
      currency: string;
      created_at: string;
      updated_at: string;
    };
    user: {
      first_name: string;
      last_name: string;
      email: string;
    };
  };
}

interface EditTicketDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ticketId: string | null;
  onTicketUpdated?: () => void;
}

export const EditTicketDialog: React.FC<EditTicketDialogProps> = ({
  open,
  onOpenChange,
  ticketId,
  onTicketUpdated,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    priority_level: "",
    status: "",
    assignment_notes: "",
    rm_id: "",
  });
  const [hasChanges, setHasChanges] = useState(false);

  // React Query hooks
  const { data: ticketResponse, isLoading, error: fetchError } = useTicketDetail(ticketId);
  const updateTicketMutation = useUpdateTicket();

  const ticketData = ticketResponse?.data;

  // Initialize form data when ticket data is loaded
  useEffect(() => {
    if (ticketData) {
      setFormData({
        priority_level: ticketData.priority_level || "",
        status: ticketData.status || "",
        assignment_notes: ticketData.assignment_notes || "",
        rm_id: ticketData.rm_id || "",
      });
    }
  }, [ticketData]);

  // Track form changes
  useEffect(() => {
    if (ticketData) {
      const originalData = {
        priority_level: ticketData.priority_level || "",
        status: ticketData.status || "",
        assignment_notes: ticketData.assignment_notes || "",
        rm_id: ticketData.rm_id || "",
      };
      
      const hasChanged = Object.keys(formData).some(
        (key) => formData[key as keyof typeof formData] !== originalData[key as keyof typeof originalData]
      );
      setHasChanges(hasChanged);
    }
  }, [formData, ticketData]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    if (!ticketId) return;
    
    setError(null);
    setSuccess(false);

    try {
      // Prepare the data for the API call
      const updateData = {
        status: formData.status,
        priority: formData.priority_level || null,
        note: formData.assignment_notes || null,
        rm_id: formData.rm_id || null,
      };

      console.log("Updating ticket with data:", {
        ticketId,
        updateData,
      });

      // Make the API call to update the ticket
      await updateTicketMutation.mutateAsync({ ticketId, data: updateData });
      
      setSuccess(true);
      
      // Call the callback to refetch tickets data
      onTicketUpdated?.();
      
      // Auto close dialog after success
      setTimeout(() => {
        onOpenChange(false);
        setSuccess(false);
      }, 2000);
      
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to update ticket. Please try again.");
      console.error("Error updating ticket:", err);
    }
  };

  // Handle loading and error states
  const displayError = error || (fetchError ? 'Failed to fetch ticket details' : null);
  const isMutating = updateTicketMutation.isPending;
  const isDataLoading = isLoading;

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getPriorityColor = (priority: string | null) => {
    switch (priority?.toUpperCase()) {
      case "HIGH":
        return "bg-red-100 text-red-800";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800";
      case "LOW":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "New":
        return "bg-blue-100 text-blue-800";
      case "Assigned to RM":
        return "bg-yellow-100 text-yellow-800";
      case "Quote Received":
        return "bg-purple-100 text-purple-800";
      case "Approved":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="rounded-[30px] w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-white">
        {/* Loading Overlay */}
        {(isDataLoading || isMutating) && (
          <div className="absolute inset-0 bg-white/90 rounded-[30px] z-50 flex items-center justify-center">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-[#05A049]" />
              <p className="text-sm text-gray-600">
                {ticketData ? "Updating ticket..." : "Loading ticket details..."}
              </p>
            </div>
          </div>
        )}

        {/* Success Overlay */}
        {success && (
          <div className="absolute inset-0 bg-white/95 rounded-[30px] z-50 flex items-center justify-center">
            <div className="flex flex-col items-center space-y-4 text-center px-6">
              <div className="bg-green-100 rounded-full p-3">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-green-600 mb-2">
                  Ticket Updated Successfully!
                </h3>
                <p className="text-sm text-gray-600">
                  Your ticket changes have been saved.
                  <br />
                  You will receive a confirmation shortly.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Overlay */}
        {error && (
          <div className="absolute inset-0 bg-white/95 rounded-[30px] z-50 flex items-center justify-center">
            <div className="flex flex-col items-center space-y-4 text-center px-6">
              <div className="bg-red-100 rounded-full p-3">
                <AlertCircle className="h-12 w-12 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-red-600 mb-2">
                  Update Failed
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {error}
                </p>
                <Button
                  onClick={() => setError(null)}
                  className="bg-red-600 hover:bg-red-700 text-white rounded-[15px]"
                >
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        )}

        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-[#05A049] text-center">
            Edit Ticket {ticketData?.ticket_id}
          </DialogTitle>
        </DialogHeader>

        {ticketData && (
          <div className="space-y-6 py-6">
            {/* Ticket Overview Cards */}
            <div className="bg-gray-50 p-4 rounded-[15px] space-y-3">
              <h4 className="font-semibold text-gray-800 mb-2">Ticket Information</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Customer:</span>
                  <span className="font-semibold text-[#05A049]">
                    {ticketData.user.first_name} {ticketData.user.last_name}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-semibold text-[#05A049]">
                    {formatCurrency(ticketData.amount, ticketData.currency)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Product:</span>
                  <span className="font-semibold">{ticketData.product.name}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Risk Level:</span>
                  <Badge className={getPriorityColor(ticketData.product.risk_level)}>
                    {ticketData.product.risk_level}
                  </Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Maturity Date:</span>
                  <span className="font-semibold">{formatDate(ticketData.maturity_date)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Created:</span>
                  <span className="font-semibold">{formatDate(ticketData.created_at)}</span>
                </div>
              </div>
            </div>

            {/* Editable Fields */}
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Priority Level</label>
                <select
                  value={formData.priority_level}
                  onChange={(e) => handleInputChange("priority_level", e.target.value)}
                  className="w-full h-[40px] px-3 border border-gray-300 rounded-[15px] focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                  disabled={isDataLoading || isMutating}
                >
                  <option value="">Unassigned</option>
                  <option value="LOW">Low</option>
                  <option value="MEDIUM">Medium</option>
                  <option value="HIGH">High</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Status</label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange("status", e.target.value)}
                  className="w-full h-[40px] px-3 border border-gray-300 rounded-[15px] focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                  disabled={isDataLoading || isMutating}
                >
                  <option value="New">New</option>
                  <option value="Archived">Archived</option>
                  <option value="AssignedToRM">Assigned to RM</option>
                  <option value="QuoteRequested">Quote Requested</option>
                  <option value="QuoteReceived">Quote Received</option>
                  <option value="Approved">Approved</option>
                  <option value="Executed">Executed</option>
                  <option value="Rejected">Rejected</option>
                  <option value="Cancelled">Cancelled</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Relationship Manager ID</label>
                <Input
                  value={formData.rm_id}
                  onChange={(e) => handleInputChange("rm_id", e.target.value)}
                  placeholder="Enter RM ID"
                  className="rounded-[15px] border-gray-300 focus:border-[#05A049] focus:ring-[#05A049]"
                  disabled={isDataLoading || isMutating}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Assignment Notes</label>
                <textarea
                  value={formData.assignment_notes}
                  onChange={(e) => handleInputChange("assignment_notes", e.target.value)}
                  placeholder="Add any notes about this ticket assignment..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-[15px] focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] resize-none"
                  disabled={isDataLoading || isMutating}
                />
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="flex gap-3 pt-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 rounded-[15px] border-gray-300 hover:bg-gray-50"
            disabled={isMutating}
          >
            Cancel
          </Button>
          {hasChanges && (
            <Button
              onClick={handleSave}
              className="flex-1 rounded-[15px] bg-[#05A049] hover:bg-green-700 text-white"
              disabled={isMutating}
            >
              {isMutating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
