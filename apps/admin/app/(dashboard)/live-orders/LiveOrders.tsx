"use client";
import React, { useState, useMemo } from "react";
import { Button } from "@admin/components/ui/button";
import { Input } from "@admin/components/ui/input";
import { SearchIcon, DownloadIcon, RefreshCwIcon } from "lucide-react";
import { SummaryCards } from "./components/SummaryCards";
import { OrdersTable } from "./components/OrdersTable";
import { BrokerStatus } from "./components/BrokerStatus";
import { brokerData, mapApiOrdersToOrders, ApiOrderResponse } from "./data";
import { Order } from "./types";
import { Card } from "@admin/components/ui/card";
import { useOrders } from "@admin/app/lib/hooks/api-hooks";

// Add custom CSS for select elements
const selectStyles = {
  appearance: "none" as const,
  backgroundImage: "none",
  paddingRight: "2.5rem",
};

export const LiveOrders = (): JSX.Element => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All Status");
  const [brokerFilter, setBrokerFilter] = useState("All Vendors");

  // React Query hooks
  const { data: ordersResponse, isLoading, error, refetch } = useOrders();

  // Transform API data to orders
  const ordersData = useMemo(() => {
    if (!ordersResponse?.data?.orders) return [];
    return mapApiOrdersToOrders(ordersResponse.data.orders);
  }, [ordersResponse]);

  const handleRefresh = () => {
    refetch();
  };

  // Filter orders based on search term, status, and broker
  const filteredOrders = ordersData.filter((order) => {
    const matchesSearch = 
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.symbol.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "All Status" || order.status === statusFilter;
    const matchesBroker = brokerFilter === "All Vendors" || order.broker === brokerFilter;
    
    return matchesSearch && matchesStatus && matchesBroker;
  });

  // Generate dynamic summary data based on filtered orders
  const generateSummaryData = () => {
    const totalOrders = filteredOrders.length;
    const settledOrders = filteredOrders.filter(order => order.status === "Settled" || order.status === "Filled").length;
    const pendingOrders = filteredOrders.filter(order => order.status === "Placed" || order.status === "Partially Filled").length;
    const failedOrders = filteredOrders.filter(order => order.status === "Failed").length;

    return [
      {
        title: "Total Orders",
        value: totalOrders.toString(),
        icon: "trending-up",
        color: "text-blue-600",
      },
      {
        title: "Executed",
        value: settledOrders.toString(),
        icon: "circle-check-big",
        color: "text-green-600",
      },
      {
        title: "Pending",
        value: pendingOrders.toString(),
        icon: "clock",
        color: "text-yellow-600",
      },
      {
        title: "Failed",
        value: failedOrders.toString(),
        icon: "triangle-alert",
        color: "text-red-600",
      },
    ];
  };


  return (
    <div className="w-full h-full  overflow-y-auto">
      {/* Header */}
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900"> Auto Executed Orders</h1>
            <p className="text-gray-600 text-sm">
              Monitor stock orders and broker connectivity
            </p>
          </div>
          <div className="flex gap-2">
            <Button className="border border-gray-300 bg-white hover:bg-gray-50 text-black rounded-[8px]">
              <DownloadIcon className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button className="admin_green_gradient hover:admin_green_gradient_hover rounded-[8px] text-white" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCwIcon className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Loading...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Error Message */}
        {error && (
          <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">{error.message || 'An error occurred'}</p>
          </div>
        )}

        {/* Summary Cards */}
        <SummaryCards data={generateSummaryData()} />

        {/* Search and Filters */}
        <div className="">
          <Card className="mb-6 p-6 bg-white">
            <div className="flex gap-4 mb-6">
              <div className="relative flex-1">
                <Input
                  placeholder="Search orders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 border border-gray-200 p-5 rounded-[10px] relative"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
                style={selectStyles}
              >
                <option>All Status</option>
                <option>Settled</option>
                <option>Filled</option>
                <option>Partially Filled</option>
                <option>Failed</option>
                <option>Placed</option>
              </select>
              <select
                value={brokerFilter}
                onChange={(e) => setBrokerFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
                style={selectStyles}
              >
                <option>All Vendors</option>
                <option>Interactive Brokers</option>
                <option>Schwab</option>
                <option>Fidelity</option>
                <option>TD Ameritrade</option>
                <option>E*TRADE</option>
              </select>
              <span className="text-sm text-gray-500 flex items-center">
                {filteredOrders.length} orders found
              </span>
            </div>
          </Card>
        </div>

        {/* Orders Table */}
        {isLoading ? (
          <Card className="mb-6 p-12 bg-white">
            <div className="text-center">
              <RefreshCwIcon className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Loading orders...</p>
            </div>
          </Card>
        ) : filteredOrders.length > 0 ? (
          <OrdersTable orders={filteredOrders} />
        ) : (
          <Card className="mb-6 p-12 bg-white">
            <div className="text-center">
              <div className="mx-auto w-24 h-24 mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                <SearchIcon className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Orders Found</h3>
              <p className="text-gray-500 mb-4">
                {ordersData.length === 0 
                  ? "No orders have been placed yet." 
                  : "No orders match your current search criteria."}
              </p>
              {ordersData.length > 0 && (searchTerm || statusFilter !== "All Status" || brokerFilter !== "All Vendors") && (
                <Button 
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("All Status");
                    setBrokerFilter("All Vendors");
                  }}
                  className="rounded-[8px]"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </Card>
        )}

        {/* Broker Status */}
        <BrokerStatus brokers={brokerData} />
      </div>
    </div>
  );
};
