import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { Badge } from "@admin/components/ui/badge";
import { Order } from "../types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@admin/components/ui/table";

interface OrdersTableProps {
  orders: Order[];
}

export const OrdersTable: React.FC<OrdersTableProps> = ({ orders }) => {
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Settled":
        return "bg-[#EDF8F2] text-[#05A049]";
      case "Filled":
        return "bg-blue-100 text-blue-800";
      case "Partially Filled":
        return "bg-yellow-100 text-yellow-800";
      case "Failed":
        return "bg-red-100 text-red-800";
      case "Placed":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card className="bg-white mb-6 rounded-xl">
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-gray-50 rounded-t-xl">
              <TableRow className="border-none">
                <TableHead className="text-sm font-medium text-gray-700 rounded-tl-xl px-6 py-4">
                  ORDER ID
                </TableHead>
                <TableHead className="text-sm font-medium text-gray-700 px-6 py-4">
                  CUSTOMER & SYMBOL
                </TableHead>
                <TableHead className="text-sm font-medium text-gray-700 px-6 py-4">
                  TYPE & QUANTITY
                </TableHead>
                <TableHead className="text-sm font-medium text-gray-700 px-6 py-4">
                  PRICE
                </TableHead>
                <TableHead className="text-sm font-medium text-gray-700 px-6 py-4">
                  STATUS
                </TableHead>
                <TableHead className="text-sm font-medium text-gray-700 px-6 py-4">
                  BROKER
                </TableHead>
                <TableHead className="text-sm font-medium text-gray-700 px-6 py-4">
                  VALUE
                </TableHead>
                <TableHead className="text-sm font-medium text-gray-700 rounded-tr-xl px-6 py-4">
                  SETTLEMENT
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => (
                <TableRow key={order.id} className="hover:bg-gray-50">
                  <TableCell className="px-6 py-4">
                    <div className="space-y-1">
                      <span className="font-medium text-gray-900">
                        {order.id}
                      </span>
                      <p className="text-xs text-gray-500">{order.timestamp}</p>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <div className="space-y-1">
                      <p className="font-medium text-gray-900">
                        {order.customer}
                      </p>
                      <p className="text-sm text-gray-600">{order.symbol}</p>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <div className="space-y-1">
                      <p className="font-medium text-gray-900">{order.type}</p>
                      <p className="text-sm text-gray-600">{order.quantity}</p>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <span className="font-medium text-gray-900">
                      {order.price}
                    </span>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <Badge
                      className={`${getStatusBadgeColor(order.status)} text-xs rounded-full px-3 py-1`}
                    >
                      {order.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <div className="space-y-1">
                      <p className="font-medium text-gray-900">
                        {order.broker}
                      </p>
                      <p className="text-xs text-gray-500">Fee: {order.fee}</p>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <span className="font-medium text-gray-900">
                      {order.value}
                    </span>
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <span className="text-sm text-gray-600">
                      {order.settlement}
                    </span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};
