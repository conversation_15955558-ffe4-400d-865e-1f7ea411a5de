import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { BrokerStatus as BrokerStatusType } from "../types";

interface BrokerStatusProps {
  brokers: BrokerStatusType[];
}

export const BrokerStatus: React.FC<BrokerStatusProps> = ({ brokers }) => {
  return (
    <Card className="bg-white">
      <div className="border-b p-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Vendor Connectivity Status
        </h3>
      </div>
      <CardContent className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {brokers.map((broker, index) => (
            <div key={index} className="text-left p-4 border rounded-[8px]">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900">{broker.name}</span>
                <div
                  className={`w-3 h-3 ${broker.statusColor} rounded-full`}
                ></div>
              </div>
              <p className="text-sm text-gray-600 mb-1">{broker.status}</p>
              <p className="text-xs text-gray-500">{broker.orders}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
