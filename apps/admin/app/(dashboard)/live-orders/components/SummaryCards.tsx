import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { SummaryCard } from "../types";
import { TrendingUp, CircleCheckBig, Clock, TriangleAlert } from "lucide-react";

interface SummaryCardsProps {
  data: SummaryCard[];
}

const iconMap = {
  "trending-up": TrendingUp,
  "circle-check-big": CircleCheckBig,
  clock: Clock,
  "triangle-alert": TriangleAlert,
};

export const SummaryCards: React.FC<SummaryCardsProps> = ({ data }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      {data.map((item, index) => {
        const IconComponent = iconMap[item.icon as keyof typeof iconMap];
        const bgColor = item.color
          .replace("text-", "bg-")
          .replace("-600", "-100");
        return (
          <Card key={index} className="bg-white">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                {IconComponent && (
                  <div
                    className={`p-2 w-12 h-12 rounded-xl ${bgColor} flex items-center justify-center`}
                  >
                    <IconComponent className={`text-2xl ${item.color}`} />
                  </div>
                )}
                <div>
                  <p className="text-sm text-gray-600">{item.title}</p>
                  <p className={`text-2xl font-bold ${item.color}`}>
                    {item.value}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
