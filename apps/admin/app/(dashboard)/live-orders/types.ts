import { ReactNode } from "react";

export interface Order {
  id: string;
  timestamp: string;
  customer: string;
  symbol: string;
  type: string;
  quantity: string;
  price: string;
  status: OrderStatus;
  broker: string;
  fee: string;
  value: string;
  settlement: string;
}

export type OrderStatus =
  | "Settled"
  | "Filled"
  | "Partially Filled"
  | "Failed"
  | "Placed";

export interface SummaryCard {
  title: string;
  value: string;
  icon: ReactNode;
  color: string;
}

export interface BrokerStatus {
  name: string;
  status: string;
  orders: string;
  statusColor: string;
}
