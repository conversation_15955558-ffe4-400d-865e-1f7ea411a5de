import { Order, SummaryCard, BrokerStatus } from "./types";
import { <PERSON><PERSON><PERSON>Up, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Alert } from "lucide-react";

// API Response Interface
export interface ApiOrderResponse {
  id: string;
  user: {
    name: string;
  };
  product: {
    name: string;
    type: string;
  };
  vendor: {
    name: string;
    type: string;
  };
  quantity: number;
  price: number;
  status: string;
  created_at: string;
  commission_fees: number | null;
  settled_at: string | null;
}

// Mapper function to convert API response to Order format
export const mapApiOrderToOrder = (apiOrder: ApiOrderResponse): Order => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).replace(',', '');
  };

  const formatSettlement = (settledAt: string | null, status: string) => {
    if (settledAt) {
      return formatDate(settledAt).split(' ')[0]; // Just the date part
    }
    return status === "Settled" ? formatDate(apiOrder.created_at).split(' ')[0] : "Pending";
  };

  const formatPrice = (price: number) => {
    const numPrice = Number(price);
    if (isNaN(numPrice) || numPrice <= 0) return "$0.00";
    if (numPrice >= 1_000_000) {
      return `$${(numPrice / 1_000_000).toFixed(2)}M`;
    }
    if (numPrice >= 1_000) {
      return `$${(numPrice / 1_000).toFixed(2)}k`;
    }
    return `$${numPrice?.toFixed(2)}`;
  };

  const formatValue = (quantity: number) => {
    const numQuantity = Number(quantity);
    const numPrice = Number(apiOrder.price);
    const totalValue = numQuantity * numPrice;
    if (isNaN(totalValue) || totalValue <= 0) return "$0.00";
    if (totalValue >= 1_000_000) {
      return `$${(totalValue / 1_000_000).toFixed(2)}M`;
    }
    if (totalValue >= 1_000) {
      return `$${(totalValue / 1_000).toFixed(2)}k`;
    }
    return `$${totalValue?.toFixed(2)}`;
  };

  const formatFee = (commissionFees: number | null) => {
    if (commissionFees === null) return "$0.00";
    return `$${commissionFees.toFixed(2)}`;
  };

  return {
    id: apiOrder.id,
    timestamp: formatDate(apiOrder.created_at),
    customer: apiOrder.user.name,
    symbol: apiOrder.product.name,
    type: `${apiOrder.product.type} Order`, // Combining product type as order type
    quantity: `${apiOrder.quantity} units`,
    price: formatPrice(apiOrder.price),
    status: apiOrder.status as Order['status'],
    broker: apiOrder.vendor.name,
    fee: formatFee(apiOrder.commission_fees),
    value: formatValue(apiOrder.quantity),
    settlement: formatSettlement(apiOrder.settled_at, apiOrder.status),
  };
};

// Helper function to map multiple API orders
export const mapApiOrdersToOrders = (apiOrders: ApiOrderResponse[] | undefined | null): Order[] => {
  if (!Array.isArray(apiOrders)) return [];
  return apiOrders.map(mapApiOrderToOrder);
};

export const ordersData: Order[] = [
  {
    id: "ORD-10001",
    timestamp: "2024-06-15 09:15",
    customer: "Alice Chen",
    symbol: "AAPL",
    type: "Market Buy",
    quantity: "100 shares",
    price: "$185.42",
    status: "Settled",
    broker: "Interactive Brokers",
    fee: "$1.00",
    value: "$18,542.00",
    settlement: "2024-06-17",
  },
  {
    id: "ORD-10002",
    timestamp: "2024-06-15 10:32",
    customer: "Robert Kim",
    symbol: "TSLA",
    type: "Limit Buy",
    quantity: "50 shares",
    price: "$239.85",
    status: "Filled",
    broker: "Schwab",
    fee: "$0.00",
    value: "$11,992.50",
    settlement: "2024-06-17",
  },
  {
    id: "ORD-10003",
    timestamp: "2024-06-15 11:22",
    customer: "Sarah Wilson",
    symbol: "NVDA",
    type: "Market Sell",
    quantity: "25 shares",
    price: "$875.30",
    status: "Partially Filled",
    broker: "Fidelity",
    fee: "$0.00",
    value: "$21,882.50",
    settlement: "Pending",
  },
  {
    id: "ORD-10004",
    timestamp: "2024-06-15 14:45",
    customer: "Michael Brown",
    symbol: "GOOGL",
    type: "Limit Sell",
    quantity: "10 shares",
    price: "$2,800.00",
    status: "Failed",
    broker: "TD Ameritrade",
    fee: "$0.00",
    value: "$0.00",
    settlement: "N/A",
  },
  {
    id: "ORD-10005",
    timestamp: "2024-06-15 15:20",
    customer: "Emma Davis",
    symbol: "MSFT",
    type: "Market Buy",
    quantity: "75 shares",
    price: "$420.15",
    status: "Placed",
    broker: "E*TRADE",
    fee: "$0.75",
    value: "$31,511.25",
    settlement: "Pending",
  },
];

export const summaryData: SummaryCard[] = [
  {
    title: "Total Orders Today",
    value: "1,247",
    icon: "trending-up",
    color: "text-blue-600",
  },
  {
    title: "Executed",
    value: "1,189",
    icon: "circle-check-big",
    color: "text-blue-600",
  },
  { title: "Pending", value: "45", icon: "clock", color: "text-yellow-600" },
  {
    title: "Failed",
    value: "13",
    icon: "triangle-alert",
    color: "text-red-600",
  },
];

export const brokerData: BrokerStatus[] = [
  {
    name: "Interactive Brokers",
    status: "Online",
    orders: "Today's Orders: 347",
    statusColor: "bg-[#05A049]",
  },
  {
    name: "Schwab",
    status: "Online",
    orders: "Today's Orders: 892",
    statusColor: "bg-[#05A049]",
  },
  {
    name: "Fidelity",
    status: "Online",
    orders: "Today's Orders: 756",
    statusColor: "bg-[#05A049]",
  },
  {
    name: "TD Ameritrade",
    status: "Maintenance",
    orders: "Status: Offline",
    statusColor: "bg-yellow-500",
  },
  {
    name: "E*TRADE",
    status: "Online",
    orders: "Today's Orders: 334",
    statusColor: "bg-[#05A049]",
  },
];
