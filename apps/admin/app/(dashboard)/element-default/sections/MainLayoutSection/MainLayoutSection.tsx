import {
  BellIcon,
  ChevronDownIcon,
  SearchIcon,
  UserIcon,
  LogOutIcon,
} from "lucide-react";
import React, { useState, useRef, useEffect } from "react";
import { Avatar, AvatarFallback } from "@admin/components/ui/avatar";
import { Badge } from "@admin/components/ui/badge";
import { Input } from "@admin/components/ui/input";
import { SwitchUserModal } from "./SwitchUserModal";
import { LogoutModal } from "./LogoutModal";
import { AuthService } from "@admin/app/lib/AuthService";
import { useAuth } from "@admin/app/lib/AuthContext";

export const MainLayoutSection = (): JSX.Element => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showSwitchUser, setShowSwitchUser] = useState(false);
  const [showLogout, setShowLogout] = useState(false);
  const { user } = useAuth();
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    }
    if (dropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownOpen]);

  // Static user roles data
  const users = [
    ...(user
      ? [
          {
            name: user.name,
            email: user.email,
            role: user.role,
            initials: user.name ? user.name.charAt(0).toUpperCase() : "U",
            color: "bg-[#05A049]",
          },
        ]
      : []),
  ];

  const handleSwitchUserLogin = (
    user: { label: string; avatar: string } | null,
  ) => {
    setShowSwitchUser(false);
    // TODO: Implement actual user switching logic here
    if (user) {
      console.log("Switched to user:", user);
    }
  };

  const handleLogout = async () => {
    try {
      const { success, error } = await AuthService.signOut();
      if (success) {
        // Close the modal
        setShowLogout(false);
        // Redirect to login page
        window.location.href = "/login";
      } else {
        console.error("Failed to sign out:", error);
      }
    } catch (error) {
      console.error("Error during sign out:", error);
    }
  };

  return (
    <header className="w-full h-20 bg-white border-b border-solid shadow-[0px_1px_2px_#0000000d] flex items-center justify-between px-8">
      <div className="relative flex-1 max-w-[1292px]">
        <div className="relative flex items-center">
          <div className="absolute left-3 h-full flex items-center">
            <SearchIcon className="w-5 h-5 text-gray-400" />
          </div>
          <Input
            className="h-[55px] pl-10 bg-gray-50 rounded-[20px] border-none text-gray-400 text-[12.7px] font-normal focus-visible:ring-0 focus-visible:ring-offset-0"
            placeholder="Search orders, tickets, products..."
          />
        </div>
      </div>

      <div className="flex items-center gap-5">
        <div className="relative">
          <div className="w-10 h-10 flex items-center justify-center">
            <BellIcon className="w-6 h-6 text-gray-500" />
            <Badge className="absolute -top-1 -right-1 w-4 h-4 p-0 flex items-center justify-center bg-red-500 text-white text-[12.1px] rounded-full">
              3
            </Badge>
          </div>
        </div>

        {/* User info and dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            className="flex items-center gap-2 focus:outline-none"
            onClick={() => setDropdownOpen((v) => !v)}
          >
            <Avatar className="w-8 h-8 bg-[#05A049]">
              <AvatarFallback className="text-[13.7px] text-white bg-[#05A049]">
                {user?.name ? user.name.charAt(0).toUpperCase() : "U"}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col items-start">
              <span className="text-[13.3px] text-gray-900 font-normal leading-[20.2px]">
                {user?.name
                  ? user.name.charAt(0).toUpperCase() + user.name.slice(1)
                  : ""}{" "}
                {user?.role
                  ? user.role.charAt(0).toUpperCase() + user.role.slice(1)
                  : ""}
              </span>
              <span className="text-[11.3px] text-gray-500 font-normal leading-[16.1px]">
                {user?.email || ""}
              </span>
            </div>
            <ChevronDownIcon className="w-4 h-4 text-gray-500" />
          </button>

          {/* Dropdown menu */}
          {dropdownOpen && (
            <div
              className="absolute right-0 mt-3 w-72 bg-white rounded-2xl shadow-xl border border-gray-100 z-50 p-4 flex flex-col gap-2"
              style={{ minWidth: 280, background: "#fff" }}
            >
              {/* User roles */}
              {users.map((user, idx) => (
                <div key={idx} className="flex items-center gap-3 mb-1">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold text-lg ${user.color}`}
                  >
                    {user.initials}
                  </div>
                  <div className="flex flex-col">
                    <span className="text-[15px] text-gray-900 font-semibold leading-tight">
                      {user.name}
                    </span>
                    <span className="text-[13px] text-gray-500 leading-tight">
                      {user.email}
                    </span>
                  </div>
                </div>
              ))}
              <div className="border-t border-gray-100 my-2"></div>
              {/* Switch Users */}
              <button
                className="flex items-center gap-2 text-[15px] text-gray-700 py-2 px-2 rounded-[8px] hover:bg-gray-50 transition"
                onClick={() => setShowSwitchUser(true)}
              >
                <UserIcon className="w-5 h-5" />
                Switch Users
              </button>
              {/* Log out */}
              <button
                className="flex items-center gap-2 text-[15px] font-semibold py-2 px-2 rounded-[8px] bg-red-100 text-red-600 hover:bg-red-200 transition mt-1"
                onClick={() => setShowLogout(true)}
              >
                <LogOutIcon className="w-5 h-5" />
                Log out
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Switch User Modal */}
      <SwitchUserModal
        open={showSwitchUser}
        onOpenChange={setShowSwitchUser}
        onLogin={handleSwitchUserLogin}
      />
      {/* Logout Modal */}
      <LogoutModal
        open={showLogout}
        onOpenChange={setShowLogout}
        onLogout={handleLogout}
      />
    </header>
  );
};
