import * as Dialog from "@radix-ui/react-dialog";
import React from "react";

export interface LogoutModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onLogout: () => void;
}

export const LogoutModal: React.FC<LogoutModalProps> = ({
  open,
  onOpenChange,
  onLogout,
}) => {
  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/30 z-[100]" />
        <Dialog.Content
          className="fixed left-1/2 top-1/2 z-[101] w-full max-w-md max-h-[95vh] -translate-x-1/2 -translate-y-1/2 rounded-3xl bg-white p-0 shadow-2xl focus:outline-none flex flex-col items-center justify-center"
          style={{ boxShadow: "0 8px 32px 0 rgba(60, 60, 60, 0.18)" }}
        >
          <button
            className="absolute top-6 right-8 text-gray-400 hover:text-gray-600 text-2xl leading-none z-10"
            onClick={() => onOpenChange(false)}
            aria-label="Close"
            style={{ background: "none", border: "none" }}
          >
            &times;
          </button>
          <div className="pt-10 pb-6 px-8 w-full flex flex-col items-center">
            <img
              src="/logo.png"
              alt="Logout"
              className="w-28 h-28 mb-4"
              style={{ objectFit: "contain" }}
            />
            <h2 className="text-2xl font-bold mb-2 text-center">
              Are you sure you want to log out?
            </h2>
            <p
              className="text-gray-500 text-center mb-8 text-base font-normal"
              style={{ fontWeight: 400 }}
            >
              Logging out will end your session, and you will need to sign in
              again to continue
            </p>
            <div className="flex gap-4 justify-center w-full mt-2">
              <button
                className="px-8 py-2 rounded-full border border-gray-300 text-gray-700 bg-white hover:bg-gray-100 font-semibold text-lg"
                onClick={() => onOpenChange(false)}
                style={{ minWidth: 120 }}
              >
                Cancel
              </button>
              <button
                className="px-8 py-2 rounded-full text-white font-semibold text-lg transition min-w-[120px] bg-[#05A049] hover:bg-[#05A049]/80"
                onClick={onLogout}
              >
                Log out
              </button>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
