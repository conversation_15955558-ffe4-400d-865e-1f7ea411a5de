import * as Dialog from "@radix-ui/react-dialog";
import React, { useState } from "react";
import { UserIcon } from "lucide-react";

const users = [
  {
    label: "Add User",
    avatar: "/group-1.png",
    fallback: <UserIcon className="w-12 h-12 text-gray-400" />,
  },
  { label: "Super Admin", avatar: "/group-3.png" },
  { label: "Admin", avatar: "/group-4.png" },
];

export interface SwitchUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onLogin: (user: { label: string; avatar: string } | null) => void;
}

export const SwitchUserModal: React.FC<SwitchUserModalProps> = ({
  open,
  onOpenChange,
  onLogin,
}) => {
  const [selected, setSelected] = useState<number | null>(null);

  const handleLogin = () => {
    if (selected !== null) {
      onLogin(users[selected]);
    }
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/30 z-40" />
        <Dialog.Content
          className="fixed left-1/2 top-1/2 z-50 w-full max-w-2xl max-h-[95vh] -translate-x-1/2 -translate-y-1/2 rounded-3xl bg-white p-0 shadow-2xl focus:outline-none flex flex-col items-center justify-center"
          style={{ boxShadow: "0 8px 32px 0 rgba(60, 60, 60, 0.18)" }}
        >
          <button
            className="absolute top-6 right-8 text-gray-400 hover:text-gray-600 text-2xl leading-none z-10"
            onClick={() => onOpenChange(false)}
            aria-label="Close"
            style={{ background: "none", border: "none" }}
          >
            &times;
          </button>
          <div className="pt-10 pb-6 px-8 w-full flex flex-col items-center">
            <h2 className="text-3xl font-bold mb-2 text-center">
              Switch Users
            </h2>
            <p
              className="text-gray-500 text-center mb-10 text-base font-normal"
              style={{ fontWeight: 400 }}
            >
              Easily switch between user profiles to manage our Dashboard
              effortlessly!
            </p>
            <div className="flex justify-center gap-12 mb-10 w-full">
              {users.map((user, idx) => {
                const isSelected = selected === idx;
                return (
                  <div
                    key={user.label}
                    onClick={() => setSelected(idx)}
                    className={`flex flex-col items-center cursor-pointer transition-all select-none`}
                  >
                    <div
                      className={`w-[110px] h-[110px] rounded-full flex items-center justify-center overflow-hidden mb-3 border-2 transition-all shadow-sm ${
                        isSelected
                          ? "border-[#05A049] bg-[#EDF8F2]"
                          : "border-gray-200 bg-white"
                      }`}
                      style={{
                        boxShadow: isSelected
                          ? "0 0 0 4px #05A04922"
                          : "0 2px 8px 0 rgba(60,60,60,0.06)",
                      }}
                    >
                      {user.avatar ? (
                        <img
                          src={user.avatar}
                          alt={user.label}
                          className="w-full h-full object-contain"
                        />
                      ) : (
                        user.fallback || (
                          <UserIcon className="w-12 h-12 text-gray-400" />
                        )
                      )}
                    </div>
                    <span className="mt-1 text-base text-gray-800 font-medium text-center">
                      {user.label}
                    </span>
                  </div>
                );
              })}
            </div>
            <div className="flex gap-4 justify-center w-full mt-2">
              <button
                className="px-8 py-2 rounded-full border border-gray-300 text-gray-700 bg-white hover:bg-gray-100 font-semibold text-lg"
                onClick={() => onOpenChange(false)}
                style={{ minWidth: 120 }}
              >
                Cancel
              </button>
              <button
                className={`px-8 py-2 rounded-full text-white font-semibold text-lg transition min-w-[120px] ${selected !== null ? "bg-[#05A049] hover:bg-[#05A049]/80" : "bg-gray-300 cursor-not-allowed"}`}
                onClick={handleLogin}
                disabled={selected === null}
              >
                Login
              </button>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
