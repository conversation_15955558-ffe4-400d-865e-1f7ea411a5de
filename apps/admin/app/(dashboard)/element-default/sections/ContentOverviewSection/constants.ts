import {
  <PERSON><PERSON><PERSON><PERSON>,
  Recent<PERSON>rde<PERSON>,
  TopProduct,
  ComplianceAlert,
  ProductDistribution,
} from "./types";

export const METRIC_CARDS: MetricCard[] = [
  {
    id: 1,
    title: "Total Users",
    value: "12,847",
    change: "+12.5%",
    isPositive: true,
    icon: "/svg-8.svg",
  },
  {
    id: 2,
    title: "Total Vendors/Issuers",
    value: "34",
    change: "+2 new",
    isPositive: true,
    icon: "/svg-18.svg",
  },
  {
    id: 3,
    title: "Active Tickets",
    value: "186",
    change: "-8.2%",
    isPositive: false,
    icon: "/svg-3.svg",
  },
  {
    id: 4,
    title: "Monthly Revenue",
    value: "$2.4M",
    change: "+18.7%",
    isPositive: true,
    icon: "/svg-17.svg",
  },
];

export const RECENT_ORDERS: RecentOrder[] = [
  {
    id: "#ORD-001",
    customer: "<PERSON> Chen",
    product: "AAPL Stock",
    amount: "$50,000",
    status: "Executed",
    time: "2 mins ago",
  },
  {
    id: "#ORD-002",
    customer: "Robert Kim",
    product: "Treasury Bond",
    amount: "$100,000",
    status: "Pending",
    time: "5 mins ago",
  },
  {
    id: "#ORD-003",
    customer: "Sarah Wilson",
    product: "Growth Fund",
    amount: "$25,000",
    status: "Executed",
    time: "8 mins ago",
  },
  {
    id: "#ORD-004",
    customer: "Michael Brown",
    product: "TSLA Stock",
    amount: "$75,000",
    status: "Failed",
    time: "12 mins ago",
  },
];

export const TOP_PRODUCTS: TopProduct[] = [
  {
    rank: 1,
    name: "Tech Growth Fund",
    volume: "Volume: $2.8M",
    change: "+24.5%",
  },
  {
    rank: 2,
    name: "NVDA Stock",
    volume: "Volume: $2.1M",
    change: "+18.2%",
  },
  {
    rank: 3,
    name: "ESG Bond Fund",
    volume: "Volume: $1.9M",
    change: "+12.8%",
  },
  {
    rank: 4,
    name: "Dividend Equity",
    volume: "Volume: $1.7M",
    change: "+9.4%",
  },
];

export const COMPLIANCE_ALERTS: ComplianceAlert[] = [
  {
    id: 1,
    type: "KYC Expiry",
    entity: "John Martinez",
    time: "1 hour ago",
    priority: "HIGH",
    isPriorityHigh: true,
  },
  {
    id: 2,
    type: "Large Transaction",
    entity: "Global Corp Ltd",
    time: "3 hours ago",
    priority: "MEDIUM",
    isPriorityHigh: false,
  },
  {
    id: 3,
    type: "Risk Limit Breach",
    entity: "Investment Group",
    time: "5 hours ago",
    priority: "HIGH",
    isPriorityHigh: true,
  },
];

export const PRODUCT_DISTRIBUTION: ProductDistribution[] = [
  { name: "Stocks", percentage: "45%", color: "#3B82F6" },
  { name: "Bonds", percentage: "30%", color: "#10B981" },
  { name: "Funds", percentage: "20%", color: "#F59E0B" },
  { name: "Structured Products", percentage: "5%", color: "#EF4444" },
];

export const ASSETS_UNDER_MANAGEMENT = [
  { month: "Jan", value: 125 },
  { month: "Feb", value: 132 },
  { month: "Mar", value: 150 },
  { month: "Apr", value: 158 },
  { month: "May", value: 175 },
  { month: "Jun", value: 195 },
];
