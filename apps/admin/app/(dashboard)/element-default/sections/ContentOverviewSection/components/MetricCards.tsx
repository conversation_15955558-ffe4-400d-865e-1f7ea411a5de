import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { MetricCard } from "../types";

interface MetricCardsProps {
  cards: MetricCard[];
}

export const MetricCards: React.FC<MetricCardsProps> = ({ cards }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {cards.map((card) => (
        <Card
          key={card.id}
          className="border-none shadow-md rounded-[12.09px] bg-white"
        >
          <CardContent className="p-6 flex items-center">
            <div className="w-10 h-10 bg-blue-50 rounded-[8.06px] flex items-center justify-center mr-4">
              <img className="w-6 h-6" alt={card.title} src={card.icon} />
            </div>
            <div className="flex-1">
              <p className="text-[12.7px] text-gray-600 font-['Inter',Helvetica] leading-[20.2px]">
                {card.title}
              </p>
              <p className="text-[22.9px] font-bold text-gray-900 font-['Inter',Helvetica] leading-[32.3px]">
                {card.value}
              </p>
            </div>
            <div className="flex items-center">
              <img
                className="w-4 h-4 mr-1"
                alt="Trend"
                src={card.isPositive ? "/svg-2.svg" : "/svg-10.svg"}
              />
              <span
                className={`text-[13.1px] font-['Inter',Helvetica] ${card.isPositive ? "text-[#05A049]" : "text-red-600"}`}
              >
                {card.change}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
