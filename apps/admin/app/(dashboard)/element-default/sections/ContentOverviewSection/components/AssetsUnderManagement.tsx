import React from "react";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { AssetsUnderManagementData } from "../types";
import { Card, CardContent } from "@admin/components/ui/card";

interface AssetsUnderManagementProps {
  data: AssetsUnderManagementData[];
}

export const AssetsUnderManagement: React.FC<AssetsUnderManagementProps> = ({
  data,
}) => {
  return (
    <Card className="bg-white border shadow-[0px_1.01px_2.02px_#0000000d] rounded-[12.09px]">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-[16.9px] text-gray-900 font-['Inter',Helvetica] leading-[28.2px] font-bold">
            Assets Under Management
          </h2>
          <div className="flex items-center text-[#05A049] text-[13.1px] font-['Inter',Helvetica]">
            <img className="w-4 h-4 mr-1" alt="Trend" src="/svg-2.svg" />
            +15.2% this quarter
          </div>
        </div>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="month" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#3B82F6"
                strokeWidth={2}
                dot={{ r: 6 }}
                activeDot={{ r: 8 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
