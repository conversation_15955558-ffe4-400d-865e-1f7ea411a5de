import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { ComplianceAlert } from "../types";

interface ComplianceAlertsProps {
  alerts: ComplianceAlert[];
}

export const ComplianceAlerts: React.FC<ComplianceAlertsProps> = ({
  alerts,
}) => {
  return (
    <Card className="bg-white border shadow-[0px_1.01px_2.02px_#0000000d] rounded-[12.09px]">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-[16.9px] text-gray-900 font-['Inter',Helvetica] leading-[28.2px] font-bold">
            Compliance Alerts
          </h2>
          <button className="text-[13.2px] text-blue-600 font-['Inter',Helvetica] leading-[20.2px]">
            View All
          </button>
        </div>
        <div className="space-y-4">
          {alerts.map((alert) => (
            <div
              key={alert.id}
              className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0"
            >
              <div className="flex-1">
                <p className="text-[14.1px] font-medium text-gray-900 font-['Inter',Helvetica] leading-[22.2px]">
                  {alert.type}
                </p>
                <p className="text-[13.2px] text-gray-600 font-['Inter',Helvetica] leading-[20.2px]">
                  {alert.entity}
                </p>
              </div>
              <div className="flex items-center gap-4">
                <span
                  className={`px-2 py-1 rounded-full text-[12.1px] font-medium font-['Inter',Helvetica] leading-[19.2px] ${
                    alert.isPriorityHigh
                      ? "bg-red-100 text-red-700"
                      : "bg-yellow-100 text-yellow-700"
                  }`}
                >
                  {alert.priority}
                </span>
                <span className="text-[13.2px] text-gray-500 font-['Inter',Helvetica] leading-[20.2px]">
                  {alert.time}
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
