import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { TopProduct } from "../types";

interface TopProductsProps {
  products: TopProduct[];
}

export const TopProducts: React.FC<TopProductsProps> = ({ products }) => {
  return (
    <Card className="bg-white border shadow-[0px_1.01px_2.02px_#0000000d] rounded-[12.09px]">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-[16.9px] text-gray-900 font-['Inter',Helvetica] leading-[28.2px] font-bold">
            Top Performing Products
          </h2>
          <button className="text-[13.2px] text-blue-600 font-['Inter',Helvetica] leading-[20.2px]">
            View All
          </button>
        </div>
        <div className="space-y-4">
          {products.map((product) => (
            <div
              key={product.rank}
              className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0"
            >
              <div className="flex items-center gap-4">
                <span className="w-6 h-6 flex items-center justify-center bg-blue-100 rounded-full text-[13.2px] font-medium text-gray-700">
                  {product.rank}
                </span>
                <div>
                  <p className="text-[14.1px] font-medium text-gray-900 font-['Inter',Helvetica] leading-[22.2px]">
                    {product.name}
                  </p>
                  <p className="text-[13.2px] text-gray-600 font-['Inter',Helvetica] leading-[20.2px]">
                    {product.volume}
                  </p>
                </div>
              </div>
              <span className="text-[13.2px] text-[#05A049] font-['Inter',Helvetica] leading-[20.2px]">
                {product.change}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
