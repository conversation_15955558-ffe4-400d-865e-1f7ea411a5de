import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { ProductDistribution as ProductDistributionType } from "../types";
import {
  Pie<PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts";

interface ProductDistributionProps {
  distribution: ProductDistributionType[];
}

export const ProductDistribution: React.FC<ProductDistributionProps> = ({
  distribution,
}) => {
  // Convert percentage strings to numbers for the pie chart
  const data = distribution.map((item) => ({
    name: item.name,
    value: parseFloat(item.percentage),
    color: item.color,
  }));

  return (
    <Card className="bg-white border shadow-[0px_1.01px_2.02px_#0000000d] rounded-[12.09px]">
      <CardContent className="p-6">
        <h2 className="text-[16.9px] text-gray-900 font-['Inter',Helvetica] leading-[28.2px] mb-6 font-bold">
          Product Distribution
        </h2>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={70}
                outerRadius={120}
                paddingAngle={5}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) => `${value}%`}
                contentStyle={{
                  backgroundColor: "white",
                  border: "none",
                  borderRadius: "8px",
                  boxShadow: "0px 1.01px 2.02px #0000000d",
                }}
              />
              <Legend
                layout="horizontal"
                align="center"
                verticalAlign="bottom"
                wrapperStyle={{
                  paddingTop: "20px",
                }}
                formatter={(value, entry) => (
                  <span className="text-[14.1px] font-medium text-gray-900 font-['Inter',Helvetica] leading-[22.2px]">
                    {value}{" "}
                    {entry?.payload?.value ? `(${entry.payload.value}%)` : ""}
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
