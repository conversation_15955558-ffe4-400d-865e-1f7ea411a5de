import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { RecentOrder } from "../types";

interface RecentOrdersProps {
  orders: RecentOrder[];
}

export const RecentOrders: React.FC<RecentOrdersProps> = ({ orders }) => {
  return (
    <Card className="bg-white border shadow-[0px_1.01px_2.02px_#0000000d] rounded-[12.09px]">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-[16.9px] text-gray-900 font-['Inter',Helvetica] leading-[28.2px] font-bold">
            Recent Orders
          </h2>
          <button className="text-[13.2px] text-blue-600 font-['Inter',Helvetica] leading-[20.2px]">
            View All
          </button>
        </div>
        <div className="space-y-4">
          {orders.map((order) => (
            <div
              key={order.id}
              className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0"
            >
              <div className="flex-1">
                <p className="text-[14.1px] font-medium text-gray-900 font-['Inter',Helvetica] leading-[22.2px]">
                  {order.customer}
                </p>
                <p className="text-[13.2px] text-gray-600 font-['Inter',Helvetica] leading-[20.2px]">
                  {order.product}
                </p>
                <span className="text-[14.1px] font-medium text-gray-900 font-['Inter',Helvetica] leading-[22.2px]">
                  {order.amount}
                </span>
              </div>
              <div className="flex items-center gap-4">
                <span
                  className={`px-2 py-1 rounded-full text-[12.1px] font-medium font-['Inter',Helvetica] leading-[19.2px] ${
                    order.status === "Executed"
                      ? "bg-[#EDF8F2] text-[#05A049]"
                      : order.status === "Pending"
                        ? "bg-yellow-100 text-yellow-700"
                        : "bg-red-100 text-red-700"
                  }`}
                >
                  {order.status}
                </span>
                <span className="text-[13.2px] text-gray-500 font-['Inter',Helvetica] leading-[20.2px]">
                  {order.time}
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
