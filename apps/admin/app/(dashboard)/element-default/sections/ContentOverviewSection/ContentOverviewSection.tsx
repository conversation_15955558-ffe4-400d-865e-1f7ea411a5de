import React from "react";
import { MetricCards } from "./components/MetricCards";
import { RecentOrders } from "./components/RecentOrders";
import { TopProducts } from "./components/TopProducts";
import { ComplianceAlerts } from "./components/ComplianceAlerts";
import { ProductDistribution } from "./components/ProductDistribution";
import { AssetsUnderManagement } from "./components/AssetsUnderManagement";
import {
  METRIC_CARDS,
  RECENT_ORDERS,
  TOP_PRODUCTS,
  COMPLIANCE_ALERTS,
  PRODUCT_DISTRIBUTION,
  ASSETS_UNDER_MANAGEMENT,
} from "./constants";

export const ContentOverviewSection = (): JSX.Element => {
  return (
    <section className="w-full h-full  overflow-y-auto">
      <div className=" px-6 py-4">
        <h1 className="text-2xl font-bold text-gray-900 font-['Inter',Helvetica] leading-[36.3px]">
          Dashboard
        </h1>
        <p className="text-[15px] text-gray-600 font-['Inter',Helvetica] leading-[24.2px]">
          Welcome back, here's what's happening with your platform today.
        </p>
        <p className="text-[13px] text-gray-500 font-['Inter',Helvetica] leading-[20.2px] text-right">
          Last updated: 6/6/2025, 11:02:50 PM
        </p>
      </div>
      <div className="p-6">
        <MetricCards cards={METRIC_CARDS} />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <AssetsUnderManagement data={ASSETS_UNDER_MANAGEMENT} />
          <ProductDistribution distribution={PRODUCT_DISTRIBUTION} />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <RecentOrders orders={RECENT_ORDERS} />
          <TopProducts products={TOP_PRODUCTS} />
        </div>

        <div className=" gap-8">
          <ComplianceAlerts alerts={COMPLIANCE_ALERTS} />
        </div>
      </div>
    </section>
  );
};
