export interface MetricCard {
  id: number;
  title: string;
  value: string;
  change: string;
  isPositive: boolean;
  icon: string;
}

export interface RecentOrder {
  id: string;
  customer: string;
  product: string;
  amount: string;
  status: string;
  time: string;
}

export interface TopProduct {
  rank: number;
  name: string;
  volume: string;
  change: string;
}

export interface ComplianceAlert {
  id: number;
  type: string;
  entity: string;
  time: string;
  priority: string;
  isPriorityHigh: boolean;
}

export interface ProductDistribution {
  name: string;
  percentage: string;
  color: string;
}

export interface AssetsUnderManagementData {
  month: string;
  value: number;
}
