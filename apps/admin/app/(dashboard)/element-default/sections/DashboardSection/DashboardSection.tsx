import {
  LayoutDashboard,
  Ticket,
  Package,
  Truck,
  Users,
  UserCheck,
  Shield,
  Calculator,
  Headphones,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  BugIcon,
  AlertTriangleIcon,
  Settings,
  UserIcon,
  LogOutIcon,
} from "lucide-react";
import React, { useState, useRef, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { Button } from "@admin/components/ui/button";
import { PageType } from "../../types";
import { Avatar, AvatarFallback } from "@admin/components/ui/avatar";
import { SwitchUserModal } from "../MainLayoutSection/SwitchUserModal";
import { LogoutModal } from "../MainLayoutSection/LogoutModal";
import { useAuth } from "@admin/app/lib/AuthContext";
import { getAccessiblePages } from "@admin/app/lib/rolePermissions";
import type { UserRole } from "@admin/app/lib/rolePermissions";

const pageToPath: Record<PageType, string> = {
  Dashboard: "/element-default",
  TicketsManagement: "/tickets-management",
  OpenOrders: "/tickets-management/open-orders",
  ClosedOrders: "/tickets-management/closed-orders",
  ProductManagement: "/product-hub",
  ProductOverview: "/product-overview",
  ProductCatalog: "/product-catalog",
  InvestorManagement: "/investor-management",
  InvestorOverview: "/investor-management/investor-overview",
  InvestorDirectory: "/investor-management/investor-directory",
  LiveOrders: "/live-orders",
  UserManagement: "/user-management",
  RMDashboard: "/rm-dashboard",
  RMOverview: "/rm-dashboard/rm-overview",
  RMDirectory: "/rm-dashboard/rm-directory",
  Compliance: "/compliance",
  VendorManagement: "/vendor-management",
  PartnerOverview: "/vendor-management/partner-overview",
  PartnerDirectory: "/vendor-management/partner-directory",
  SupportTickets: "/support-tickets",
  ProductSupport: "/support-tickets/product-support",
  TechnicalSupport: "/support-tickets/technical-support",
  SystemConfiguration: "/system-configuration",
  SystemLogs: "/system-logs",
};

const pathToPage: Record<string, PageType> = Object.fromEntries(
  Object.entries(pageToPath).map(([key, value]) => [value, key as PageType]),
);

interface DashboardSectionProps {}

interface UserProfile {
  initials: string;
  name: string;
  email: string;
  color: string;
}

export const DashboardSection = ({}: DashboardSectionProps): JSX.Element => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [adminDropdownOpen, setAdminDropdownOpen] = useState(false);
  const [showSwitchUser, setShowSwitchUser] = useState(false);
  const [showLogout, setShowLogout] = useState(false);
  const adminDropdownRef = useRef<HTMLDivElement>(null);
  const [supportOpen, setSupportOpen] = useState(false);
  const [ticketManagementOpen, setTicketManagementOpen] = useState(false);
  const [productHubOpen, setProductHubOpen] = useState(false);
  const [vendorManagementOpen, setVendorManagementOpen] = useState(false);
  const [rmDashboardOpen, setRmDashboardOpen] = useState(false);
  const [investorManagementOpen, setInvestorManagementOpen] = useState(false);
  const { user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const activePage = pathToPage[pathname];

  // Static user profiles data
  const userProfiles: UserProfile[] = [
    ...(user
      ? [
          {
            name: user.name ?? "",
            email: user.email,
            initials: user.name ? user.name.charAt(0).toUpperCase() : "U",
            color: "bg-[#05A049]",
          },
        ]
      : []),
  ];

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        adminDropdownRef.current &&
        !adminDropdownRef.current.contains(event.target as Node)
      ) {
        setAdminDropdownOpen(false);
      }
    }

    if (adminDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [adminDropdownOpen]);

  // Navigation menu items data with their associated roles
  const allNavItems = [
    {
      icon: <LayoutDashboard size={20} />,
      label: "Dashboard",
      key: "Dashboard" as PageType,
    },
    {
      icon: <Ticket size={20} />,
      label: "Order Management",
      key: "TicketsManagement" as PageType,
    },
    {
      icon: <Package size={20} />,
      label: "Product Management",
      key: "ProductManagement" as PageType,
    },
    {
      icon: <Users size={20} />,
      label: "Investor Management",
      key: "InvestorManagement" as PageType,
    },
    {
      icon: <Calculator size={20} />,
      label: "Partner Management",
      key: "VendorManagement" as PageType,
    },
    {
      icon: <UserCheck size={20} />,
      label: "RM Management",
      key: "RMDashboard" as PageType,
    },
    {
      icon: <Shield size={20} />,
      label: "Compliance Center",
      key: "Compliance" as PageType,
    },
    {
      icon: <Headphones size={20} />,
      label: "Support Center",
      key: "SupportTickets" as PageType,
    },
    {
      icon: <Users size={20} />,
      label: "User Management",
      key: "UserManagement" as PageType,
    },
    {
      icon: <Settings size={20} />,
      label: "System Configuration",
      key: "SystemConfiguration" as PageType,
    },
    {
      icon: <AlertTriangleIcon size={20} />,
      label: "System Logs",
      key: "SystemLogs" as PageType,
    },
  ];

  // Filter navigation items based on user's role
  const navItems = allNavItems.filter((item) =>
    user?.role
      ? getAccessiblePages(user.role as UserRole).includes(item.key)
      : false,
  );

  const handleNavClick = (key: PageType) => {
    const path = pageToPath[key];
    if (path) {
      router.push(path);
    }
  };

  const handleSwitchUserLogin = (
    user: { label: string; avatar: string } | null,
  ) => {
    setShowSwitchUser(false);
    if (user) {
      console.log("Switched to user:", user);
    }
  };

  const handleLogout = () => {
    setShowLogout(false);
    // TODO: Implement actual logout logic here
    console.log("User logged out");
  };

  return (
    <div
      className={`min-h-screen ${isCollapsed ? "w-[80px]" : "w-[323px]"} bg-gradient-to-br from-[rgba(255,255,252,0.8)] to-[#FFFFFC]/20 border border-gray-200 flex flex-col justify-start transition-all duration-300`}
      style={{ width: isCollapsed ? "80px" : "323px", height: "942px" }}
    >
      {/* Logo and Arrow Button */}
      <div className="h-[80px] w-full flex items-center justify-center border-b border-gray-100 relative bg-white">
        {!isCollapsed && (
          <img
            className="object-contain w-[140px] h-[40px] transition-all duration-300"
            alt="Valura.ai Logo"
            src="/logo.png"
          />
        )}
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 bg-[#05A049] hover:bg-[#05A049]/80 text-white rounded-full w-8 h-8 flex items-center justify-center shadow transition-colors z-10 border-none outline-none p-0"
          onClick={() => setIsCollapsed((prev) => !prev)}
          aria-label={isCollapsed ? "Open sidebar" : "Close sidebar"}
        >
          <div className="flex items-center justify-center w-full h-full">
            {isCollapsed ? (
              <ChevronRightIcon size={20} />
            ) : (
              <ChevronLeftIcon size={20} />
            )}
          </div>
        </button>
      </div>
      {/* Navigation menu */}
      <nav
        className={`flex flex-col gap-1 py-6 ${isCollapsed ? "px-1" : "px-4"} flex-1 transition-all duration-300`}
      >
        {navItems.map((item, index) => {
          if (item.label === "Support Center") {
            const isSupportActive = pathname.startsWith(pageToPath[item.key]);
            return (
              <div key={index}>
                <Button
                  variant="ghost"
                  className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between"} gap-3 h-14 w-full rounded-xl transition-all duration-200  shadow-none border-none ${isCollapsed ? "px-2" : "px-4"} mb-1 ${
                    isSupportActive
                      ? "bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] text-white font-bold shadow-md"
                      : "text-gray-900 font-bold hover:bg-[#EDF8F2]/80 hover:text-[#05A049]"
                  }`}
                  title={item.label}
                  onClick={() => {
                    handleNavClick(item.key);
                    setSupportOpen((prev) => !prev);
                  }}
                  style={{
                    boxShadow: isSupportActive
                      ? "0 2px 8px 0 #05A04922"
                      : undefined,
                  }}
                >
                  <span
                    className={`flex items-center ${isCollapsed ? "justify-center w-full" : "gap-3"}`}
                  >
                    <span
                      className="flex items-center justify-center"
                      style={{ fontSize: 28 }}
                    >
                      {item.icon}
                    </span>
                    {!isCollapsed && (
                      <span className="truncate">{item.label}</span>
                    )}
                  </span>
                  {!isCollapsed &&
                    (supportOpen ? (
                      <ChevronUpIcon className="w-4 h-4 ml-2" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4 ml-2" />
                    ))}
                </Button>
                {supportOpen && !isCollapsed && (
                  <div className="pl-2 flex flex-col gap-1 mt-1">
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["ProductSupport"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push(pageToPath["ProductSupport"])}
                      style={{ marginBottom: "2px" }}
                    >
                      <BugIcon className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Product Support
                      </span>
                    </div>
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["TechnicalSupport"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() =>
                        router.push(pageToPath["TechnicalSupport"])
                      }
                      style={{ marginBottom: "2px" }}
                    >
                      <AlertTriangleIcon className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Technical Support
                      </span>
                    </div>
                  </div>
                )}
              </div>
            );
          }
          if(item.label === "Investor Management") {
            const isInvestorManagementActive = pathname.startsWith(pageToPath[item.key]) || 
                                           pathname.startsWith(pageToPath["InvestorOverview"]) || 
                                           pathname.startsWith(pageToPath["InvestorDirectory"]);
            return (
              <div key={index}>
                <Button
                  variant="ghost"
                  className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between"} gap-3 h-14 w-full rounded-xl transition-all duration-200  shadow-none border-none ${isCollapsed ? "px-2" : "px-4"} mb-1 ${
                    isInvestorManagementActive
                      ? "bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] text-white font-bold shadow-md"
                      : "text-gray-900 font-bold hover:bg-[#EDF8F2]/80 hover:text-[#05A049]"
                  }`}
                  title={item.label}
                  onClick={() => {
                    handleNavClick(item.key);
                    setInvestorManagementOpen((prev) => !prev);
                  }}
                  style={{
                    boxShadow: isInvestorManagementActive
                      ? "0 2px 8px 0 #05A04922"
                      : undefined,
                  }}
                >
                  <span
                    className={`flex items-center ${isCollapsed ? "justify-center w-full" : "gap-3"}`}
                  >
                    <span
                      className="flex items-center justify-center"
                      style={{ fontSize: 28 }}
                    >
                      {item.icon}
                    </span>
                    {!isCollapsed && (
                      <span className="truncate">{item.label}</span>
                    )}
                  </span>
                  {!isCollapsed &&
                    (investorManagementOpen ? (
                      <ChevronUpIcon className="w-4 h-4 ml-2" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4 ml-2" />
                    ))}
                </Button>
                {investorManagementOpen && !isCollapsed && (
                  <div className="pl-2 flex flex-col gap-1 mt-1">
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["InvestorOverview"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push(pageToPath["InvestorOverview"])}
                      style={{ marginBottom: "2px" }}
                    >
                      <Users className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Investor Overview
                      </span>
                    </div>
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["InvestorDirectory"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push(pageToPath["InvestorDirectory"])}
                      style={{ marginBottom: "2px" }}
                    >
                      <Users className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Investor Directory
                      </span>
                    </div>
                  </div>
                )}
              </div>
            );
          }
          
          if (item.label === "Product Management") {
            const isProductHubActive = pathname.startsWith(pageToPath[item.key]) || 
                                     pathname.startsWith(pageToPath["ProductOverview"]) || 
                                     pathname.startsWith(pageToPath["ProductCatalog"]);
            return (
              <div key={index}>
                <Button
                  variant="ghost"
                  className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between"} gap-3 h-14 w-full rounded-xl transition-all duration-200  shadow-none border-none ${isCollapsed ? "px-2" : "px-4"} mb-1 ${
                    isProductHubActive
                      ? "bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] text-white font-bold shadow-md"
                      : "text-gray-900 font-bold hover:bg-[#EDF8F2]/80 hover:text-[#05A049]"
                  }`}
                  title={item.label}
                  onClick={() => {
                    handleNavClick(item.key);
                    setProductHubOpen((prev) => !prev);
                  }}
                  style={{
                    boxShadow: isProductHubActive
                      ? "0 2px 8px 0 #05A04922"
                      : undefined,
                  }}
                >
                  <span
                    className={`flex items-center ${isCollapsed ? "justify-center w-full" : "gap-3"}`}
                  >
                    <span
                      className="flex items-center justify-center"
                      style={{ fontSize: 28 }}
                    >
                      {item.icon}
                    </span>
                    {!isCollapsed && (
                      <span className="truncate">{item.label}</span>
                    )}
                  </span>
                  {!isCollapsed &&
                    (productHubOpen ? (
                      <ChevronUpIcon className="w-4 h-4 ml-2" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4 ml-2" />
                    ))}
                </Button>
                {productHubOpen && !isCollapsed && (
                  <div className="pl-2 flex flex-col gap-1 mt-1">
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["ProductOverview"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push(pageToPath["ProductOverview"])}
                      style={{ marginBottom: "2px" }}
                    >
                      <Package className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Product Overview
                      </span>
                    </div>
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["ProductCatalog"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() =>
                        router.push(pageToPath["ProductCatalog"])
                      }
                      style={{ marginBottom: "2px" }}
                    >
                      <Package className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Product Catalog
                      </span>
                    </div>
                  </div>
                )}
              </div>
            );
          }
          
          if (item.label === "Order Management") {
            const isTicketManagementActive = pathname.startsWith(pageToPath[item.key]) || 
                                 pathname.startsWith(pageToPath["OpenOrders"]) || 
                                 pathname.startsWith(pageToPath["ClosedOrders"]) ||
                                 pathname.startsWith("/orders-management/auto-executed-orders");
            return (
              <div key={index}>
                <Button
                  variant="ghost"
                  className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between"} gap-3 h-14 w-full rounded-xl transition-all duration-200  shadow-none border-none ${isCollapsed ? "px-2" : "px-4"} mb-1 ${
                    isTicketManagementActive
                      ? "bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] text-white font-bold shadow-md"
                      : "text-gray-900 font-bold hover:bg-[#EDF8F2]/80 hover:text-[#05A049]"
                  }`}
                  title={item.label}
                  onClick={() => {
                    handleNavClick(item.key);
                    setTicketManagementOpen((prev) => !prev);
                  }}
                  style={{
                    boxShadow: isTicketManagementActive
                      ? "0 2px 8px 0 #05A04922"
                      : undefined,
                  }}
                >
                  <span
                    className={`flex items-center ${isCollapsed ? "justify-center w-full" : "gap-3"}`}
                  >
                    <span
                      className="flex items-center justify-center"
                      style={{ fontSize: 28 }}
                    >
                      {item.icon}
                    </span>
                    {!isCollapsed && (
                      <span className="truncate">{item.label}</span>
                    )}
                  </span>
                  {!isCollapsed &&
                    (ticketManagementOpen ? (
                      <ChevronUpIcon className="w-4 h-4 ml-2" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4 ml-2" />
                    ))}
                </Button>
                {ticketManagementOpen && !isCollapsed && (
                  <div className="pl-2 flex flex-col gap-1 mt-1">
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["OpenOrders"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push(pageToPath["OpenOrders"])}
                      style={{ marginBottom: "2px" }}
                    >
                      <Ticket className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Open Orders
                      </span>
                    </div>
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["ClosedOrders"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push(pageToPath["ClosedOrders"])}
                      style={{ marginBottom: "2px" }}
                    >
                      <Ticket className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Closed Orders
                      </span>
                    </div>
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === "/orders-management/auto-executed-orders" ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push("/orders-management/auto-executed-orders")}
                      style={{ marginBottom: "2px" }}
                    >
                      <Truck className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Auto Executed Orders
                      </span>
                    </div>
                  </div>
                )}
              </div>
            );
          }
          
          if (item.label === "RM Management") {
            const isRmDashboardActive = pathname.startsWith(pageToPath[item.key]) || 
                                      pathname.startsWith(pageToPath["RMOverview"]) || 
                                      pathname.startsWith(pageToPath["RMDirectory"]);
            return (
              <div key={index}>
                <Button
                  variant="ghost"
                  className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between"} gap-3 h-14 w-full rounded-xl transition-all duration-200  shadow-none border-none ${isCollapsed ? "px-2" : "px-4"} mb-1 ${
                    isRmDashboardActive
                      ? "bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] text-white font-bold shadow-md"
                      : "text-gray-900 font-bold hover:bg-[#EDF8F2]/80 hover:text-[#05A049]"
                  }`}
                  title={item.label}
                  onClick={() => {
                    handleNavClick(item.key);
                    setRmDashboardOpen((prev) => !prev);
                  }}
                  style={{
                    boxShadow: isRmDashboardActive
                      ? "0 2px 8px 0 #05A04922"
                      : undefined,
                  }}
                >
                  <span
                    className={`flex items-center ${isCollapsed ? "justify-center w-full" : "gap-3"}`}
                  >
                    <span
                      className="flex items-center justify-center"
                      style={{ fontSize: 28 }}
                    >
                      {item.icon}
                    </span>
                    {!isCollapsed && (
                      <span className="truncate">{item.label}</span>
                    )}
                  </span>
                  {!isCollapsed &&
                    (rmDashboardOpen ? (
                      <ChevronUpIcon className="w-4 h-4 ml-2" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4 ml-2" />
                    ))}
                </Button>
                {rmDashboardOpen && !isCollapsed && (
                  <div className="pl-2 flex flex-col gap-1 mt-1">
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["RMOverview"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push(pageToPath["RMOverview"])}
                      style={{ marginBottom: "2px" }}
                    >
                      <UserCheck className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        RM Overview
                      </span>
                    </div>
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["RMDirectory"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() =>
                        router.push(pageToPath["RMDirectory"])
                      }
                      style={{ marginBottom: "2px" }}
                    >
                      <UserCheck className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        RM Directory
                      </span>
                    </div>
                  </div>
                )}
              </div>
            );
          }
          
          if (item.label === "Partner Management") {
            const isVendorManagementActive = pathname.startsWith(pageToPath[item.key]) || 
                                           pathname.startsWith(pageToPath["PartnerOverview"]) || 
                                           pathname.startsWith(pageToPath["PartnerDirectory"]);
            return (
              <div key={index}>
                <Button
                  variant="ghost"
                  className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between"} gap-3 h-14 w-full rounded-xl transition-all duration-200  shadow-none border-none ${isCollapsed ? "px-2" : "px-4"} mb-1 ${
                    isVendorManagementActive
                      ? "bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] text-white font-bold shadow-md"
                      : "text-gray-900 font-bold hover:bg-[#EDF8F2]/80 hover:text-[#05A049]"
                  }`}
                  title={item.label}
                  onClick={() => {
                    handleNavClick(item.key);
                    setVendorManagementOpen((prev) => !prev);
                  }}
                  style={{
                    boxShadow: isVendorManagementActive
                      ? "0 2px 8px 0 #05A04922"
                      : undefined,
                  }}
                >
                  <span
                    className={`flex items-center ${isCollapsed ? "justify-center w-full" : "gap-3"}`}
                  >
                    <span
                      className="flex items-center justify-center"
                      style={{ fontSize: 28 }}
                    >
                      {item.icon}
                    </span>
                    {!isCollapsed && (
                      <span className="truncate">{item.label}</span>
                    )}
                  </span>
                  {!isCollapsed &&
                    (vendorManagementOpen ? (
                      <ChevronUpIcon className="w-4 h-4 ml-2" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4 ml-2" />
                    ))}
                </Button>
                {vendorManagementOpen && !isCollapsed && (
                  <div className="pl-2 flex flex-col gap-1 mt-1">
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["PartnerOverview"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() => router.push(pageToPath["PartnerOverview"])}
                      style={{ marginBottom: "2px" }}
                    >
                      <Calculator className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Partner Overview
                      </span>
                    </div>
                    <div
                      className={`flex items-center gap-2 h-10 w-full rounded-[8px] px-4 cursor-pointer transition-colors duration-150 text-[15px] font-normal select-none
                        ${pathname === pageToPath["PartnerDirectory"] ? "text-[#05A049] font-semibold" : "text-gray-700 hover:bg-[#EDF8F2]/80"}
                      `}
                      onClick={() =>
                        router.push(pageToPath["PartnerDirectory"])
                      }
                      style={{ marginBottom: "2px" }}
                    >
                      <Calculator className="w-4 h-4" />
                      <span className="font-bold text-xs text-gray-900">
                        Partner Directory
                      </span>
                    </div>
                  </div>
                )}
              </div>
            );
          }
          return (
            <Button
              key={index}
              variant="ghost"
              className={`flex items-center ${isCollapsed ? "justify-center" : "justify-start"} gap-3 h-14 w-full rounded-xl transition-all duration-200 text-[15px] font-bold shadow-none border-none ${isCollapsed ? "px-2" : "px-4"} mb-1 ${
                activePage === item.key
                  ? "bg-[linear-gradient(91.8deg,rgba(5,160,73,0.9)_0%,rgba(5,160,73,0.63)_100%)] text-white font-bold shadow-md"
                  : "text-gray-900 font-bold hover:bg-[#EDF8F2]/80 hover:text-[#05A049]"
              }`}
              title={item.label}
              onClick={() => handleNavClick(item.key)}
              style={{
                boxShadow:
                  activePage === item.key ? "0 2px 8px 0 #05A04922" : undefined,
              }}
            >
              <span
                className="flex items-center justify-center"
                style={{ fontSize: 28 }}
              >
                {item.icon}
              </span>
              {!isCollapsed && <span className="truncate">{item.label}</span>}
            </Button>
          );
        })}
      </nav>

      {/* Admin User Button at Bottom */}
      {/* <div
        className={`border-t border-gray-100 p-4 ${isCollapsed ? "px-2" : "px-4"} relative`}
        ref={adminDropdownRef}
      >
        <Button
          variant="ghost"
          className={`flex items-center ${isCollapsed ? "justify-center" : "justify-start"} h-16 w-full rounded-xl transition-all duration-200 text-[15px] font-bold shadow-none border-none ${isCollapsed ? "px-2" : "px-4"} mb-1 text-gray-900 font-bold hover:bg-[#EDF8F2]/80 hover:text-[#05A049]`}
          title="Admin Settings"
          onClick={() => setAdminDropdownOpen((v) => !v)}
        >
          <div className="flex items-center w-full justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="w-10 h-10 bg-[#05A049]">
                <AvatarFallback className="text-[16px] text-white bg-[#05A049]">
                  AD
                </AvatarFallback>
              </Avatar>
              {!isCollapsed && (
                <div className="flex flex-col items-start justify-center">
                  <span className="text-[17px] font-semibold text-gray-900 leading-[22px]">
                    {user?.name
                      ? user.name.charAt(0).toUpperCase() + user.name.slice(1)
                      : ""}
                  </span>
                  <span className="text-[12px] text-gray-400 leading-[20px]">
                    {user?.email}
                  </span>
                </div>
              )}
            </div>
            {!isCollapsed && <Settings className="w-6 h-6 text-gray-400" />}
          </div>
        </Button> */}

        {/* Admin Dropdown Menu */}
        {/* {adminDropdownOpen && !isCollapsed && (
          <div
            className="absolute bottom-full left-4 mb-2 w-72 bg-white rounded-2xl shadow-xl border border-gray-100 z-50 p-4 flex flex-col gap-2"
            style={{ minWidth: 280, background: "#fff" }}
          > */}
            {/* User roles */}
            {/* {userProfiles.map((user, idx) => (
              <div key={idx} className="flex items-center gap-3 mb-1">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold text-lg ${user.color}`}
                >
                  {user.initials}
                </div>
                <div className="flex flex-col">
                  <span className="text-[15px] text-gray-900 font-semibold leading-tight">
                    {user.name}
                  </span>
                  <span className="text-[13px] text-gray-500 leading-tight">
                    {user.email}
                  </span>
                </div>
              </div>
            ))} */}
            {/* <div className="border-t border-gray-100 my-2"></div> */}
            {/* Switch Users */}
            {/* <button
              className="flex items-center gap-2 text-[15px] text-gray-700 py-2 px-2 rounded-[8px] hover:bg-gray-50 transition"
              onClick={() => setShowSwitchUser(true)}
            >
              <UserIcon className="w-5 h-5" />
              Switch Users
            </button> */}
            {/* Log out */}
            {/* <button
              className="flex items-center gap-2 text-[15px] font-semibold py-2 px-2 rounded-[8px] bg-red-100 text-red-600 hover:bg-red-200 transition mt-1"
              onClick={() => setShowLogout(true)}
            >
              <LogOutIcon className="w-5 h-5" />
              Log out
            </button> */}
          {/* </div> */}
        {/* )} */}
        
      {/* </div> */}

      {/* Switch User Modal */}
      {/* <SwitchUserModal
        open={showSwitchUser}
        onOpenChange={setShowSwitchUser}
        onLogin={handleSwitchUserLogin}
      /> */}
      {/* Logout Modal */}
      {/* <LogoutModal
        open={showLogout}
        onOpenChange={setShowLogout}
        onLogout={handleLogout}
      /> */}
    </div>
  );
};
