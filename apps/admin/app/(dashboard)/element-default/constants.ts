import {
  LayoutDashboard,
  Ticket,
  Package,
  Truck,
  Users,
  UserCheck,
  Shield,
  Headphones,
  BugIcon,
  AlertTriangleIcon,
} from "lucide-react";
import { PageType } from "./types";

export const NAVIGATION_ITEMS: Array<{
  id: PageType;
  label: string;
  icon: React.ElementType;
}> = [
  { id: "Dashboard", label: "Dashboard", icon: LayoutDashboard },
  { id: "TicketsManagement", label: "Tickets Management", icon: Ticket },
  { id: "SupportTickets", label: "Support Tickets", icon: Headphones },
  { id: "ProductSupport", label: "Product Support", icon: BugIcon },
  {
    id: "TechnicalSupport",
    label: "Technical Support",
    icon: AlertTriangleIcon,
  },
  { id: "LiveOrders", label: "Live Orders", icon: Truck },
  { id: "ProductManagement", label: "Product Management", icon: Package },
  { id: "ProductOverview", label: "Product Overview", icon: Package },
  { id: "ProductCatalog", label: "Product Catalog", icon: Package },
  { id: "VendorManagement", label: "Vendor Management", icon: Users },
  { id: "RMDashboard", label: "RM Dashboard", icon: UserCheck },
  { id: "Compliance", label: "Compliance", icon: Shield },
  { id: "UserManagement", label: "User Management", icon: Users },
];
