export type PageType =
  | "Dashboard"
  | "TicketsManagement"
  | "OpenOrders"
  | "ClosedOrders"
  | "SupportTickets"
  | "ProductSupport"
  | "TechnicalSupport"
  | "LiveOrders"
  | "ProductManagement"
  | "ProductOverview"
  | "ProductCatalog"
  | "VendorManagement"
  | "PartnerOverview"
  | "PartnerDirectory"
  | "RMDashboard"
  | "RMOverview"
  | "RMDirectory"
  | "Compliance"
  | "UserManagement"
  | "InvestorManagement"
  | "InvestorOverview"
  | "InvestorDirectory"
  | "SystemConfiguration"
  | "SystemLogs";

export interface NavigationProps {
  onNavigate: (page: PageType) => void;
  activePage: PageType;
}
