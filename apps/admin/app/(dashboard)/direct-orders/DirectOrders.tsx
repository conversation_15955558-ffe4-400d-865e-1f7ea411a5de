"use client";
import React, { useState } from "react";
import { Badge } from "@admin/components/ui/badge";
import { <PERSON><PERSON> } from "@admin/components/ui/button";
import { Card, CardContent } from "@admin/components/ui/card";
import { Input } from "@admin/components/ui/input";
import { SearchIcon, DownloadIcon, RefreshCwIcon } from "lucide-react";

// Sample data for orders
const ordersData = [
  {
    id: "ORD-10001",
    timestamp: "2024-06-15 09:15",
    customer: "Alice Chen",
    symbol: "AAPL",
    type: "Market Buy",
    quantity: "100 shares",
    price: "$185.42",
    status: "Settled",
    broker: "Interactive Brokers",
    fee: "$1.00",
    value: "$18,542.00",
    settlement: "2024-06-17",
  },
  {
    id: "ORD-10002",
    timestamp: "2024-06-15 10:32",
    customer: "<PERSON>",
    symbol: "TSLA",
    type: "Limit Buy",
    quantity: "50 shares",
    price: "$239.85",
    status: "Filled",
    broker: "Schwab",
    fee: "$0.00",
    value: "$11,992.50",
    settlement: "2024-06-17",
  },
  {
    id: "ORD-10003",
    timestamp: "2024-06-15 11:22",
    customer: "Sarah <PERSON>",
    symbol: "NVDA",
    type: "Market Sell",
    quantity: "25 shares",
    price: "$875.30",
    status: "Partially Filled",
    broker: "Fidelity",
    fee: "$0.00",
    value: "$21,882.50",
    settlement: "Pending",
  },
  {
    id: "ORD-10004",
    timestamp: "2024-06-15 14:45",
    customer: "Michael Brown",
    symbol: "GOOGL",
    type: "Limit Sell",
    quantity: "10 shares",
    price: "$2,800.00",
    status: "Failed",
    broker: "TD Ameritrade",
    fee: "$0.00",
    value: "$0.00",
    settlement: "N/A",
  },
  {
    id: "ORD-10005",
    timestamp: "2024-06-15 15:20",
    customer: "Emma Davis",
    symbol: "MSFT",
    type: "Market Buy",
    quantity: "75 shares",
    price: "$420.15",
    status: "Placed",
    broker: "E*TRADE",
    fee: "$0.75",
    value: "$31,511.25",
    settlement: "Pending",
  },
];

// Sample data for summary cards
const summaryData = [
  {
    title: "Total Orders Today",
    value: "1,247",
    icon: "📈",
    color: "text-blue-600",
  },
  { title: "Executed", value: "1,189", icon: "✅", color: "text-[#05A049]" },
  { title: "Pending", value: "45", icon: "⏳", color: "text-yellow-600" },
  { title: "Failed", value: "13", icon: "⚠️", color: "text-red-600" },
];

// Sample data for broker connectivity
const brokerData = [
  {
    name: "Interactive Brokers",
    status: "Online",
    orders: "Today's Orders: 347",
    statusColor: "bg-[#05A049]",
  },
  {
    name: "Schwab",
    status: "Online",
    orders: "Today's Orders: 892",
    statusColor: "bg-[#05A049]",
  },
  {
    name: "Fidelity",
    status: "Online",
    orders: "Today's Orders: 756",
    statusColor: "bg-[#05A049]",
  },
  {
    name: "TD Ameritrade",
    status: "Maintenance",
    orders: "Status: Offline",
    statusColor: "bg-yellow-500",
  },
  {
    name: "E*TRADE",
    status: "Online",
    orders: "Today's Orders: 334",
    statusColor: "bg-[#05A049]",
  },
];

export const DirectOrders = (): JSX.Element => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All Status");
  const [brokerFilter, setBrokerFilter] = useState("All Brokers");
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Settled":
        return "bg-[#EDF8F2] text-[#05A049]";
      case "Filled":
        return "bg-blue-100 text-blue-800";
      case "Partially Filled":
        return "bg-yellow-100 text-yellow-800";
      case "Failed":
        return "bg-red-100 text-red-800";
      case "Placed":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-[#EDF8F2] text-[#05A049]";
    }
  };

  return (
    <div className="w-full h-full  overflow-y-auto">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Live Orders</h1>
            <p className="text-gray-600 text-sm">
              Monitor stock orders and broker connectivity
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="text-gray-600">
              <DownloadIcon className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button
              className="bg-[#05A049] hover:bg-[#05A049]/90 text-white"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <>
                  <RefreshCwIcon className="w-4 h-4 mr-2 animate-spin" />
                  Refreshing...
                </>
              ) : (
                <>
                  <RefreshCwIcon className="w-4 h-4 mr-2" />
                  Refresh
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {summaryData.map((item, index) => (
            <Card key={index} className="bg-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">{item.title}</p>
                    <p className={`text-2xl font-bold ${item.color}`}>
                      {item.value}
                    </p>
                  </div>
                  <span className="text-2xl">{item.icon}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4 mb-6">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search orders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm"
          >
            <option>All Status</option>
            <option>Settled</option>
            <option>Filled</option>
            <option>Partially Filled</option>
            <option>Failed</option>
            <option>Placed</option>
          </select>
          <select
            value={brokerFilter}
            onChange={(e) => setBrokerFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm"
          >
            <option>All Brokers</option>
            <option>Interactive Brokers</option>
            <option>Schwab</option>
            <option>Fidelity</option>
            <option>TD Ameritrade</option>
            <option>E*TRADE</option>
          </select>
          <span className="text-sm text-gray-500 flex items-center">
            5 orders found
          </span>
        </div>

        {/* Orders Table */}
        <Card className="bg-white mb-6">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="text-left p-4 text-sm font-medium text-gray-700">
                      ORDER ID
                    </th>
                    <th className="text-left p-4 text-sm font-medium text-gray-700">
                      CUSTOMER & SYMBOL
                    </th>
                    <th className="text-left p-4 text-sm font-medium text-gray-700">
                      TYPE & QUANTITY
                    </th>
                    <th className="text-left p-4 text-sm font-medium text-gray-700">
                      PRICE
                    </th>
                    <th className="text-left p-4 text-sm font-medium text-gray-700">
                      STATUS
                    </th>
                    <th className="text-left p-4 text-sm font-medium text-gray-700">
                      BROKER
                    </th>
                    <th className="text-left p-4 text-sm font-medium text-gray-700">
                      VALUE
                    </th>
                    <th className="text-left p-4 text-sm font-medium text-gray-700">
                      SETTLEMENT
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {ordersData.map((order, index) => (
                    <tr key={order.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div>
                          <span className="font-medium text-gray-900">
                            {order.id}
                          </span>
                          <p className="text-xs text-gray-500">
                            {order.timestamp}
                          </p>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-gray-900">
                            {order.customer}
                          </p>
                          <p className="text-sm text-gray-600">
                            {order.symbol}
                          </p>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-gray-900">
                            {order.type}
                          </p>
                          <p className="text-sm text-gray-600">
                            {order.quantity}
                          </p>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="font-medium text-gray-900">
                          {order.price}
                        </span>
                      </td>
                      <td className="p-4">
                        <Badge
                          className={`${getStatusBadgeColor(order.status)} text-xs`}
                        >
                          {order.status}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-gray-900">
                            {order.broker}
                          </p>
                          <p className="text-xs text-gray-500">
                            Fee: {order.fee}
                          </p>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="font-medium text-gray-900">
                          {order.value}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-600">
                          {order.settlement}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Broker Connectivity Status */}
        <Card className="bg-white">
          <div className="border-b p-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Broker Connectivity Status
            </h3>
          </div>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {brokerData.map((broker, index) => (
                <div
                  key={index}
                  className="text-center p-4 border rounded-[8px]"
                >
                  <div className="flex items-center justify-center mb-2">
                    <div
                      className={`w-3 h-3 ${broker.statusColor} rounded-full mr-2`}
                    ></div>
                    <span className="font-medium text-gray-900">
                      {broker.name}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">{broker.status}</p>
                  <p className="text-xs text-gray-500">{broker.orders}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
