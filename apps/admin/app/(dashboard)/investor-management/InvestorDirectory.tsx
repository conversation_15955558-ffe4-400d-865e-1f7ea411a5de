"use client";
import React, { useState, useMemo } from "react";
import { Card } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { 
  UsersIcon, 
  ShieldIcon, 
  AlertTriangleIcon,
  DownloadIcon,
  RefreshCwIcon,
  CheckCircleIcon,
  ClockIcon,
  FileTextIcon
} from "lucide-react";
import { InvestorUsersTable } from "./components/InvestorUsersTable";
import { useInvestorUsers, mockInvestorUsers } from "./services/investorUserService";
import { InvestorUserFilters } from "./types";

export const InvestorDirectory = (): JSX.Element => {
  const [filters, setFilters] = useState<InvestorUserFilters>({
    search: "",
    kycStatusFilter: "",
    statusFilter: "",
    dateFrom: "",
    dateTo: "",
  });

  // Use the service hook to fetch users
  const { 
    data: users = [], 
    isLoading, 
    error, 
    refetch 
  } = useInvestorUsers(filters);

  // For development, use mock data if API fails
  const displayUsers = users.length > 0 ? users : mockInvestorUsers;

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const totalUsers = displayUsers.length;
    const activeUsers = displayUsers.filter(user => user.status === "active").length;
    const kycApproved = displayUsers.filter(user => user.kyc_status === "approved").length;
    const kycPending = displayUsers.filter(user => user.kyc_status === "pending").length;

    return [
      {
        title: "Total Investors",
        value: totalUsers.toString(),
        icon: UsersIcon,
        color: "text-blue-600",
        bgColor: "bg-blue-50"
      },
      {
        title: "Active Users",
        value: activeUsers.toString(),
        icon: ShieldIcon,
        color: "text-green-600",
        bgColor: "bg-green-50"
      },
      {
        title: "KYC Approved",
        value: kycApproved.toString(),
        icon: CheckCircleIcon,
        color: "text-[#05A049]",
        bgColor: "bg-[#EDF8F2]"
      },
      {
        title: "KYC Pending",
        value: kycPending.toString(),
        icon: ClockIcon,
        color: "text-yellow-600",
        bgColor: "bg-yellow-50"
      }
    ];
  }, [displayUsers]);

  const formatDateForCSV = (dateString: string | null) => {
    try {
      // Handle null, empty, or invalid date strings
      if (!dateString || dateString === "N/A" || dateString === "null" || dateString === "") {
        return "Never";
      }
      
      const date = new Date(dateString);
      
      // Check if the date is valid (not epoch date or invalid)
      if (isNaN(date.getTime()) || date.getTime() === 0) {
        return "Never";
      }
      
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      return "Never";
    }
  };

  const handleExport = () => {
    // Create CSV content
    const csvContent = [
      ["ID", "First Name", "Last Name", "Email", "Mobile", "KYC Status", "Account Status", "Profile Completion", "Portfolio Value", "Connected Broker", "Joined"],
      ...displayUsers.map(user => [
        user.id,
        user.first_name,
        user.last_name,
        user.email,
        user.mobile_number,
        user.kyc_status,
        user.status,
        `${user.profile_completion}%`,
        `$${user.portfolio_value.toLocaleString()}`,
        user.connected_broker || "N/A",
        formatDateForCSV(user.created_at)
      ])
    ].map(row => row.map(field => `"${field}"`).join(",")).join("\n");

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `investor-directory-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="px-6 py-4 flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Investor Directory</h1>
          <p className="text-gray-500 text-[15px] mt-1">
            View and manage investor user details with KYC status from the web application
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => refetch()}
            className="flex items-center gap-2"
          >
            <RefreshCwIcon className="w-4 h-4" />
            Refresh
          </Button>
          <Button
            onClick={handleExport}
            className="admin_green_gradient hover:admin_green_gradient_hover rounded-[8px] text-white px-5 py-2 text-[15px] font-semibold flex items-center gap-2"
          >
            <DownloadIcon className="w-4 h-4" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="px-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {summaryStats.map((stat, index) => (
            <Card key={index} className="p-6 border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mx-6 mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertTriangleIcon className="w-5 h-5 text-red-600" />
            <span className="text-red-600 font-medium">Error loading investor users:</span>
            <span className="text-red-500">{error.message}</span>
            <button 
              onClick={() => refetch()}
              className="ml-auto text-red-600 hover:text-red-800 underline text-sm"
            >
              Retry
            </button>
          </div>
          <p className="text-sm text-red-600 mt-2">
            Using mock data for demonstration. Connect to your database API to see real user data.
          </p>
        </div>
      )}

      {/* Users Table */}
      <div className="px-6 flex-1">
        <InvestorUsersTable
          users={displayUsers}
          filters={filters}
          loading={isLoading}
          onFiltersChange={setFilters}
        />
      </div>
    </div>
  );
}; 