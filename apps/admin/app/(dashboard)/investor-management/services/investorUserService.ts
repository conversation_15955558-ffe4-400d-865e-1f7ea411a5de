import axiosInstance from "@admin/app/lib/axiosInstance";
import { InvestorUser, InvestorUserResponse, InvestorUserFilters } from "../types";
import { useQuery } from "@tanstack/react-query";

// Transform API user data to match our InvestorUser interface
const transformApiUserToInvestorUser = (apiUser: any): InvestorUser => {
  // Calculate profile completion based on KYC status
  const profileCompletion = apiUser.kyc_status === "approved" ? 100 : 0;
  
  // Get connected broker name from portfolio data
  const connectedBroker = apiUser.Portfolio && apiUser.Portfolio.length > 0 
    ? apiUser.Portfolio[0].broker?.name || "N/A"
    : "N/A";
  
  return {
    id: apiUser.id,
    first_name: apiUser.first_name,
    last_name: apiUser.last_name,
    email: apiUser.email,
    mobile_number: apiUser.mobile_number || "N/A",
    kyc_status: apiUser.kyc_status,
    status: apiUser.status,
    created_at: apiUser.created_at,
    last_login: apiUser.last_login,
    profile_completion: profileCompletion,
    portfolio_value: apiUser.Portfolio[0]?.balance || 0,
    connected_broker: connectedBroker
  };
};

// API function to fetch investor users
const fetchInvestorUsersAPI = async (filters: InvestorUserFilters): Promise<InvestorUser[]> => {
  const params = new URLSearchParams();
  
  if (filters.search) params.append('search', filters.search);
  if (filters.kycStatusFilter) params.append('kyc_status', filters.kycStatusFilter);
  if (filters.statusFilter) params.append('status', filters.statusFilter);
  if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
  if (filters.dateTo) params.append('dateTo', filters.dateTo);

  const response = await axiosInstance.get(`http://localhost:4000/api/admin/users?${params.toString()}`);
  
  if (response.status !== 200) {
    throw new Error('Failed to fetch investor users');
  }
  
  const apiResponse: InvestorUserResponse = response.data;
  return apiResponse.data.map(transformApiUserToInvestorUser);
};

// React Query hook for fetching investor users
export const useInvestorUsers = (filters: InvestorUserFilters) => {
  return useQuery({
    queryKey: ['investorUsers', filters],
    queryFn: () => fetchInvestorUsersAPI(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Mock data for development/testing
export const mockInvestorUsers: InvestorUser[] = [
  {
    id: "INV-001",
    first_name: "John",
    last_name: "Smith",
    email: "<EMAIL>",
    mobile_number: "******-0101",
    kyc_status: "approved",
    status: "active",
    created_at: "2024-01-15T10:30:00Z",
    last_login: "2024-01-15T14:30:00Z",
    profile_completion: 100,
    portfolio_value: 125000,
    connected_broker: "Interactive Brokers"
  },
  {
    id: "INV-002",
    first_name: "Sarah",
    last_name: "Johnson",
    email: "<EMAIL>",
    mobile_number: "******-0102",
    kyc_status: "pending",
    status: "active",
    created_at: "2024-01-14T09:15:00Z",
    last_login: "2024-01-15T13:45:00Z",
    profile_completion: 0,
    portfolio_value: 0,
    connected_broker: "N/A"
  },
  {
    id: "INV-003",
    first_name: "Michael",
    last_name: "Chen",
    email: "<EMAIL>",
    mobile_number: "******-0103",
    kyc_status: "in_progress",
    status: "active",
    created_at: "2024-01-13T16:20:00Z",
    last_login: "", // User hasn't logged in yet
    profile_completion: 0,
    portfolio_value: 0,
    connected_broker: "N/A"
  },
  {
    id: "INV-004",
    first_name: "Emma",
    last_name: "Wilson",
    email: "<EMAIL>",
    mobile_number: "******-0104",
    kyc_status: "rejected",
    status: "suspended",
    created_at: "2024-01-12T14:45:00Z",
    last_login: "2024-01-14T09:30:00Z",
    profile_completion: 0,
    portfolio_value: 0,
    connected_broker: "N/A"
  },
  {
    id: "INV-005",
    first_name: "David",
    last_name: "Park",
    email: "<EMAIL>",
    mobile_number: "******-0105",
    kyc_status: "approved",
    status: "inactive",
    created_at: "2024-01-11T11:30:00Z",
    last_login: null, // User hasn't logged in yet
    profile_completion: 100,
    portfolio_value: 75000,
    connected_broker: "TD Ameritrade"
  },
  {
    id: "INV-006",
    first_name: "Lisa",
    last_name: "Brown",
    email: "<EMAIL>",
    mobile_number: "******-0106",
    kyc_status: "approved",
    status: "active",
    created_at: "2024-01-10T08:20:00Z",
    last_login: "2024-01-15T16:15:00Z",
    profile_completion: 100,
    portfolio_value: 250000,
    connected_broker: "Charles Schwab"
  }
]; 