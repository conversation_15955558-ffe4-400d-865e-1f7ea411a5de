import React from "react";
import { Input } from "@admin/components/ui/input";
import { Button } from "@admin/components/ui/button";
import { Badge } from "@admin/components/ui/badge";
import { LoadingSpinner } from "@admin/components/ui/loading-spinner";
import {
  SearchIcon,
  CalendarIcon,
  UserIcon,
  UsersIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  XIcon,
  ClockIcon,
  FileTextIcon,
  PercentIcon,
  DollarSignIcon,
  BuildingIcon,
} from "lucide-react";
import { InvestorUser, InvestorUserFilters } from "../types";

interface InvestorUsersTableProps {
  users: InvestorUser[];
  filters: InvestorUserFilters;
  loading?: boolean;
  onFiltersChange: (filters: InvestorUserFilters) => void;
}

const selectStyles = {
  appearance: "none" as const,
  backgroundImage: "none",
  paddingRight: "2.5rem",
};

export const InvestorUsersTable: React.FC<InvestorUsersTableProps> = ({
  users,
  filters,
  loading = false,
  onFiltersChange,
}) => {
  const handleFilterChange = (key: keyof InvestorUserFilters, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: "",
      kycStatusFilter: "",
      statusFilter: "",
      dateFrom: "",
      dateTo: "",
    });
  };

  const getKYCStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge className="bg-[#EDF8F2] text-[#05A049] rounded-full text-xs font-normal">
            <CheckCircleIcon className="w-3 h-3 mr-1" />
            Approved
          </Badge>
        );
      case "pending":
        return (
          <Badge className="bg-yellow-100 text-yellow-700 rounded-full text-xs font-normal">
            <ClockIcon className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
      case "in_progress":
        return (
          <Badge className="bg-blue-100 text-blue-700 rounded-full text-xs font-normal">
            <FileTextIcon className="w-3 h-3 mr-1" />
            In Progress
          </Badge>
        );
      case "rejected":
        return (
          <Badge className="bg-red-100 text-red-700 rounded-full text-xs font-normal">
            <AlertTriangleIcon className="w-3 h-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-700 rounded-full text-xs font-normal">
            {status}
          </Badge>
        );
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-[#EDF8F2] text-[#05A049] rounded-full text-xs font-normal">
            Active
          </Badge>
        );
      case "inactive":
        return (
          <Badge className="bg-gray-100 text-gray-700 rounded-full text-xs font-normal">
            Inactive
          </Badge>
        );
      case "suspended":
        return (
          <Badge className="bg-red-100 text-red-700 rounded-full text-xs font-normal">
            Suspended
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-700 rounded-full text-xs font-normal">
            {status}
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string) => {
    try {
      // Handle null, empty, or invalid date strings
      if (!dateString || dateString === "N/A" || dateString === "null" || dateString === "") {
        return "Never";
      }
      
      const date = new Date(dateString);
      
      // Check if the date is valid (not epoch date or invalid)
      if (isNaN(date.getTime()) || date.getTime() === 0) {
        return "Never";
      }
      
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return "Never";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-12 text-center">
        <LoadingSpinner className="w-8 h-8 text-[#05A049] mx-auto mb-4" />
        <p className="text-gray-500">Loading investor users...</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Filters Section */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex flex-col lg:flex-row gap-4 mb-4">
          <div className="flex-1">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search users by name or email..."
                value={filters.search}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                className="pl-10 border border-gray-200 rounded-[10px]"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={filters.kycStatusFilter}
              onChange={(e) => handleFilterChange("kycStatusFilter", e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
              style={selectStyles}
            >
              <option value="">All KYC Status</option>
              <option value="approved">Approved</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="rejected">Rejected</option>
            </select>
            <select
              value={filters.statusFilter}
              onChange={(e) => handleFilterChange("statusFilter", e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-[8px] bg-white text-sm"
              style={selectStyles}
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
        </div>
        
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          <div className="flex gap-2 items-center">
            <CalendarIcon className="w-4 h-4 text-gray-500" />
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-[8px] text-sm"
            />
            <span className="text-gray-500">to</span>
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange("dateTo", e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-[8px] text-sm"
            />
          </div>
          <Button
            onClick={clearFilters}
            variant="outline"
            className="text-gray-600 hover:text-gray-800"
          >
            <XIcon className="w-4 h-4 mr-2" />
            Clear Filters
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full text-sm">
          <thead>
            <tr className="text-left text-gray-500 border-b bg-[#F9FAFB]">
              <th className="py-3 px-6 font-semibold">USER DETAILS</th>
              <th className="py-3 px-6 font-semibold">KYC STATUS</th>
              <th className="py-3 px-6 font-semibold">ACCOUNT STATUS</th>
              <th className="py-3 px-6 font-semibold">PROFILE COMPLETION</th>
              <th className="py-3 px-6 font-semibold">PORTFOLIO VALUE</th>
              <th className="py-3 px-6 font-semibold">CONNECTED BROKER</th>
              <th className="py-3 px-6 font-semibold">JOINED</th>
            </tr>
          </thead>
          <tbody>
            {users.length === 0 ? (
              <tr>
                <td colSpan={7} className="py-8 text-center text-gray-500">
                  <div className="flex flex-col items-center">
                    <UsersIcon className="w-12 h-12 text-gray-300 mb-2" />
                    <p>No users found</p>
                    <p className="text-sm">Try adjusting your filters</p>
                  </div>
                </td>
              </tr>
            ) : (
              users.map((user) => (
                <tr key={user.id} className="border-b hover:bg-gray-50 transition-all">
                  <td className="py-3 px-6">
                    <div className="flex items-center gap-3">
                      <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-50">
                        <UserIcon className="w-4 h-4 text-[#05A049]" />
                      </span>
                      <div>
                        <div className="font-medium text-gray-900">{user.first_name} {user.last_name}</div>
                        <div className="text-xs text-gray-500">{user.email}</div>
                        <div className="text-[11px] text-gray-400">{user.mobile_number}</div>
                      </div>
                    </div>
                  </td>
                  <td className="py-3 px-6">
                    {getKYCStatusBadge(user.kyc_status)}
                  </td>
                  <td className="py-3 px-6">
                    {getStatusBadge(user.status)}
                  </td>
                  <td className="py-3 px-6">
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-[#05A049] h-2 rounded-full" 
                          style={{ width: `${user.profile_completion}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-600">{user.profile_completion}%</span>
                    </div>
                  </td>
                  <td className="py-3 px-6">
                    <div className="flex items-center gap-2">
                      {/* <DollarSignIcon className="w-4 h-4 text-green-600" /> */}
                      <span className="font-medium text-gray-900">
                        {formatCurrency(user.portfolio_value)}
                      </span>
                    </div>
                  </td>
                  <td className="py-3 px-6">
                    <div className="flex items-center gap-2">
                      <BuildingIcon className="w-4 h-4 text-blue-600" />
                      <span className="font-medium text-gray-900">
                        {user.connected_broker || "N/A"}
                      </span>
                    </div>
                  </td>
                  <td className="py-3 px-6 text-gray-700">
                    {formatDate(user.created_at)}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Summary */}
      <div className="p-4 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>{users.length} users found</span>
          <span>Last updated: {new Date().toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
}; 