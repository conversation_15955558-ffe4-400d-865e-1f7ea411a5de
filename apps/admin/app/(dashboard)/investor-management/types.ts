export interface InvestorUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  kyc_status: "pending" | "approved" | "rejected" | "in_progress";
  status: "active" | "inactive" | "suspended";
  created_at: string;
  last_login: string | null;
  profile_completion: number;
  portfolio_value: number;
  connected_broker?: string;
}

export interface InvestorUserResponse {
  statusCode: number;
  message: string;
  data: InvestorUser[];
}

export interface InvestorUserFilters {
  search: string;
  kycStatusFilter: string;
  statusFilter: string;
  dateFrom: string;
  dateTo: string;
} 