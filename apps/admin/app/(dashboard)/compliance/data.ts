import {
  SummaryCard,
  AuditTrailEntry,
  SCARequirement,
  RegulatoryReport,
  ComplianceAlert,
  ComplianceCheckApiData,
  ComplianceAlertApiData,
} from "./types";

export const SUMMARY_CARDS: SummaryCard[] = [
  {
    title: "Active Alerts",
    value: 0,
    icon: "AlertTriangle",
    color: "text-red-600",
    bg: "bg-red-50",
  },
  {
    title: "Compliant Customers",
    value: 0,
    icon: "CheckCircle",
    color: "text-[#05A049]",
    bg: "bg-[#EDF8F2]",
  },
  {
    title: "Pending Reviews",
    value: 0,
    icon: "Clock",
    color: "text-yellow-600",
    bg: "bg-yellow-50",
  },
  {
    title: "Reports Due",
    value: 3,
    icon: "FileText",
    color: "text-blue-600",
    bg: "bg-blue-50",
  },
];

export const AUDIT_TRAIL_DATA: AuditTrailEntry[] = [
  {
    user: "<PERSON>",
    action: "Updated customer risk profile",
    resource: "Customer: Premium Investments Ltd",
    timestamp: "2024-01-15 14:30:22",
    ip: "*************",
    result: "Success",
  },
  {
    user: "<PERSON>",
    action: "Approved structured product quote",
    resource: "Ticket: TKT-001",
    timestamp: "2024-01-15 13:45:11",
    ip: "*************",
    result: "Success",
  },
  {
    user: "Admin User",
    action: "Modified user permissions",
    resource: "User: <EMAIL>",
    timestamp: "2024-01-15 11:20:05",
    ip: "*************",
    result: "Success",
  },
];

export const SCA_REQUIREMENTS: SCARequirement[] = [
  {
    title: "Customer Onboarding",
    items: [
      "KYC documentation",
      "Risk assessment",
      "Suitability testing",
      "Identity verification",
    ],
    icon: "User2",
  },
  {
    title: "Market Conduct",
    items: [
      "Fair dealing principles",
      "Best execution",
      "Conflict of interest",
      "Product disclosure",
    ],
    icon: "Shield",
  },
  {
    title: "Record Keeping",
    items: [
      "Transaction records",
      "Customer communications",
      "Compliance documentation",
      "Audit trails",
    ],
    icon: "FileText",
  },
];

export const COMPLIANCE_ALERTS: ComplianceAlert[] = [
  {
    alert: {
      title: "KYC Expiry",
      description: "Customer KYC documentation expires in 7 days",
      code: "ALT-001",
    },
    customer: "John Martinez",
    severity: "HIGH",
    dueDate: "2024-01-22",
    assignedTo: "Compliance Team",
    status: "Open",
  },
  {
    alert: {
      title: "Large Transaction Alert",
      description:
        "Transaction amount exceeds $10M threshold - requires additional scrutiny",
      code: "ALT-002",
    },
    customer: "Global Wealth Partners",
    severity: "MEDIUM",
    dueDate: "2024-01-18",
    assignedTo: "Sarah Johnson",
    status: "Under Review",
  },
  {
    alert: {
      title: "Risk Limit Breach",
      description: "Portfolio concentration exceeds approved risk limits",
      code: "ALT-003",
    },
    customer: "Tech Investment Group",
    severity: "HIGH",
    dueDate: "2024-01-17",
    assignedTo: "Risk Team",
    status: "Action Required",
  },
];

export const REGULATORY_REPORTS: RegulatoryReport[] = [
  {
    id: "1",
    title: "Q1 2024 Compliance Report",
    date: "Mar 31, 2024",
    status: "completed",
    type: "Quarterly",
  },
  {
    id: "2",
    title: "Annual Security Assessment",
    date: "Dec 31, 2023",
    status: "completed",
    type: "Annual",
  },
];

// Function to map API response to compliance check format
export const mapComplianceData = (apiData: ComplianceCheckApiData[]) => {
  return apiData?.map((item: ComplianceCheckApiData) => {
    // Check if this is a vendor KYB check
    const isVendorKYB = item.check_type === "KYB" && item.details && typeof item.details === 'object' && 'vendor_name' in item.details;
    
    if (isVendorKYB) {
      const vendorDetails = item.details as any;
      return {
        alert: {
          title: `Vendor KYB Check - ${vendorDetails.vendor_name}`,
          description: `Know Your Business verification for vendor: ${vendorDetails.vendor_name} (${vendorDetails.vendor_type})`,
          code: item.check_id,
        },
        customer: vendorDetails.vendor_name,
        severity: item.status === "PENDING" ? "HIGH" : "MEDIUM",
        dueDate: new Date(item.created_at).toISOString().split("T")[0],
        assignedTo: item.assigned_to || "None",
        status: item.status === "PENDING" ? "Open" : item.status,
        details: item.details,
        check_type: item.check_type,
        check_id: item.check_id,
        user_id: item.user_id,
        created_at: item.created_at,
        updated_at: item.updated_at,
      };
    } else {
      // Regular user compliance check
      return {
        alert: {
          title: `${item.check_type} Check`,
          description: `${item.check_type} compliance check for ${item.user.first_name} ${item.user.last_name}`,
          code: item.check_id,
        },
        customer: `${item.user.first_name} ${item.user.last_name}`,
        severity: item.status === "PENDING" ? "HIGH" : "MEDIUM",
        dueDate: new Date(item.created_at).toISOString().split("T")[0],
        assignedTo: item.assigned_to || "None",
        status: item.status === "PENDING" ? "Open" : item.status,
        details: item.details,
        check_type: item.check_type,
        check_id: item.check_id,
        user_id: item.user_id,
        created_at: item.created_at,
        updated_at: item.updated_at,
      };
    }
  }) || [];
};

// Function to map API response to compliance alerts format
export const mapComplianceAlerts = (apiData: ComplianceAlertApiData[]) => {
  return apiData?.map((item: ComplianceAlertApiData) => ({
    alert: {
      title: item.title,
      description: item.description,
      code: item.alert_id,
    },
    customer: `${item.user.first_name} ${item.user.last_name}`,
    severity: (item.severity_level?.toUpperCase() === 'HIGH' ? 'HIGH' : item.severity_level?.toUpperCase() === 'LOW' ? 'LOW' : 'MEDIUM') as "HIGH" | "MEDIUM" | "LOW",
    dueDate: new Date(item.due_date).toISOString().split("T")[0],
    assignedTo: item.assigned_to || "None",
    status: item.status,
    alert_id: item.alert_id,
    alert_type: item.alert_type,
    document_type: item.document_type,
    expiry_date: item.expiry_date,
    created_at: item.created_at,
    updated_at: item.updated_at,
  })) || [];
};

// Function to map API response to audit logs format
export const mapAuditLogs = (apiData: any[]) => {
  return apiData?.map((log: any) => ({
    user: `${log.user?.name} <${log.user?.role}>` || log.admin_name || 'System',
    action: log.action,
    resource: log.resource || `${log.entity_type}: ${log.entity_id}`,
    timestamp: new Date(log.created_at).toLocaleString(),
    ip: log.ip_address || 'N/A',
    result: log.status || 'Success',
  })) || [];
};
