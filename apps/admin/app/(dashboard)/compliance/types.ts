import { ReactNode } from "react";
import { IconName } from "./iconMap";

export type TabName = "checks" | "alerts" | "reports" | "audit";

export interface SummaryCard {
  title: string;
  value: number;
  icon: IconName;
  color: string;
  bg: string;
}

export interface AuditTrailEntry {
  user: string;
  action: string;
  resource: string;
  timestamp: string;
  ip: string;
  result: string;
}

export interface SCARequirement {
  title: string;
  items: string[];
  icon: IconName;
}

export interface RegulatoryReport {
  id: string;
  title: string;
  date: string;
  status: "completed" | "pending" | "failed";
  type: string;
}

export interface complianceChecks{

}

export interface ComplianceAlert {
  alert: {
    title: string;
    description: string;
    code: string;
  };
  customer: string;
  severity: "HIGH" | "MEDIUM" | "LOW";
  dueDate: string;
  assignedTo: string;
  status: string;
}

export interface SummaryCardsProps {
  cards: SummaryCard[];
  renderIcon: (iconName: IconName, className?: string) => React.ReactNode;
}

export interface SCARequirementsProps {
  requirements: SCARequirement[];
  renderIcon: (iconName: IconName, className?: string) => React.ReactNode;
}

export type TabType =
  | "Compliance Alerts"
  | "Regulatory Reports"
  | "Audit Trail";

// API response type for compliance check
export interface ComplianceCheckApiData {
  check_type: string;
  check_id: string;
  user: {
    first_name: string;
    last_name: string;
  };
  status: string;
  created_at: string;
  assigned_to?: string;
  details?: any;
  user_id: string;
  updated_at: string;
}

// API response type for compliance alert
export interface ComplianceAlertApiData {
  title: string;
  description: string;
  alert_id: string;
  user: {
    first_name: string;
    last_name: string;
  };
  severity_level: string;
  due_date: string;
  assigned_to?: string;
  status: string;
  alert_type?: string;
  document_type?: string;
  expiry_date?: string;
  created_at: string;
  updated_at: string;
}
