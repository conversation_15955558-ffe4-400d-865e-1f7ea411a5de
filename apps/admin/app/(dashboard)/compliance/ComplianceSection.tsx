"use client";

import type React from "react";
import { useState, useMemo } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "@admin/components/ui/tabs";
import { SummaryCards } from "./components/SummaryCards";
import { AuditTrail } from "./components/AuditTrail";
import { ComplianceAlerts } from "./components/ComplianceAlerts";
import { SCARequirements } from "./components/SCARequirements";
import { RegulatoryReports } from "./components/RegulatoryReports";
import type { TabName } from "./types";
import {
  SUMMARY_CARDS,
  AUDIT_TRAIL_DATA,
  SCA_REQUIREMENTS,
  COMPLIANCE_ALERTS,
  REGULATORY_REPORTS,
  mapComplianceData,
  mapComplianceAlerts,
  mapAuditLogs,
} from "./data";
import { iconMap, type IconName } from "./iconMap";
import { RefreshCwIcon } from "lucide-react";
import { useComplianceData, useComplianceAlerts, useAuditLogs } from "@admin/app/lib/hooks/api-hooks";
import { ComplianceChecks } from "./components/ComplianceCheck";

export const ComplianceSection: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabName>("checks");

  // React Query hooks
  const { data: complianceResponse, isLoading: complianceLoading, refetch: refetchCompliance } = useComplianceData();
  const { data: alertsResponse, isLoading: alertsLoading, refetch: refetchAlerts } = useComplianceAlerts();
  const { data: auditResponse, isLoading: auditLoading, refetch: refetchAudit } = useAuditLogs();

  // Transform API data
  const complianceData = useMemo(() => {
    if (!complianceResponse?.data) return [];
    return mapComplianceData(complianceResponse.data);
  }, [complianceResponse]);

  const complianceAlerts = useMemo(() => {
    if (!alertsResponse?.data) return [];
    return mapComplianceAlerts(alertsResponse.data);
  }, [alertsResponse]);

  const auditLogs = useMemo(() => {
    if (!auditResponse?.data?.logs) return [];
    return mapAuditLogs(auditResponse.data.logs);
  }, [auditResponse]);

  const isLoading = complianceLoading;

  const handleRefresh = () => {
    refetchCompliance();
    refetchAlerts();
    refetchAudit();
  };

  const renderIcon = (iconName: IconName, className?: string) => {
    const Icon = iconMap[iconName];
    return Icon ? <Icon className={className} /> : null;
  };

  return (
    <div className="w-full h-full overflow-y-auto">
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-semibold text-gray-900">
              Compliance & Regulatory
            </h2>
            <p className="text-gray-600 text-sm">
              Monitor compliance status and regulatory requirements
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              className="border border-gray-300 bg-white hover:bg-gray-50 text-black rounded-[8px]"
              onClick={handleRefresh}
              disabled={isLoading || alertsLoading || auditLoading}
            >
              <RefreshCwIcon className={`w-4 h-4 mr-2 ${(isLoading || alertsLoading || auditLoading) ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              className="border border-gray-300 bg-white hover:bg-gray-50 text-black rounded-[8px]"
            >
              {renderIcon("Download", "w-4 h-4 mr-2")}
              Generate Report
            </Button>
            {/* <Button className="admin_green_gradient hover:admin_green_gradient_hover rounded-[8px] text-white">
              {renderIcon("Plus", "w-4 h-4 mr-2")}
              New Alert
            </Button> */}
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-6">
          <SummaryCards cards={SUMMARY_CARDS} renderIcon={renderIcon} />
          <Tabs
            value={activeTab}
            onValueChange={(value: string) => setActiveTab(value as TabName)}
            className="bg-white rounded-[8px] p-6 mb-6"
          >
            <TabsList className="h-auto p-0 bg-transparent border-b border-gray-200 rounded-none w-full justify-start">
              <TabsTrigger
                value="checks"
                className="relative h-12 px-4 pb-3 pt-3 bg-transparent text-gray-500 border-b-2 border-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-gray-900 data-[state=active]:border-[#05A049] data-[state=active]:shadow-none hover:text-gray-700 font-medium"
              >
                Compliance Checks
              </TabsTrigger>
              <TabsTrigger
                value="alerts"
                className="relative h-12 px-4 pb-3 pt-3 bg-transparent text-gray-500 border-b-2 border-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-gray-900 data-[state=active]:border-[#05A049] data-[state=active]:shadow-none hover:text-gray-700 font-medium"
              >
                Compliance Alerts
              </TabsTrigger>
              <TabsTrigger
                value="reports"
                className="relative h-12 px-4 pb-3 pt-3 bg-transparent text-gray-500 border-b-2 border-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-gray-900 data-[state=active]:border-[#05A049] data-[state=active]:shadow-none hover:text-gray-700 font-medium"
              >
                Regulatory Reports
              </TabsTrigger>
              <TabsTrigger
                value="audit"
                className="relative h-12 px-4 pb-3 pt-3 bg-transparent text-gray-500 border-b-2 border-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-gray-900 data-[state=active]:border-[#05A049] data-[state=active]:shadow-none hover:text-gray-700 font-medium"
              >
                Audit Trail
              </TabsTrigger>
            </TabsList>

            <TabsContent value="checks" className="space-y-6 mt-6">
              <ComplianceChecks checks={complianceData} loading={isLoading} />
            </TabsContent>
            <TabsContent value="alerts" className="space-y-6 mt-6">
              <ComplianceAlerts alerts={complianceAlerts} />
            </TabsContent>

            <TabsContent value="reports" className="mt-6">
              <RegulatoryReports reports={REGULATORY_REPORTS} />
            </TabsContent>

            <TabsContent value="audit" className="mt-6">
              <AuditTrail entries={auditLogs} loading={auditLoading} />
            </TabsContent>
          </Tabs>
        </div>
        <SCARequirements
          requirements={SCA_REQUIREMENTS}
          renderIcon={renderIcon}
        />
      </div>
    </div>
  );
};
