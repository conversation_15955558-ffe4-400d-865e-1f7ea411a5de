import React from "react";
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  FileTextIcon,
  User2Icon,
  ShieldIcon,
} from "lucide-react";
import {
  SummaryCard,
  AuditTrailEntry,
  SCARequirement,
  ComplianceAlert,
} from "./types";

export const SUMMARY_CARDS: SummaryCard[] = [
  {
    title: "Active Alerts",
    value: 0,
    icon: "AlertTriangle",
    color: "text-red-600",
    bg: "bg-red-50",
  },
  {
    title: "Compliant Customers",
    value: 0,
    icon: "CheckCircle",
    color: "text-[#05A049]",
    bg: "bg-[#EDF8F2]",
  },
  {
    title: "Pending Reviews",
    value: 0,
    icon: "Clock",
    color: "text-yellow-600",
    bg: "bg-yellow-50",
  },
  {
    title: "Reports Due",
    value: 0,
    icon: "FileText",
    color: "text-blue-600",
    bg: "bg-blue-50",
  },
];

export const AUDIT_TRAIL_DATA: AuditTrailEntry[] = [
  {
    user: "<PERSON> Johnson",
    action: "Updated customer risk profile",
    resource: "Customer: Premium Investments Ltd",
    timestamp: "2024-01-15 14:30:22",
    ip: "*************",
    result: "Success",
  },
  {
    user: "Michael Chen",
    action: "Approved structured product quote",
    resource: "Ticket: TKT-001",
    timestamp: "2024-01-15 13:45:11",
    ip: "*************",
    result: "Success",
  },
  {
    user: "Admin User",
    action: "Modified user permissions",
    resource: "User: <EMAIL>",
    timestamp: "2024-01-15 11:20:05",
    ip: "*************",
    result: "Success",
  },
];

export const SCA_REQUIREMENTS: SCARequirement[] = [
  {
    title: "Customer Onboarding",
    items: [
      "KYC documentation",
      "Risk assessment",
      "Suitability testing",
      "Identity verification",
    ],
    icon: "User2",
  },
  {
    title: "Market Conduct",
    items: [
      "Fair dealing principles",
      "Best execution",
      "Conflict of interest",
      "Product disclosure",
    ],
    icon: "Shield",
  },
  {
    title: "Record Keeping",
    items: [
      "Transaction records",
      "Customer communications",
      "Compliance documentation",
      "Audit trails",
    ],
    icon: "FileText",
  },
];

export const COMPLIANCE_ALERTS: ComplianceAlert[] = [
  {
    alert: {
      title: "KYC Expiry",
      description: "Customer KYC documentation expires in 7 days",
      code: "ALT-001",
    },
    customer: "John Martinez",
    severity: "HIGH",
    dueDate: "2024-01-22",
    assignedTo: "Compliance Team",
    status: "Open",
  },
  {
    alert: {
      title: "Large Transaction Alert",
      description:
        "Transaction amount exceeds $10M threshold - requires additional scrutiny",
      code: "ALT-002",
    },
    customer: "Global Wealth Partners",
    severity: "MEDIUM",
    dueDate: "2024-01-18",
    assignedTo: "Sarah Johnson",
    status: "Under Review",
  },
  {
    alert: {
      title: "Risk Limit Breach",
      description: "Portfolio concentration exceeds approved risk limits",
      code: "ALT-003",
    },
    customer: "Tech Investment Group",
    severity: "HIGH",
    dueDate: "2024-01-17",
    assignedTo: "Risk Team",
    status: "Action Required",
  },
];
