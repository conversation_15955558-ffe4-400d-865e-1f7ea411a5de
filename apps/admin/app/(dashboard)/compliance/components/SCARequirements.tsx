import React from "react";
import { Card, CardContent, CardHeader } from "@admin/components/ui/card";
import { SCARequirementsProps } from "../types";

export const SCARequirements: React.FC<SCARequirementsProps> = ({
  requirements,
  renderIcon,
}) => {
  return (
    <Card className="mt-6 bg-white">
      <CardHeader>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          SCA Category 5 Compliance Requirements
        </h2>
        <p className="text-gray-600 text-sm">
          Key regulatory requirements for financial distribution
        </p>
      </CardHeader>

      <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4  bg-white rounded-[8px] p-6">
        {requirements.map((requirement, index) => (
          <Card key={index} className="">
            <CardContent className="p-6 bg-white">
              <div className="flex items-center mb-4">
                {renderIcon(requirement.icon, "w-6 h-6 text-gray-600 mr-2")}
                <h3 className="text-lg font-semibold">{requirement.title}</h3>
              </div>
              <ul className="space-y-2">
                {requirement.items.map((item, itemIndex) => (
                  <li
                    key={itemIndex}
                    className="flex items-center text-sm text-gray-600"
                  >
                    <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></span>
                    {item}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
};
