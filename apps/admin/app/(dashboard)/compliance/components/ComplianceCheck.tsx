import React, { useState } from "react";
import { Badge } from "@admin/components/ui/badge";
import { But<PERSON> } from "@admin/components/ui/button";
import { LoadingSpinner } from "@admin/components/ui/loading-spinner";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON><PERSON>Footer,
} from "@admin/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@admin/components/ui/table";
import { ComplianceAlert } from "../types";
import { HandHelping, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { useUpdateComplianceStatus } from "@admin/app/lib/hooks/api-hooks";

interface ComplianceChecksProps {
  checks: any[];
  loading?: boolean;
}

export const ComplianceChecks: React.FC<ComplianceChecksProps> = ({
  checks,
  loading = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCompliance, setSelectedCompliance] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // React Query mutation
  const updateComplianceMutation = useUpdateComplianceStatus();

  const handleResolveClick = (check: any) => {
    setSelectedCompliance(check);
    setError(null); // Reset error state
    setIsModalOpen(true);
  };

  const handleApprove = async () => {
    if (!selectedCompliance) return;
    
    setError(null);
    
    try {
      await updateComplianceMutation.mutateAsync({
        checkId: selectedCompliance.check_id,
        data: { status: "Approved" }
      });
      
      console.log("Compliance approved successfully");
      setIsModalOpen(false);
      setSelectedCompliance(null);
    } catch (error: any) {
      console.error("Error approving compliance:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to approve compliance check";
      setError(`Approval failed: ${errorMessage}`);
    }
  };

  const handleReject = async () => {
    if (!selectedCompliance) return;
    
    setError(null);
    
    try {
      await updateComplianceMutation.mutateAsync({
        checkId: selectedCompliance.check_id,
        data: { status: "Rejected" }
      });
      
      console.log("Compliance rejected successfully");
      setIsModalOpen(false);
      setSelectedCompliance(null);
    } catch (error: any) {
      console.error("Error rejecting compliance:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to reject compliance check";
      setError(`Rejection failed: ${errorMessage}`);
    }
  };

  const parseComplianceDetails = (details: any) => {
    try {
      // If details is already an object, return it
      if (typeof details === 'object' && details !== null) {
        return details;
      }
      // If details is a string, try to parse it
      if (typeof details === 'string') {
        return JSON.parse(details);
      }
      return null;
    } catch (error) {
      console.error('Error parsing compliance details:', error, 'Details:', details);
      return null;
    }
  };

  const formatDate = (dateObj: any) => {
    if (!dateObj || typeof dateObj !== 'object') return 'N/A';
    return `${dateObj.day}/${dateObj.month}/${dateObj.year}`;
  };
  return (
    <div className="bg-white rounded-[12px] border">
      {loading ? (
        <div className="p-12 text-center">
          <LoadingSpinner className="w-8 h-8 text-[#05A049] mx-auto mb-4" />
          <p className="text-gray-500">Loading compliance checks...</p>
        </div>
      ) : (
        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                Alert Details
              </TableHead>
              <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </TableHead>
              <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                Severity
              </TableHead>
              <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                Due Date
              </TableHead>
              <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                Assigned To
              </TableHead>
              <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </TableHead>
              <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {checks.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="px-6 py-12 text-center">
                  <div className="text-gray-500 text-sm">
                    Nothing to show
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              checks.map((check, idx) => (
                <TableRow key={idx} className="hover:bg-gray-50">
                  <TableCell className="px-6 py-4">
                    <div className="font-semibold text-gray-900">
                      {check.alert.title}
                    </div>
                    <div className="text-gray-600 text-sm">
                      {check.alert.description}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      {check.alert.code}
                    </div>
                  </TableCell>
                <TableCell className="px-6 py-4 font-semibold text-gray-900">
                  {check.customer}
                </TableCell>
                <TableCell className="px-6 py-4">
                  <Badge
                    className={`$ {
                      check.severity === 'HIGH'
                        ? 'bg-red-100 text-red-800'
                        : check.severity === 'MEDIUM'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-[#EDF8F2] text-[#05A049]'
                    } rounded-full text-xs font-normal`}
                  >
                    {check.severity}
                  </Badge>
                </TableCell>
                <TableCell className="px-6 py-4">{check.dueDate}</TableCell>
                <TableCell className="px-6 py-4">{check.assignedTo}</TableCell>
                <TableCell className="px-6 py-4">
                  {check.status === "Open" && (
                    <Badge className="bg-blue-100 text-blue-800 rounded-full text-xs font-normal">
                      Open
                    </Badge>
                  )}
                  {check.status === "Under Review" && (
                    <Badge className="bg-yellow-100 text-yellow-800 rounded-full text-xs font-normal">
                      Under Review
                    </Badge>
                  )}
                  {check.status === "Rejected" && (
                    <Badge className="bg-red-100 text-red-800 rounded-full text-xs font-normal">
                      Rejected
                    </Badge>
                  )}
                  {check.status === "Approved" && (
                    <Badge className="bg-green-100 text-green-800 rounded-full text-xs font-normal">
                      Approved
                    </Badge>
                  )}
                </TableCell>
                <TableCell className="px-6 py-4">
                  {check.status !== "Approved" ? (
                    <button 
                      onClick={() => handleResolveClick(check)}
                      className="text-[#05A049] font-medium flex items-center gap-1 hover:underline"
                    >
                      <HandHelping className="w-4 h-4" />
                      Resolve
                    </button>
                  ) : (
                    <span className="text-gray-400 text-sm">Resolved</span>
                  )}
                </TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      )}

      {/* Compliance Details Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        {/* Loading Overlay - positioned outside DialogContent */}
        {updateComplianceMutation.isPending && (
          <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-[60] flex items-center justify-center">
            <div className="bg-white rounded-[30px] p-8 shadow-lg">
              <div className="flex flex-col items-center justify-center space-y-3">
                <LoadingSpinner className="w-8 h-8 text-[#05A049]" />
                <p className="text-gray-600 font-medium">Processing request...</p>
              </div>
            </div>
          </div>
        )}
        
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-gray-900">
              Compliance Check Details
            </DialogTitle>
          </DialogHeader>
          
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-red-800 font-medium">Error</h4>
                  <p className="text-red-700 text-sm mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}
          
          {selectedCompliance && (
            <div className="space-y-6">
              {/* Customer Information */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Customer Information</h3>
                <p className="text-gray-700">{selectedCompliance.customer}</p>
              </div>

              {/* Compliance Details */}
              {selectedCompliance.details && (() => {
                const details = parseComplianceDetails(selectedCompliance.details);
                if (!details) {
                  return (
                    <div className="space-y-4">
                      <div className="bg-yellow-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-900 mb-3">Compliance Check Information</h3>
                        <div className="text-sm space-y-2">
                          <p><span className="font-medium text-gray-600">Check Type:</span> {selectedCompliance.check_type || 'N/A'}</p>
                          <p><span className="font-medium text-gray-600">Status:</span> 
                            <Badge className={`ml-2 ${
                              selectedCompliance.status === 'pending' 
                                ? 'bg-yellow-100 text-yellow-800' 
                                : selectedCompliance.status === 'approved'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {selectedCompliance.status}
                            </Badge>
                          </p>
                          <p><span className="font-medium text-gray-600">Customer:</span> {selectedCompliance.customer || 'N/A'}</p>
                          <p><span className="font-medium text-gray-600">Check ID:</span> {selectedCompliance.check_id || 'N/A'}</p>
                        </div>
                        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                          <p className="text-sm text-red-800">
                            <strong>Note:</strong> Unable to parse detailed compliance information. Please contact support if you need to view the complete details.
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                }
                
                // Check if this is a vendor KYB check
                const isVendorKYB = selectedCompliance.check_type === "KYB" && details.vendor_name;
                
                if (isVendorKYB) {
                  // Validate vendor details structure
                  const vendorDetails = details as any;
                  const hasRequiredFields = vendorDetails.vendor_name && vendorDetails.vendor_type;
                  
                  if (!hasRequiredFields) {
                    return (
                      <div className="space-y-4">
                        <div className="bg-yellow-50 p-4 rounded-lg">
                          <h3 className="font-semibold text-gray-900 mb-3">Vendor KYB Check</h3>
                          <div className="text-sm space-y-2">
                            <p><span className="font-medium text-gray-600">Check Type:</span> {selectedCompliance.check_type || 'N/A'}</p>
                            <p><span className="font-medium text-gray-600">Status:</span> 
                              <Badge className={`ml-2 ${
                                selectedCompliance.status === 'pending' 
                                  ? 'bg-yellow-100 text-yellow-800' 
                                  : selectedCompliance.status === 'approved'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {selectedCompliance.status}
                              </Badge>
                            </p>
                            <p><span className="font-medium text-gray-600">Customer:</span> {selectedCompliance.customer || 'N/A'}</p>
                          </div>
                          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                            <p className="text-sm text-red-800">
                              <strong>Note:</strong> Vendor details are incomplete. Please contact support for assistance.
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  }
                  
                  // Display vendor information for KYB checks
                  return (
                    <div className="space-y-4">
                      {/* Vendor Information */}
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-900 mb-3">Vendor Information</h3>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-600">Vendor Name:</span>
                            <p className="text-gray-800">{details.vendor_name || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Vendor Type:</span>
                            <p className="text-gray-800">{details.vendor_type || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Contact Person:</span>
                            <p className="text-gray-800">{details.contact_name || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Contact Email:</span>
                            <p className="text-gray-800">{details.contact_email || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Phone:</span>
                            <p className="text-gray-800">{details.phone || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Website:</span>
                            <p className="text-gray-800">{details.website || 'N/A'}</p>
                          </div>
                        </div>
                      </div>

                      {/* Address Information */}
                      {details.address && (
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h3 className="font-semibold text-gray-900 mb-3">Business Address</h3>
                          <div className="text-sm space-y-2">
                            <p><span className="font-medium text-gray-600">Address:</span> {details.address || 'N/A'}</p>
                          </div>
                        </div>
                      )}

                      {/* Contract Information */}
                      <div className="bg-yellow-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-900 mb-3">Contract Information</h3>
                        <div className="text-sm space-y-2">
                          <p><span className="font-medium text-gray-600">Contract End Date:</span> {
                            details.contract_end_date 
                              ? (() => {
                                  try {
                                    return new Date(details.contract_end_date).toLocaleDateString();
                                  } catch (error) {
                                    return details.contract_end_date || 'N/A';
                                  }
                                })()
                              : 'N/A'
                          }</p>
                          <p><span className="font-medium text-gray-600">Description:</span> {details.description || 'N/A'}</p>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-900 mb-3">Additional Information</h3>
                        <div className="text-sm space-y-2">
                          <p><span className="font-medium text-gray-600">Vendor ID:</span> {details.vendor_id || 'N/A'}</p>
                          <p><span className="font-medium text-gray-600">Check Type:</span> {details.check_type || 'N/A'}</p>
                          <p><span className="font-medium text-gray-600">Check Purpose:</span> {details.check_purpose || 'N/A'}</p>
                          <p><span className="font-medium text-gray-600">Status:</span> 
                            <Badge className={`ml-2 ${
                              selectedCompliance.status === 'pending' 
                                ? 'bg-yellow-100 text-yellow-800' 
                                : selectedCompliance.status === 'approved'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {selectedCompliance.status}
                            </Badge>
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                } else {
                  // Display regular KYC information
                  return (
                    <div className="space-y-4">
                      {/* Personal Information */}
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-900 mb-3">Personal Information</h3>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-600">Citizenship:</span>
                            <p className="text-gray-800">{details.citizenship || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Date of Birth:</span>
                            <p className="text-gray-800">{formatDate(details.birthday)}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Emirates ID:</span>
                            <p className="text-gray-800">{details.emirates || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Identity:</span>
                            <p className="text-gray-800">{details.identity || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Employment Status:</span>
                            <p className="text-gray-800">{details.employmentStatus || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Source of Funds:</span>
                            <p className="text-gray-800">{details.sourceOfFunds || 'N/A'}</p>
                          </div>
                        </div>
                      </div>

                      {/* Address Information */}
                      {details.address && (
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h3 className="font-semibold text-gray-900 mb-3">Address Information</h3>
                          <div className="text-sm space-y-2">
                            <p><span className="font-medium text-gray-600">Address:</span> {details.address.address || 'N/A'}</p>
                            <p><span className="font-medium text-gray-600">City:</span> {details.address.city || 'N/A'}</p>
                            <p><span className="font-medium text-gray-600">State:</span> {details.address.state || 'N/A'}</p>
                            <p><span className="font-medium text-gray-600">Zip Code:</span> {details.address.zipcode || 'N/A'}</p>
                            <p><span className="font-medium text-gray-600">Country:</span> {details.address.country || 'N/A'}</p>
                          </div>
                        </div>
                      )}

                      {/* Accreditation Information */}
                      {details.accreditation && (
                        <div className="bg-yellow-50 p-4 rounded-lg">
                          <h3 className="font-semibold text-gray-900 mb-3">Accreditation Status</h3>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center space-x-2">
                              <div className={`w-3 h-3 rounded-full ${details.accreditation.income300k ? 'bg-green-500' : 'bg-red-500'}`}></div>
                              <span>Income $300k+: {details.accreditation.income300k ? 'Yes' : 'No'}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className={`w-3 h-3 rounded-full ${details.accreditation.assets1m ? 'bg-green-500' : 'bg-red-500'}`}></div>
                              <span>Assets $1M+: {details.accreditation.assets1m ? 'Yes' : 'No'}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className={`w-3 h-3 rounded-full ${details.accreditation.personalAssets2m ? 'bg-green-500' : 'bg-red-500'}`}></div>
                              <span>Personal Assets $2M+: {details.accreditation.personalAssets2m ? 'Yes' : 'No'}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className={`w-3 h-3 rounded-full ${details.accreditation.investments5m ? 'bg-green-500' : 'bg-red-500'}`}></div>
                              <span>Investments $5M+: {details.accreditation.investments5m ? 'Yes' : 'No'}</span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Additional Information */}
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-900 mb-3">Additional Information</h3>
                        <div className="text-sm space-y-2">
                          <p><span className="font-medium text-gray-600">Inquiry ID:</span> {details.inquiryId || 'N/A'}</p>
                          <p><span className="font-medium text-gray-600">Check Type:</span> {selectedCompliance.check_type || 'N/A'}</p>
                          <p><span className="font-medium text-gray-600">Status:</span> 
                            <Badge className={`ml-2 ${
                              selectedCompliance.status === 'pending' 
                                ? 'bg-yellow-100 text-yellow-800' 
                                : selectedCompliance.status === 'approved'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {selectedCompliance.status}
                            </Badge>
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                }
              })()}
            </div>
          )}

          <DialogFooter className="flex justify-end space-x-3 pt-6">
            <Button
              variant="outline"
              onClick={handleReject}
              disabled={updateComplianceMutation.isPending}
              className="flex items-center rounded-xl space-x-2 px-6 py-2 text-red-600 border-red-300 hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <XCircle className="w-4 h-4" />
              <span>Reject</span>
            </Button>
            <Button
              onClick={handleApprove}
              disabled={updateComplianceMutation.isPending}
              className="flex items-center rounded-xl space-x-2 px-6 py-2 bg-[#05A049] hover:bg-[#048A41] text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <CheckCircle className="w-4 h-4" />
              <span>Approve</span>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
