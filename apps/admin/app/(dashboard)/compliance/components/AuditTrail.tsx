import React from "react";
import { <PERSON><PERSON> } from "@admin/components/ui/button";
import { Badge } from "@admin/components/ui/badge";
import { LoadingSpinner } from "@admin/components/ui/loading-spinner";
import { AuditTrailEntry } from "../types";
import { HandHelping } from "lucide-react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@admin/components/ui/table";

interface AuditTrailProps {
  entries: AuditTrailEntry[];
  loading?: boolean;
}

export const AuditTrail: React.FC<AuditTrailProps> = ({ entries, loading = false }) => {
  if (loading) {
    return (
      <div className="bg-white rounded-[12px] border p-12 text-center">
        <LoadingSpinner className="w-8 h-8 text-[#05A049] mx-auto mb-4" />
        <p className="text-gray-500">Loading audit trail...</p>
      </div>
    );
  }

  if (!entries || entries.length === 0) {
    return (
      <div className="bg-white rounded-[12px] border p-12 text-center">
        <div className="mx-auto h-12 w-12 text-gray-400">
          <HandHelping className="h-12 w-12" />
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">No audit logs</h3>
        <p className="mt-1 text-sm text-gray-500">
          No audit trail entries to display at this time.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-[12px] border">
      <Table>
        <TableHeader className="bg-gray-50">
          <TableRow>
            <TableHead className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              User & Action
            </TableHead>
            <TableHead className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Resource
            </TableHead>
            <TableHead className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Timestamp
            </TableHead>
            <TableHead className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              IP Address
            </TableHead>
            <TableHead className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Result
            </TableHead>
            <TableHead className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {entries.map((entry, idx) => (
            <TableRow key={idx}>
              <TableCell className="px-6 py-4">
                <span className="font-semibold text-gray-900">{entry.user}</span>
                <div className="text-xs text-gray-500">{entry.action}</div>
              </TableCell>
              <TableCell className="px-6 py-4 text-sm text-gray-700">
                {entry.resource}
              </TableCell>
              <TableCell className="px-6 py-4 text-sm text-gray-700">
                {entry.timestamp}
              </TableCell>
              <TableCell className="px-6 py-4 text-sm text-gray-700">
                {entry.ip}
              </TableCell>
              <TableCell className="px-6 py-4">
                <Badge className="bg-[#EDF8F2] text-[#05A049] rounded-full text-xs font-normal">
                  {entry.result}
                </Badge>
              </TableCell>
              <TableCell className="px-6 py-4">
                <Button
                  variant="ghost"
                  className="text-[#05A049] hover:underline p-2"
                >
                  <p>Resolve</p>
                  <HandHelping />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
