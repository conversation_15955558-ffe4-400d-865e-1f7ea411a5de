import React from "react";
import { Badge } from "@admin/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@admin/components/ui/table";
import { ComplianceAlert } from "../types";
import { HandHelping } from "lucide-react";

interface ComplianceAlertsProps {
  alerts: ComplianceAlert[];
}

export const ComplianceAlerts: React.FC<ComplianceAlertsProps> = ({
  alerts,
}) => {
  return (
    <div className="bg-white rounded-[12px] border">
      <Table>
        <TableHeader className="bg-gray-50">
          <TableRow>
            <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Alert Details
            </TableHead>
            <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Customer
            </TableHead>
            <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Severity
            </TableHead>
            <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Due Date
            </TableHead>
            <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Assigned To
            </TableHead>
            <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </TableHead>
            <TableHead className="px-6 py-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {alerts.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="px-6 py-12 text-center">
                <div className="text-gray-500 text-sm">
                  Nothing to show
                </div>
              </TableCell>
            </TableRow>
          ) : (
            alerts.map((alert, idx) => (
            <TableRow key={idx} className="hover:bg-gray-50">
              <TableCell className="px-6 py-4">
                <div className="font-semibold text-gray-900">
                  {alert.alert.title}
                </div>
                <div className="text-gray-600 text-sm">
                  {alert.alert.description}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {alert.alert.code}
                </div>
              </TableCell>
              <TableCell className="px-6 py-4 font-semibold text-gray-900">
                {alert.customer}
              </TableCell>
              <TableCell className="px-6 py-4">
                <Badge
                  className={`$ {
                    alert.severity === 'HIGH'
                      ? 'bg-red-100 text-red-800'
                      : alert.severity === 'MEDIUM'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-[#EDF8F2] text-[#05A049]'
                  } rounded-full text-xs font-normal`}
                >
                  {alert.severity}
                </Badge>
              </TableCell>
              <TableCell className="px-6 py-4">{alert.dueDate}</TableCell>
              <TableCell className="px-6 py-4">{alert.assignedTo}</TableCell>
              <TableCell className="px-6 py-4">
                {alert.status === "Open" && (
                  <Badge className="bg-blue-100 text-blue-800 rounded-full text-xs font-normal">
                    Open
                  </Badge>
                )}
                {alert.status === "Under Review" && (
                  <Badge className="bg-yellow-100 text-yellow-800 rounded-full text-xs font-normal">
                    Under Review
                  </Badge>
                )}
                {alert.status === "Action Required" && (
                  <Badge className="bg-red-100 text-red-800 rounded-full text-xs font-normal">
                    Action Required
                  </Badge>
                )}
              </TableCell>
              <TableCell className="px-6 py-4">
                <button className="text-[#05A049] font-medium flex items-center gap-1 hover:underline">
                  <HandHelping className="w-4 h-4" />
                  Resolve
                </button>
              </TableCell>
            </TableRow>
          ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};
