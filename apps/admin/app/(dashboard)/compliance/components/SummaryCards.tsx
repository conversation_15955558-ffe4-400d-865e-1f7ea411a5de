import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { SummaryCardsProps } from "../types";

export const SummaryCards: React.FC<SummaryCardsProps> = ({
  cards,
  renderIcon,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {cards.map((card, index) => (
        <Card key={index} className="bg-white">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className={`p-3 rounded-[10px] ${card.bg}`}>
                {renderIcon(card.icon, `w-6 h-6 ${card.color}`)}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  {card.title}
                </p>
                <p className="text-2xl font-semibold mt-1">
                  {card.value.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
