import React from "react";
import { Card, CardContent } from "@admin/components/ui/card";
import { Button } from "@admin/components/ui/button";
import { FileText } from "lucide-react";

interface RegulatoryReport {
  id: string;
  title: string;
  date: string;
  status: "completed" | "pending" | "failed";
  type: string;
}

interface RegulatoryReportsProps {
  reports: RegulatoryReport[];
}

const statusMap: Record<string, { label: string; color: string }> = {
  completed: { label: "Draft", color: "bg-gray-100 text-gray-600" },
  pending: { label: "Pending", color: "bg-blue-100 text-blue-800" },
  failed: { label: "In Progress", color: "bg-yellow-100 text-yellow-800" },
};

export const RegulatoryReports: React.FC<RegulatoryReportsProps> = ({
  reports,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {reports.map((report) => {
        // Placeholder values for missing fields
        const subtitle =
          report.type === "Quarterly"
            ? "Internal Review"
            : report.type === "Annual"
              ? "Regulatory Filing"
              : "SCA Submission";
        const dueDate =
          report.type === "Quarterly"
            ? "2024-03-31"
            : report.type === "Annual"
              ? "2024-02-15"
              : "2024-01-31";
        const lastSubmitted =
          report.type === "Quarterly"
            ? "2023-12-31"
            : report.type === "Annual"
              ? "2024-01-15"
              : "2023-12-31";
        const frequency = report.type;
        const status = statusMap[report.status] || {
          label: "Draft",
          color: "bg-gray-100 text-gray-600",
        };
        return (
          <Card key={report.id} className="border rounded-[12px]">
            <CardContent className="p-6">
              <div className="flex items-center mb-2">
                <div className="p-2 bg-blue-50 rounded-lg mr-3">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="text-[16px] font-semibold text-gray-900">
                      {report.title}
                    </h3>
                    <span
                      className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${status.color}`}
                    >
                      {status.label}
                    </span>
                  </div>
                  <div className="text-gray-600 text-sm">{subtitle}</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm my-4">
                <div className="flex flex-col gap-2 text-gray-600">
                  <div>Due Date:</div>
                  <div>Last Submitted:</div>
                  <div>Frequency:</div>
                </div>
                <div className="flex flex-col gap-2 text-right font-semibold text-gray-900">
                  <div>{dueDate}</div>
                  <div>{lastSubmitted}</div>
                  <div>{frequency}</div>
                </div>
              </div>
              <div className="flex gap-2 mt-2">
                <Button className="admin_green_gradient text-white font-medium flex-1 rounded-[8px]">
                  Generate
                </Button>
                <Button variant="outline" className="font-medium rounded-[8px]">
                  View
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
