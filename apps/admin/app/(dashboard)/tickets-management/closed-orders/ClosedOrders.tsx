"use client";
import React, { useState, useMemo } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { RefreshCwIcon } from "lucide-react";
import { Card } from "@admin/components/ui/card";
import { SummaryCards } from "../components/SummaryCards";
import { SearchAndFilters } from "../components/SearchAndFilters";
import { ClosedOrdersTable } from "./components/ClosedOrdersTable";
import { RMWorkload } from "../components/RMWorkload";
import {
  rmWorkloadData,
  mapApiTicketsToTickets,
  generateSummaryFromTickets,
} from "../mockData";
import {
  StatusFilter,
  PriorityFilter,
  TicketTypeFilter,
} from "../types";
import { useTickets, useDeleteTicket } from "@admin/app/lib/hooks/api-hooks";

export const ClosedOrders = (): JSX.Element => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("All Status");
  const [priorityFilter, setPriorityFilter] = useState<PriorityFilter>("All Priority");
  const [ticketTypeFilter, setTicketTypeFilter] = useState<TicketTypeFilter>("New ticket");

  // React Query hooks
  const { data: ticketsResponse, isLoading, error, refetch } = useTickets();
  const deleteTicketMutation = useDeleteTicket();

  // Transform API data to tickets and filter for closed orders
  const allTickets = useMemo(() => {
    if (!ticketsResponse?.data?.tickets) return [];
    return mapApiTicketsToTickets(ticketsResponse.data.tickets);
  }, [ticketsResponse]);

  // Filter for closed orders (statuses that are completed/closed)
  const closedTickets = useMemo(() => {
    return allTickets.filter((ticket) => {
      const closedStatuses = ["Executed", "Rejected", "Cancelled", "Archived"];
      return closedStatuses.includes(ticket.status);
    });
  }, [allTickets]);

  // Generate summary data from closed tickets
  const summaryData = useMemo(() => {
    return generateSummaryFromTickets(closedTickets);
  }, [closedTickets]);

  const handleDeleteTicket = async (ticketId: string) => {
    try {
      await deleteTicketMutation.mutateAsync(ticketId);
    } catch (err) {
      console.error("Error deleting ticket:", err);
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  // Filter tickets based on search term, status, and priority
  const filteredTickets = closedTickets.filter((ticket) => {
    const matchesSearch = 
      ticket.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.product.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "All Status" || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === "All Priority" || ticket.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Generate summary data based on filtered tickets
  const currentSummaryData = generateSummaryFromTickets(filteredTickets);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Closed Orders
            </h1>
            <p className="text-gray-600 text-sm">
              View completed and archived product requests
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="text-white border-gray-300 hover:bg-gray-50 rounded-xl bg-green-600"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCwIcon className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          {/* Error Message */}
          {error && (
            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 text-sm">{error.message || 'An error occurred'}</p>
            </div>
          )}

          {/* Summary Cards */}
          <SummaryCards data={summaryData} />

          {/* Search and Filters */}
          <SearchAndFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            priorityFilter={priorityFilter}
            onPriorityFilterChange={setPriorityFilter}
            ticketTypeFilter={ticketTypeFilter}
            onTicketTypeFilterChange={setTicketTypeFilter}
            ticketCount={filteredTickets.length}
          />

          {/* Tickets Table */}
          {isLoading ? (
            <Card className="mb-6 p-12 bg-white">
              <div className="text-center">
                <RefreshCwIcon className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Loading closed orders...</p>
              </div>
            </Card>
          ) : (
            <>
              <ClosedOrdersTable 
                tickets={filteredTickets} 
                onDeleteTicket={handleDeleteTicket} 
                onTicketUpdated={handleRefresh}
              />

              <RMWorkload data={rmWorkloadData} />
            </>
          )}
        </div>
      </div>
    </div>
  );
}; 