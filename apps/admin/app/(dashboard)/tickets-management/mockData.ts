import {
  FileTextIcon,
  ClockIcon,
  AlertCircleIcon,
  DollarSignIcon,
} from "lucide-react";
import { Ticket, SummaryCard, RMWorkload } from "./types";
import React from "react";

// Define the API response types
export interface ApiTicketResponse {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created_at: string;
  priority?: string; // Assuming priority is a string like "HIGH", "MEDIUM", "LOW"
  updated_at: string;
  rm_id?: string | null;
  rm_name?: string | null;
  user: {
    first_name?: string;
    last_name?: string;
  };
  product: {
    name: string;
    type: string;
    riskLevel?: string;
  };
}

// Mapping function to convert API response to Ticket format
export const mapApiTicketsToTickets = (apiTickets: ApiTicketResponse[]): Ticket[] => {
  return apiTickets.map((apiTicket) => {
    // Format amount with currency, handle null/undefined
    const formattedAmount = `${apiTicket.currency === 'USD' ? '$' : (apiTicket.currency ?? '')}${(apiTicket.amount ?? 0).toLocaleString()}`;
    
    // Handle priority - set to "Unassigned" if null/undefined
    let priority: "LOW" | "MEDIUM" | "HIGH" | "Unassigned" = "Unassigned";
    if (apiTicket.priority) {
      const p = apiTicket.priority.toUpperCase();
      if (p === "LOW" || p === "MEDIUM" || p === "HIGH") {
        priority = p as "LOW" | "MEDIUM" | "HIGH";
      }
    }
    
    // Format last update time, handle null/undefined
    const lastUpdate = apiTicket.updated_at ? formatTimeAgo(new Date(apiTicket.updated_at)) : "Unknown";
    
    // Format risk level, handle null/undefined
    const riskLevel = apiTicket.product?.riskLevel?.toLowerCase();
    let riskLow, riskMedium, riskHigh;
    if (riskLevel === 'low') {
      riskLow = 'Risk: Low';
    } else if (riskLevel === 'medium') {
      riskMedium = 'Risk: Medium';
    } else if (riskLevel === 'high') {
      riskHigh = 'Risk: High';
    }
    
    return {
      id: apiTicket.id ?? "",
      customer: ((apiTicket.user?.first_name ?? "") + " " + (apiTicket.user?.last_name ?? "")).trim() || "Unknown Customer",
      product: apiTicket.product?.name ?? "Unknown Product",
      riskMedium,
      riskLow,
      riskHigh,
      amount: formattedAmount,
      rmAssigned: apiTicket.rm_name || "Unassigned",
      status: apiTicket.status ?? "Unknown",
      priority,
      lastUpdate,
      actions: [], // Add empty actions array as required by Ticket interface
    };
  });
};

// Helper function to format time ago
const formatTimeAgo = (date: Date): string => {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes} mins ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hours ago`;
  } else {
    return `${diffInDays} days ago`;
  }
};

// Function to generate summary data from tickets
export const generateSummaryFromTickets = (tickets: Ticket[]): SummaryCard[] => {
  const totalTickets = tickets.length;
  const pendingTickets = tickets.filter(ticket => 
    ticket.status === "New" || 
    ticket.status === "AssignedToRM" || 
    ticket.status === "QuoteRequested"
  ).length;
  const highPriorityTickets = tickets.filter(ticket => ticket.priority === "HIGH").length;
  
  // Calculate total value (extract numeric value from amount strings)
  const totalValue = tickets.reduce((sum, ticket) => {
    const numericValue = parseFloat(ticket.amount.replace(/[^0-9.-]+/g, ""));
    return sum + (isNaN(numericValue) ? 0 : numericValue);
  }, 0);
  
  const formattedTotalValue = totalValue >= 1000000 
    ? `$${(totalValue / 1000000).toFixed(1)}M`
    : `$${totalValue.toLocaleString()}`;
  
  return [
    {
      title: "Total Tickets",
      value: totalTickets.toString(),
      icon: React.createElement(FileTextIcon, {
        className: "w-6 h-6 text-blue-600",
      }),
    },
    {
      title: "Pending Tickets",
      value: pendingTickets.toString(),
      icon: React.createElement(ClockIcon, {
        className: "w-6 h-6 text-yellow-500",
      }),
    },
    {
      title: "High Priority",
      value: highPriorityTickets.toString(),
      icon: React.createElement(AlertCircleIcon, {
        className: "w-6 h-6 text-red-500",
      }),
    },
    {
      title: "Total Value",
      value: formattedTotalValue,
      icon: React.createElement(DollarSignIcon, {
        className: "w-6 h-6 text-[#05A049]",
      }),
    },
  ];
};

export const ticketsData: Ticket[] = [
  {
    id: "TKT-001",
    customer: "Premium Investments Ltd",
    product: "Structured Note - Tech Growth",
    riskMedium: "Risk: Medium",
    amount: "$2,500,000",
    rmAssigned: "Sarah Johnson",
    status: "QuoteReceived",
    priority: "HIGH",
    lastUpdate: "2 hours ago",
    actions: ["View", "Edit"],
  },
  {
    id: "TKT-002",
    customer: "John Martinez",
    product: "Corporate Bond Package",
    riskMedium: "Risk: Medium",
    amount: "$500,000",
    rmAssigned: "Michael Chen",
    status: "AssignedToRM",
    priority: "MEDIUM",
    lastUpdate: "4 hours ago",
    actions: ["View", "Edit"],
  },
  {
    id: "TKT-003",
    customer: "Global Wealth Partners",
    product: "Multi-Currency Deposit",
    riskLow: "Risk: Low",
    amount: "$1,200,000",
    rmAssigned: "Emma Wilson",
    status: "Approved",
    priority: "HIGH",
    lastUpdate: "1 day ago",
    actions: ["View", "Edit"],
  },
  {
    id: "TKT-004",
    customer: "Tech Innovations Fund",
    product: "ESG Structured Product",
    riskMedium: "Risk: Medium",
    amount: "$800,000",
    rmAssigned: "Unassigned",
    status: "New",
    priority: "MEDIUM",
    lastUpdate: "30 mins ago",
    actions: ["View", "Edit"],
  },
];

export const summaryData: SummaryCard[] = [
  {
    title: "Total Tickets",
    value: "4",
    icon: React.createElement(FileTextIcon, {
      className: "w-6 h-6 text-blue-600",
    }),
  },
  {
    title: "Pending Tickets",
    value: "2",
    icon: React.createElement(ClockIcon, {
      className: "w-6 h-6 text-yellow-500",
    }),
  },
  {
    title: "High Priority",
    value: "2",
    icon: React.createElement(AlertCircleIcon, {
      className: "w-6 h-6 text-red-500",
    }),
  },
  {
    title: "Total Value",
    value: "$5.0M",
    icon: React.createElement(DollarSignIcon, {
      className: "w-6 h-6 text-[#05A049]",
    }),
  },
];

export const rmWorkloadData: RMWorkload[] = [
  {
    name: "Sarah Johnson",
    role: "Senior RM",
    activeTickets: "17",
    ticketCount: 17,
  },
  {
    name: "Michael Chen",
    role: "RM",
    activeTickets: "8",
    ticketCount: 8,
  },
  {
    name: "Emma Wilson",
    role: "Senior RM",
    activeTickets: "15",
    ticketCount: 15,
  },
  {
    name: "David Park",
    role: "Senior RM",
    activeTickets: "6",
    ticketCount: 6,
  },
];
