export interface Ticket {
  id: string;
  customer: string;
  product: string;
  riskMedium?: string;
  riskLow?: string;
  riskHigh?: string;
  amount: string;
  rmAssigned: string;
  status: string;
  priority: "HIGH" | "MEDIUM" | "LOW" | "Unassigned";
  lastUpdate: string;
  actions: string[];
}

export interface SummaryCard {
  title: string;
  value: string;
  icon: React.ReactNode;
}

export interface RMWorkload {
  name: string;
  role: string;
  activeTickets: string;
  ticketCount: number;
}

export type StatusFilter =
  | "All Status"
  | "New"
  | "AssignedToRM"
  | "QuoteRequested"
  | "QuoteReceived"
  | "Approved"
  | "Executed"
  | "Rejected"
  | "Cancelled"
  | "Archived";
export type PriorityFilter = "All Priority" | "HIGH" | "MEDIUM" | "LOW" | "Unassigned";
export type TicketTypeFilter =
  | "New ticket"
  | "All Priority"
  | "HIGH Priority"
  | "MEDIUM Priority"
  | "LOW Priority"
  | "Unassigned Priority";
