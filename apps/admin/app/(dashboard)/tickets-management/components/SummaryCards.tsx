import React, { ReactElement } from "react";
import { SummaryCard } from "../types";

interface SummaryCardsProps {
  data: SummaryCard[];
}

export const SummaryCards: React.FC<SummaryCardsProps> = ({ data }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      {data.map((item, index) => {
        const icon = item.icon as ReactElement;
        const iconClassName = icon.props.className;

        return (
          <div
            key={index}
            className="flex items-center bg-white rounded-2xl shadow-md px-6 py-6 min-h-[110px]"
            style={{ minWidth: 0 }}
          >
            <div
              className="flex items-center justify-center w-12 h-12 rounded-xl mr-5"
              style={{
                backgroundColor: iconClassName.includes("blue")
                  ? "#e0e7ff"
                  : iconClassName.includes("yellow")
                    ? "#fef9c3"
                    : iconClassName.includes("red")
                      ? "#fee2e2"
                      : "#EDF8F2",
              }}
            >
              {React.cloneElement(icon, {
                className: iconClassName + " w-7 h-7",
              })}
            </div>
            <div>
              <div className="text-sm text-gray-600 mb-1 font-medium">
                {item.title}
              </div>
              <div className="text-2xl font-extrabold text-gray-900">
                {item.value}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
