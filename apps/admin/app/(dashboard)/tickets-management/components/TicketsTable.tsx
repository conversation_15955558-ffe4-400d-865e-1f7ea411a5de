import React, { useState } from "react";
import { Badge } from "@admin/components/ui/badge";
import { Button } from "@admin/components/ui/button";
import { Card, CardContent } from "@admin/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@admin/components/ui/table";
import { UserIcon, EyeIcon, PencilIcon, Trash2Icon, FileTextIcon } from "lucide-react";
import { Ticket } from "../types";
import { EditTicketDialog } from "./EditTicketDialog";
import { OrderSummaryModal } from "../../orders-management/components/OrderSummaryModal";

interface TicketsTableProps {
  tickets: Ticket[];
  onDeleteTicket?: (ticketId: string) => void;
  onTicketUpdated?: () => void;
}

export const TicketsTable: React.FC<TicketsTableProps> = ({ tickets, onDeleteTicket, onTicketUpdated }) => {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);
  const [summaryModalOpen, setSummaryModalOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);

  const handleEditClick = (ticketId: string) => {
    setSelectedTicketId(ticketId);
    setEditDialogOpen(true);
  };

  const handleDeleteClick = (ticketId: string, customerName: string) => {
    if (window.confirm(`Are you sure you want to archive the ticket for ${customerName}? This action cannot be undone.`)) {
      onDeleteTicket?.(ticketId);
    }
  };

  const handleViewSummary = (ticket: Ticket) => {
    setSelectedTicket(ticket);
    setSummaryModalOpen(true);
  };
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "QuoteReceived":
        return "bg-blue-100 text-blue-800";
      case "AssignedToRM":
        return "bg-yellow-100 text-yellow-800";
      case "Approved":
        return "bg-[#EDF8F2] text-[#05A049]";
      case "New":
        return "bg-purple-100 text-purple-800";
      case "Archived":
        return "bg-orange-100 text-orange-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-[#EDF8F2] text-[#05A049]";
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case "HIGH":
        return "bg-red-100 text-red-800";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800";
      case "LOW":
        return "bg-[#EDF8F2] text-[#05A049]";
      case "Unassigned":
        return "bg-gray-100 text-gray-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  // Empty state component
  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <FileTextIcon className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">No tickets found</h3>
      <p className="text-gray-600 text-center max-w-md">
        There are currently no tickets to display. New tickets will appear here when they are created.
      </p>
    </div>
  );

  return (
    <div className="bg-white rounded-[10px] mb-6 overflow-hidden">
      <Table className="p-6">
        <TableHeader className="bg-gray-50">
          <TableRow className="hover:bg-gray-200">
            <TableHead className="py-4">TICKET ID</TableHead>
            <TableHead className="py-4">CUSTOMER & PRODUCT</TableHead>
            <TableHead className="py-4">AMOUNT</TableHead>
            <TableHead className="py-4">RM ASSIGNED</TableHead>
            <TableHead className="py-4">STATUS</TableHead>
            <TableHead className="py-4">PRIORITY</TableHead>
            <TableHead className="py-4">LAST UPDATE</TableHead>
            <TableHead className="py-4">ACTIONS</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tickets.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="p-0">
                <EmptyState />
              </TableCell>
            </TableRow>
          ) : (
            tickets.map((ticket) => (
              <TableRow key={ticket.id} className="hover:bg-gray-50">
                <TableCell className="py-4 font-medium">{ticket.id}</TableCell>
                <TableCell className="py-4">
                  <div>
                    <p className="font-medium text-gray-900">{ticket.customer}</p>
                    <p className="text-sm text-gray-600">{ticket.product}</p>
                    <Badge className="mt-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                      {ticket.riskMedium || ticket.riskLow || ticket.riskHigh}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell className="py-4">{ticket.amount}</TableCell>
                <TableCell className="py-4">
                  {ticket.rmAssigned === "Unassigned" ? (
                    <span className="italic text-gray-400">Unassigned</span>
                  ) : (
                    <div className="flex items-center gap-3">
                      <span className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center">
                        <UserIcon className="w-5 h-5 text-blue-500" />
                      </span>
                      <span className="font-medium text-gray-900">
                        {ticket.rmAssigned}
                      </span>
                    </div>
                  )}
                </TableCell>
                <TableCell className="py-4">
                  <Badge
                    className={`${getStatusBadgeColor(ticket.status)} text-xs rounded-full`}
                  >
                    {ticket.status}
                  </Badge>
                </TableCell>
                <TableCell className="py-4">
                  <Badge
                    className={`${getPriorityBadgeColor(ticket.priority)} text-xs rounded-full`}
                  >
                    {ticket.priority}
                  </Badge>
                </TableCell>
                <TableCell className="py-4 text-sm text-gray-600">
                  {ticket.lastUpdate}
                </TableCell>
                <TableCell className="py-4">
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#05A049] hover:text-[#05A049]/80 p-2"
                      onClick={() => handleViewSummary(ticket)}
                      title="View Order Summary"
                    >
                      <EyeIcon className="w-5 h-5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-gray-800 p-2"
                      onClick={() => handleEditClick(ticket.id)}
                    >
                      <PencilIcon className="w-5 h-5" />
                    </Button>
                    {ticket.status !== "Archived" && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 p-2"
                        onClick={() => handleDeleteClick(ticket.id, ticket.customer)}
                      >
                        <Trash2Icon className="w-5 h-5" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      
      <EditTicketDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        ticketId={selectedTicketId}
        onTicketUpdated={onTicketUpdated}
      />
      
      <OrderSummaryModal
        isOpen={summaryModalOpen}
        onClose={() => setSummaryModalOpen(false)}
        ticket={selectedTicket}
      />
    </div>
  );
};
