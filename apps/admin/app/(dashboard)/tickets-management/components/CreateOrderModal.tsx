import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { X, Calendar, ChevronDown, Loader2 } from "lucide-react";
import { apiService } from "@admin/app/lib/api-service";

interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number?: string;
}

interface Vendor {
  vendor_id: string;
  name: string;
  type: string;
  contact_name: string;
  contact_email: string;
  phone: string;
}

interface CreateOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
}

export const CreateOrderModal: React.FC<CreateOrderModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [formData, setFormData] = useState({
    customerId: "",
    customerName: "",
    customerEmail: "",
    customerPhone: "",
    productType: "",
    productName: "",
    investmentAmount: "",
    quantity: "",
    maturityDate: "",
    currency: "USD - US Dollar",
    priority: "MEDIUM",
    riskLevel: "MEDIUM",
    assignedRM: "",
    assignedVendor: "",
    underlyingAssets: "",
    description: "",
    specialRequirements: "",
  });

  const [users, setUsers] = useState<User[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [isLoadingVendors, setIsLoadingVendors] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch users and vendors from database
  useEffect(() => {
    if (isOpen) {
      fetchUsers();
      fetchVendors();
    }
  }, [isOpen]);

  const fetchUsers = async () => {
    setIsLoadingUsers(true);
    setError(null);
    try {
      const response = await apiService.getUsers();
      setUsers(response.data || []);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users. Please try again.');
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const fetchVendors = async () => {
    setIsLoadingVendors(true);
    setError(null);
    try {
      const response = await apiService.getApprovedVendors();
      setVendors(response.data || []);
    } catch (err) {
      console.error('Error fetching vendors:', err);
      setError('Failed to load vendors. Please try again.');
    } finally {
      setIsLoadingVendors(false);
    }
  };

  const handleUserSelect = (userId: string) => {
    const selectedUser = users.find(user => user.id === userId);
    if (selectedUser) {
      setFormData(prev => ({
        ...prev,
        customerId: userId,
        customerName: `${selectedUser.first_name} ${selectedUser.last_name}`,
        customerEmail: selectedUser.email,
        customerPhone: selectedUser.mobile_number || "",
      }));
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.customerId || !formData.productType || !formData.productName || !formData.investmentAmount || !formData.description || !formData.assignedVendor) {
        throw new Error('Please fill in all required fields');
      }

      // Create order API call
      const response = await apiService.post('/admin/ticket', {
        user_id: formData.customerId,
        product_type: formData.productType,
        product_name: formData.productName,
        amount: parseInt(formData.investmentAmount),
        currency: formData.currency ? formData.currency.split(' - ')[0] : 'USD', // Extract currency code
        quantity: formData.quantity ? parseInt(formData.quantity) : null,
        maturity_date: formData.maturityDate || null,
        priority_level: formData.priority,
        risk_level: formData.riskLevel,
        rm_id: formData.assignedRM,
        vendor_id: formData.assignedVendor,
        description: formData.description,
        special_requirements: formData.specialRequirements,
        status: "New"
      });

      console.log('Order created successfully:', response);

      // Call the onSubmit callback
      if (onSubmit) {
        await onSubmit(response.data);
      }

      // Reset form and close modal
      setFormData({
        customerId: "",
        customerName: "",
        customerEmail: "",
        customerPhone: "",
        productType: "",
        productName: "",
        investmentAmount: "",
        quantity: "",
        maturityDate: "",
        currency: "USD - US Dollar",
        priority: "MEDIUM",
        riskLevel: "MEDIUM",
        assignedRM: "",
        assignedVendor: "",
        underlyingAssets: "",
        description: "",
        specialRequirements: "",
      });
      onClose();
    } catch (err) {
      console.error('Error creating order:', err);
      setError(err instanceof Error ? err.message : 'Failed to create order');
    } finally {
      setIsSubmitting(false);
    }
  };

  const productTypeOptions = [
    "Bond",
    "Stock",
    "Fund",
    "Structured",
    "ETFs",
    "CryptoETF",
    "CryptoFunds",
    "MutualFunds",
    "Riets"
  ];

  const currencyOptions = ["USD - US Dollar", "EUR - Euro", "GBP - British Pound", "JPY - Japanese Yen", "SGD - Singapore Dollar", "AUD - Australian Dollar"];
  const priorityOptions = ["HIGH", "MEDIUM", "LOW"];
  const riskLevelOptions = ["LOW", "MEDIUM", "HIGH"];
  // RM options with their IDs (these would ideally come from an API)
  const rmOptions = [
    { id: "", name: "Select RM" },
    { id: "", name: "John Smith" },
    { id: "", name: "Sarah Johnson" },
    { id: "", name: "Mike Davis" },
    { id: "", name: "Lisa Wilson" }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl w-full max-w-2xl relative overflow-hidden shadow-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-[#05A049]">
            Create New Order
          </h2>
          <button
            className="text-gray-400 hover:text-gray-600 transition-colors"
            onClick={onClose}
            aria-label="Close"
            disabled={isSubmitting}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Customer Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Customer Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Customer *
                </label>
                <div className="relative">
                  <select
                    value={formData.customerId}
                    onChange={(e) => handleUserSelect(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] appearance-none bg-white"
                    required
                    disabled={isLoadingUsers || isSubmitting}
                  >
                    <option value="">Select Customer</option>
                    {users.map((user) => (
                      <option key={user.id} value={user.id}>
                        {user.first_name} {user.last_name}
                      </option>
                    ))}
                  </select>
                  {isLoadingUsers && (
                    <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 animate-spin" />
                  )}
                  {!isLoadingUsers && (
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer Name
                </label>
                <input
                  type="text"
                  value={formData.customerName}
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 bg-gray-100 focus:outline-none"
                  placeholder="Auto-filled from selection"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={formData.customerEmail}
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 bg-gray-100 focus:outline-none"
                  placeholder="Auto-filled from selection"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.customerPhone}
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 bg-gray-100 focus:outline-none"
                  placeholder="Auto-filled from selection"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assign RM
                </label>
                <div className="relative">
                  <select
                    value={formData.assignedRM}
                    onChange={(e) => handleInputChange("assignedRM", e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] appearance-none bg-white"
                    disabled={isSubmitting}
                  >
                    {rmOptions.map((rm) => (
                      <option key={rm.id} value={rm.id}>{rm.name}</option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Vendor *
                </label>
                <div className="relative">
                  <select
                    value={formData.assignedVendor}
                    onChange={(e) => handleInputChange("assignedVendor", e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] appearance-none bg-white"
                    required
                    disabled={isLoadingVendors || isSubmitting}
                  >
                    <option value="">Select Vendor</option>
                    {vendors.map((vendor) => (
                      <option key={vendor.vendor_id} value={vendor.vendor_id}>
                        {vendor.name} ({vendor.type})
                      </option>
                    ))}
                  </select>
                  {isLoadingVendors && (
                    <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 animate-spin" />
                  )}
                  {!isLoadingVendors && (
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Product Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Product Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Type *
                </label>
                <div className="relative">
                  <select
                    value={formData.productType}
                    onChange={(e) => handleInputChange("productType", e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] appearance-none bg-white"
                    required
                    disabled={isSubmitting}
                  >
                    <option value="">Select Product Type</option>
                    {productTypeOptions.map((type) => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Name *
                </label>
                <input
                  type="text"
                  value={formData.productName}
                  onChange={(e) => handleInputChange("productName", e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                  placeholder="e.g., Tech Basket Structured Note"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Investment Amount *
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={formData.investmentAmount}
                    onChange={(e) => handleInputChange("investmentAmount", e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                    placeholder="Enter amount"
                    required
                    disabled={isSubmitting}
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="flex flex-col">
                      <button type="button" className="text-gray-400 hover:text-gray-600 text-xs">▲</button>
                      <button type="button" className="text-gray-400 hover:text-gray-600 text-xs">▼</button>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity
                </label>
                <input
                  type="number"
                  value={formData.quantity}
                  onChange={(e) => handleInputChange("quantity", e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                  placeholder="Enter quantity"
                  disabled={isSubmitting}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <div className="relative">
                  <select
                    value={formData.currency}
                    onChange={(e) => handleInputChange("currency", e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] appearance-none bg-white"
                    disabled={isSubmitting}
                  >
                    {currencyOptions.map((currency) => (
                      <option key={currency} value={currency}>{currency}</option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maturity Date
                </label>
                <div className="relative">
                  <input
                    type="date"
                    value={formData.maturityDate}
                    onChange={(e) => handleInputChange("maturityDate", e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                    min={new Date().toISOString().split('T')[0]}
                    disabled={isSubmitting}
                  />
                  <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>
          </div>

          {/* Order Details */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Order Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority Level
                </label>
                <div className="relative">
                  <select
                    value={formData.priority}
                    onChange={(e) => handleInputChange("priority", e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] appearance-none bg-white"
                    disabled={isSubmitting}
                  >
                    {priorityOptions.map((priority) => (
                      <option key={priority} value={priority}>{priority}</option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Risk Level
                </label>
                <div className="relative">
                  <select
                    value={formData.riskLevel}
                    onChange={(e) => handleInputChange("riskLevel", e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] appearance-none bg-white"
                    disabled={isSubmitting}
                  >
                    {riskLevelOptions.map((risk) => (
                      <option key={risk} value={risk}>{risk}</option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Underlying Assets
              </label>
              <input
                type="text"
                value={formData.underlyingAssets}
                onChange={(e) => handleInputChange("underlyingAssets", e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049]"
                placeholder="e.g., AAPL, MSFT, GOOGL"
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Additional Information</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] resize-none"
                  rows={3}
                  placeholder="Detailed description of the structured product requirements..."
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Special Requirements
                </label>
                <textarea
                  value={formData.specialRequirements}
                  onChange={(e) => handleInputChange("specialRequirements", e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#05A049] focus:border-[#05A049] resize-none"
                  rows={2}
                  placeholder="Any special requirements or notes..."
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              onClick={onClose}
              className="flex-1 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 rounded-lg py-3"
              disabled={isSubmitting}
            >
              Discard
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-[#05A049] hover:bg-[#05A049]/90 text-white rounded-lg py-3"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating Order...
                </>
              ) : (
                'Create Order'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}; 