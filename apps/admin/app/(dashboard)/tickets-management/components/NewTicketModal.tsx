import React from "react";
import { But<PERSON> } from "@admin/components/ui/button";
import { PlusIcon } from "lucide-react";
import { CustomSelect } from "@admin/components/ui/CustomSelect";

interface NewTicketModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCustomer: string;
  setSelectedCustomer: (value: string) => void;
  selectedProductType: string;
  setSelectedProductType: (value: string) => void;
  selectedPriority: string;
  setSelectedPriority: (value: string) => void;
  selectedRisk: string;
  setSelectedRisk: (value: string) => void;
  selectedCurrency: string;
  setSelectedCurrency: (value: string) => void;
  selectedRM: string;
  setSelectedRM: (value: string) => void;
}

export const NewTicketModal: React.FC<NewTicketModalProps> = ({
  isOpen,
  onClose,
  selectedCustomer,
  setSelectedCustomer,
  selectedProductType,
  setSelectedProductType,
  selectedPriority,
  setSelectedPriority,
  selectedRisk,
  setSelectedRisk,
  selectedCurrency,
  setSelectedCurrency,
  selectedRM,
  setSelectedRM,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="bg-[#FFFFFC] rounded-xl w-full max-w-2xl relative overflow-hidden [box-shadow:0px_8px_10px_-6px_rgba(0,0,0,0.1),0px_20px_25px_-5px_rgba(0,0,0,0.1)]">
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl"
          onClick={onClose}
          aria-label="Close"
        >
          &times;
        </button>
        <div className="p-6">
          <div className="flex items-center mb-6">
            <span className="bg-[#D1D5DB]/10 text-[#05A049] rounded-full p-2 mr-3">
              <PlusIcon className="w-5 h-5" />
            </span>
            <h2 className="text-xl font-semibold text-gray-900">
              Create New Ticket
            </h2>
          </div>
          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Customer *
                </label>
                <CustomSelect
                  options={[
                    "Premium Investments Ltd",
                    "John Martinez",
                    "Global Wealth Partners",
                    "Tech Innovations Fund",
                  ]}
                  value={selectedCustomer}
                  onChange={setSelectedCustomer}
                  placeholder="Select Customer"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Type *
                </label>
                <CustomSelect
                  options={[
                    "Structured Product",
                    "Corporate Bond Package",
                    "Multi-Currency Deposit",
                    "ESG Structured Product",
                  ]}
                  value={selectedProductType}
                  onChange={setSelectedProductType}
                  placeholder="Select Product Type"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Name *
                </label>
                <input
                  type="text"
                  className="w-full border border-gray-300 rounded-[8px] px-3 py-2"
                  placeholder="e.g., Tech Basket Structured Note"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Investment Amount *
                </label>
                <div className="flex">
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-[8px] px-3 py-2"
                    placeholder="$  2,500,000"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Priority Level
                </label>
                <CustomSelect
                  options={["HIGH", "MEDIUM", "LOW"]}
                  value={selectedPriority}
                  onChange={setSelectedPriority}
                  placeholder="Select Priority"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Risk Level
                </label>
                <CustomSelect
                  options={["HIGH", "MEDIUM", "LOW"]}
                  value={selectedRisk}
                  onChange={setSelectedRisk}
                  placeholder="Select Risk"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency
                </label>
                <CustomSelect
                  options={["USD", "EUR", "GBP", "JPY", "SGD", "AUD"]}
                  value={selectedCurrency}
                  onChange={setSelectedCurrency}
                  placeholder="Select Currency"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Maturity Date
                </label>
                <input
                  type="date"
                  className="w-full border border-gray-300 rounded-[8px] px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Assign RM
                </label>
                <CustomSelect
                  options={[
                    "Sarah Johnson",
                    "Michael Chen",
                    "Emma Wilson",
                    "David Park",
                  ]}
                  value={selectedRM}
                  onChange={setSelectedRM}
                  placeholder="Select Relationship Manager"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Underlying Assets
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-[8px] px-3 py-2"
                placeholder="e.g., AAPL, MSFT, GOOGL"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Description *
              </label>
              <textarea
                className="w-[624px] h-[90px] fox border border-gray-300 rounded-[8px] px-3 py-2 resize-none"
                rows={3}
                placeholder="Detailed description of the structured product requirements..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Special Requirements
              </label>
              <textarea
                className="w-[624px] h-[66pz] resize-none border border-gray-300 rounded-[8px] px-3 py-2"
                rows={2}
                placeholder="Any special requirements or notes..."
              />
            </div>
            <div className="flex justify-end pt-2">
              <Button
                onClick={onClose}
                className="bg-white text-black border border-gray-300 rounded-[8px] mr-3"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#05A049] hover:bg-[#05A049]/90 text-white rounded-[8px]"
              >
                Create Ticket
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
