#!/bin/bash

# Valura AI - Dev Environment Setup Script
# This script sets up the complete development environment in ValuraDev account

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
AWS_REGION="ap-south-1"
ENVIRONMENT="development"
PROJECT_NAME="valura-ai"

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if required tools are installed
    local tools=("terraform" "aws" "gh" "jq")
    for tool in "${tools[@]}"; do
        if ! command -v $tool &> /dev/null; then
            print_error "$tool is not installed. Please install it first."
            exit 1
        fi
    done
    
    # Check AWS credentials
    if ! aws sts get-caller-identity --region $AWS_REGION &> /dev/null; then
        print_error "AWS credentials not configured for region $AWS_REGION"
        print_status "Please run: aws configure --profile valura-dev"
        exit 1
    fi
    
    # Check GitHub CLI authentication
    if ! gh auth status &> /dev/null; then
        print_error "Please authenticate with GitHub CLI: gh auth login"
        exit 1
    fi
    
    print_success "Prerequisites check completed"
}

# Function to set up AWS billing alerts
setup_billing_alerts() {
    print_status "Setting up AWS billing alerts..."
    
    # Create SNS topic for billing alerts
    local topic_arn=$(aws sns create-topic --name valura-dev-billing-alerts --region us-east-1 --output text --query 'TopicArn' 2>/dev/null || echo "")
    
    if [ -n "$topic_arn" ]; then
        print_success "Created SNS topic for billing alerts: $topic_arn"
        
        # Subscribe email to topic (you'll need to confirm subscription)
        read -p "Enter your email for billing alerts: " email
        if [ -n "$email" ]; then
            aws sns subscribe --topic-arn $topic_arn --protocol email --notification-endpoint $email --region us-east-1
            print_warning "Please check your email and confirm the subscription"
        fi
        
        # Create billing alarms
        local thresholds=(25 50 75 100)
        for threshold in "${thresholds[@]}"; do
            aws cloudwatch put-metric-alarm \
                --alarm-name "valura-dev-billing-$threshold" \
                --alarm-description "Billing alert for \$$threshold" \
                --metric-name EstimatedCharges \
                --namespace AWS/Billing \
                --statistic Maximum \
                --period 86400 \
                --threshold $threshold \
                --comparison-operator GreaterThanThreshold \
                --dimensions Name=Currency,Value=USD \
                --evaluation-periods 1 \
                --alarm-actions $topic_arn \
                --region us-east-1 2>/dev/null || echo "Failed to create alarm for \$$threshold"
        done
        
        print_success "Created billing alarms for \$25, \$50, \$75, \$100"
    else
        print_warning "Could not create billing alerts - continuing without them"
    fi
}

# Function to deploy infrastructure
deploy_infrastructure() {
    print_status "Deploying infrastructure with Terraform..."
    
    cd infrastructure/terraform
    
    # Create terraform.tfvars for dev environment
    if [ ! -f "terraform.tfvars.dev" ]; then
        print_status "Creating terraform.tfvars.dev file..."
        
        read -p "Enter your dev domain (e.g., dev.yourdomain.com): " domain_name
        read -s -p "Enter database password: " db_password
        echo
        
        cat > terraform.tfvars.dev << EOF
# Development Environment Configuration
domain_name = "$domain_name"
database_password = "$db_password"
EOF
        
        print_success "Created terraform.tfvars.dev"
    fi
    
    # Initialize and deploy
    terraform init
    terraform plan -var-file="terraform.tfvars.dev" -target=module.dev || terraform plan -var-file="terraform.tfvars.dev"
    
    read -p "Do you want to apply this plan? (y/N): " confirm
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        terraform apply -var-file="terraform.tfvars.dev" -auto-approve
        print_success "Infrastructure deployed successfully"
    else
        print_warning "Infrastructure deployment cancelled"
        exit 1
    fi
    
    cd ../..
}

# Function to create Amplify apps
create_amplify_apps() {
    print_status "Creating AWS Amplify applications..."
    
    local apps=("web" "admin" "landing")
    local app_ids=()
    
    for app in "${apps[@]}"; do
        print_status "Creating Amplify app for $app..."
        
        # Create Amplify app
        local app_id=$(aws amplify create-app \
            --name "valura-$app-dev" \
            --description "Valura AI $app application - Development" \
            --repository "https://github.com/$(gh repo view --json owner,name -q '.owner.login + "/" + .name')" \
            --platform WEB \
            --environment-variables NODE_ENV=development \
            --region $AWS_REGION \
            --output text --query 'app.appId' 2>/dev/null || echo "")
        
        if [ -n "$app_id" ]; then
            print_success "Created Amplify app for $app: $app_id"
            app_ids+=("AMPLIFY_APP_ID_${app^^}_DEV=$app_id")
            
            # Create branch
            aws amplify create-branch \
                --app-id $app_id \
                --branch-name dev \
                --description "Development branch" \
                --enable-auto-build \
                --region $AWS_REGION >/dev/null 2>&1 || echo "Branch creation failed"
        else
            print_warning "Failed to create Amplify app for $app"
        fi
    done
    
    # Store app IDs for later use
    printf '%s\n' "${app_ids[@]}" > /tmp/amplify_app_ids.txt
    print_success "Amplify apps created. App IDs saved to /tmp/amplify_app_ids.txt"
}

# Function to set up GitHub secrets
setup_github_secrets() {
    print_status "Setting up GitHub secrets..."
    
    # Get AWS account ID
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    
    # Basic AWS secrets
    read -s -p "Enter AWS Access Key ID for ValuraDev account: " aws_access_key
    echo
    read -s -p "Enter AWS Secret Access Key for ValuraDev account: " aws_secret_key
    echo
    
    # Set basic secrets
    echo "$aws_access_key" | gh secret set AWS_ACCESS_KEY_ID_INDIA
    echo "$aws_secret_key" | gh secret set AWS_SECRET_ACCESS_KEY_INDIA
    echo "$AWS_REGION" | gh secret set AWS_REGION_INDIA
    
    # Set ECR registry
    echo "$account_id.dkr.ecr.$AWS_REGION.amazonaws.com" | gh secret set ECR_REGISTRY_INDIA
    
    # Get database URL from Terraform output
    cd infrastructure/terraform
    local db_url=$(terraform output -raw database_connection_string 2>/dev/null || echo "")
    if [ -n "$db_url" ]; then
        echo "$db_url" | gh secret set DATABASE_URL_DEV
        print_success "Set DATABASE_URL_DEV from Terraform output"
    fi
    cd ../..
    
    # Set Amplify app IDs if they exist
    if [ -f "/tmp/amplify_app_ids.txt" ]; then
        while IFS= read -r line; do
            local secret_name=$(echo "$line" | cut -d'=' -f1)
            local secret_value=$(echo "$line" | cut -d'=' -f2)
            echo "$secret_value" | gh secret set "$secret_name"
            print_success "Set GitHub secret: $secret_name"
        done < /tmp/amplify_app_ids.txt
        rm /tmp/amplify_app_ids.txt
    fi
    
    # Set application environment variables
    print_status "Setting application environment variables..."
    
    # API URL (will be updated after App Runner deployment)
    echo "https://api-dev.yourdomain.com" | gh secret set NEXT_PUBLIC_API_URL_DEV
    
    # Cognito (you'll need to set these manually)
    print_warning "Please set these secrets manually in GitHub:"
    echo "  - NEXT_PUBLIC_USER_POOL_ID"
    echo "  - NEXT_PUBLIC_USER_POOL_CLIENT_ID"
    echo "  - NEXT_PUBLIC_PERSONA_TEMPLATE_ID"
    echo "  - NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID"
    echo "  - NEXT_PUBLIC_POSTHOG_KEY"
    echo "  - NEXT_PUBLIC_POSTHOG_HOST"
    echo "  - RESEND_API_KEY"
    echo "  - NEXT_PUBLIC_SUPABASE_URL"
    echo "  - NEXT_PUBLIC_SUPABASE_ANON_KEY"
    
    print_success "GitHub secrets configured"
}

# Function to test the setup
test_setup() {
    print_status "Testing the setup..."
    
    # Check if we can access AWS resources
    aws rds describe-db-instances --region $AWS_REGION --query 'DBInstances[?DBInstanceIdentifier==`valura-dev-db`].DBInstanceStatus' --output text
    
    # Check ECR repository
    aws ecr describe-repositories --repository-names valura-api-dev --region $AWS_REGION >/dev/null 2>&1 && print_success "ECR repository accessible" || print_warning "ECR repository not found"
    
    # Check Amplify apps
    local app_count=$(aws amplify list-apps --region $AWS_REGION --query 'apps[?starts_with(name, `valura-`) && ends_with(name, `-dev`)]' --output json | jq length)
    print_success "Found $app_count Amplify apps"
    
    print_success "Setup test completed"
}

# Function to display next steps
show_next_steps() {
    print_success "Development environment setup completed!"
    echo
    print_status "Next steps:"
    echo "1. Update your applications' next.config.js files for static export"
    echo "2. Set the remaining GitHub secrets manually"
    echo "3. Create and push to the dev branch to test CI/CD:"
    echo "   git checkout -b dev"
    echo "   git push origin dev"
    echo "4. Monitor AWS costs in the billing dashboard"
    echo "5. Check Amplify deployments in the AWS console"
    echo
    print_status "Useful commands:"
    echo "  - Check costs: aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31 --granularity MONTHLY --metrics BlendedCost"
    echo "  - List Amplify apps: aws amplify list-apps --region $AWS_REGION"
    echo "  - Check RDS status: aws rds describe-db-instances --region $AWS_REGION"
    echo
    print_warning "Remember to monitor your AWS costs closely during the first week!"
}

# Main function
main() {
    print_status "Starting Valura AI Development Environment Setup"
    print_warning "This will create AWS resources that may incur charges"
    
    read -p "Continue with setup? (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        print_status "Setup cancelled"
        exit 0
    fi
    
    check_prerequisites
    setup_billing_alerts
    deploy_infrastructure
    create_amplify_apps
    setup_github_secrets
    test_setup
    show_next_steps
}

# Run main function
main "$@"
