#!/bin/bash

# Valura AI Infrastructure Deployment Script
# This script helps deploy the Terraform infrastructure for different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install Terraform first."
        exit 1
    fi
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install AWS CLI first."
        exit 1
    fi
    
    # Check Terraform version
    TERRAFORM_VERSION=$(terraform version -json | jq -r '.terraform_version')
    print_status "Terraform version: $TERRAFORM_VERSION"
    
    # Check AWS CLI version
    AWS_VERSION=$(aws --version 2>&1 | cut -d/ -f2 | cut -d' ' -f1)
    print_status "AWS CLI version: $AWS_VERSION"
    
    print_success "Prerequisites check completed"
}

# Function to validate AWS credentials
validate_aws_credentials() {
    print_status "Validating AWS credentials..."
    
    # Check UAE region credentials
    print_status "Checking UAE region (me-central-1) credentials..."
    if ! aws sts get-caller-identity --region me-central-1 &> /dev/null; then
        print_error "AWS credentials for UAE region (me-central-1) are not configured properly"
        exit 1
    fi
    
    # Check India region credentials
    print_status "Checking India region (ap-south-1) credentials..."
    if ! aws sts get-caller-identity --region ap-south-1 &> /dev/null; then
        print_error "AWS credentials for India region (ap-south-1) are not configured properly"
        exit 1
    fi
    
    print_success "AWS credentials validated"
}

# Function to initialize Terraform
init_terraform() {
    print_status "Initializing Terraform..."
    
    cd infrastructure/terraform
    
    # Initialize Terraform
    terraform init
    
    print_success "Terraform initialized"
}

# Function to validate Terraform configuration
validate_terraform() {
    print_status "Validating Terraform configuration..."
    
    # Validate configuration
    terraform validate
    
    # Format check
    terraform fmt -check=true -diff=true
    
    print_success "Terraform configuration is valid"
}

# Function to plan Terraform deployment
plan_terraform() {
    local environment=$1
    
    print_status "Creating Terraform plan for environment: $environment"
    
    # Create plan
    terraform plan \
        -var="environment=$environment" \
        -out="terraform-$environment.tfplan"
    
    print_success "Terraform plan created: terraform-$environment.tfplan"
}

# Function to apply Terraform deployment
apply_terraform() {
    local environment=$1
    
    print_status "Applying Terraform plan for environment: $environment"
    
    # Apply plan
    terraform apply "terraform-$environment.tfplan"
    
    print_success "Terraform deployment completed for environment: $environment"
}

# Function to show Terraform outputs
show_outputs() {
    print_status "Terraform outputs:"
    terraform output
}

# Function to destroy infrastructure
destroy_terraform() {
    local environment=$1
    
    print_warning "This will destroy ALL infrastructure for environment: $environment"
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [ "$confirm" = "yes" ]; then
        print_status "Destroying infrastructure for environment: $environment"
        terraform destroy -var="environment=$environment" -auto-approve
        print_success "Infrastructure destroyed for environment: $environment"
    else
        print_status "Destruction cancelled"
    fi
}

# Function to setup remote state backend
setup_backend() {
    print_status "Setting up Terraform remote state backend..."
    
    # Create S3 bucket for Terraform state
    aws s3 mb s3://valura-terraform-state-$(date +%s) --region me-central-1
    
    print_warning "Please update the backend configuration in main.tf with the created bucket name"
}

# Main function
main() {
    local command=$1
    local environment=$2
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "infrastructure/terraform" ]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
    
    case $command in
        "init")
            check_prerequisites
            validate_aws_credentials
            init_terraform
            ;;
        "plan")
            if [ -z "$environment" ]; then
                print_error "Environment is required for plan command"
                echo "Usage: $0 plan <environment>"
                exit 1
            fi
            check_prerequisites
            validate_aws_credentials
            init_terraform
            validate_terraform
            plan_terraform $environment
            ;;
        "apply")
            if [ -z "$environment" ]; then
                print_error "Environment is required for apply command"
                echo "Usage: $0 apply <environment>"
                exit 1
            fi
            check_prerequisites
            validate_aws_credentials
            init_terraform
            validate_terraform
            plan_terraform $environment
            apply_terraform $environment
            show_outputs
            ;;
        "destroy")
            if [ -z "$environment" ]; then
                print_error "Environment is required for destroy command"
                echo "Usage: $0 destroy <environment>"
                exit 1
            fi
            check_prerequisites
            validate_aws_credentials
            init_terraform
            destroy_terraform $environment
            ;;
        "output")
            show_outputs
            ;;
        "setup-backend")
            setup_backend
            ;;
        "validate")
            check_prerequisites
            validate_aws_credentials
            init_terraform
            validate_terraform
            ;;
        *)
            echo "Usage: $0 {init|plan|apply|destroy|output|setup-backend|validate} [environment]"
            echo ""
            echo "Commands:"
            echo "  init           - Initialize Terraform and check prerequisites"
            echo "  plan <env>     - Create deployment plan for environment"
            echo "  apply <env>    - Deploy infrastructure for environment"
            echo "  destroy <env>  - Destroy infrastructure for environment"
            echo "  output         - Show Terraform outputs"
            echo "  setup-backend  - Setup remote state backend"
            echo "  validate       - Validate configuration and credentials"
            echo ""
            echo "Environments: production, staging, development"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
