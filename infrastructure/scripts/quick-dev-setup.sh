#!/bin/bash

# Quick Dev Environment Setup for IAM Identity Center Users
# This script helps you get started quickly with temporary credentials

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
AWS_REGION="ap-south-1"
ENVIRONMENT="development"

print_header() {
    echo "=================================================="
    echo "  Valura AI - Quick Dev Environment Setup"
    echo "  For IAM Identity Center Users"
    echo "=================================================="
    echo
}

check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check required tools
    local tools=("aws" "gh" "terraform")
    for tool in "${tools[@]}"; do
        if ! command -v $tool &> /dev/null; then
            print_error "$tool is not installed"
            echo "Please install: brew install $tool"
            exit 1
        fi
    done
    
    # Check GitHub CLI auth
    if ! gh auth status &> /dev/null; then
        print_error "Please authenticate with GitHub CLI: gh auth login"
        exit 1
    fi
    
    print_success "Prerequisites OK"
}

setup_aws_credentials() {
    print_status "Setting up AWS credentials..."
    echo
    print_warning "You have two options for AWS authentication:"
    echo
    echo "1. OIDC (Recommended for production) - More secure, no credential management"
    echo "2. Temporary Access Keys (Quick start) - Faster setup, need periodic refresh"
    echo
    
    read -p "Choose option (1 for OIDC, 2 for temp keys): " auth_choice
    
    case $auth_choice in
        1)
            setup_oidc
            ;;
        2)
            setup_temp_keys
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
}

setup_oidc() {
    print_status "Setting up OIDC authentication..."
    echo
    print_warning "You need to set up OIDC in your AWS console first."
    echo "Please follow the guide: docs/GITHUB_OIDC_SETUP.md"
    echo
    
    read -p "Have you completed the OIDC setup in AWS? (y/N): " oidc_ready
    if [ "$oidc_ready" != "y" ] && [ "$oidc_ready" != "Y" ]; then
        print_status "Please complete OIDC setup first, then run this script again."
        exit 0
    fi
    
    read -p "Enter your AWS Account ID for ValuraDev: " account_id
    read -p "Enter the IAM role name (default: GitHubActionsRole-ValuraDev): " role_name
    role_name=${role_name:-GitHubActionsRole-ValuraDev}
    
    local role_arn="arn:aws:iam::$account_id:role/$role_name"
    
    # Set GitHub secrets for OIDC
    echo "$role_arn" | gh secret set AWS_ROLE_ARN_INDIA
    echo "$AWS_REGION" | gh secret set AWS_REGION_INDIA
    
    print_success "OIDC authentication configured"
    print_status "Role ARN: $role_arn"
}

setup_temp_keys() {
    print_status "Setting up temporary access keys..."
    echo
    print_warning "To get temporary credentials:"
    echo "1. Go to AWS Console via IAM Identity Center"
    echo "2. Click 'Command line or programmatic access'"
    echo "3. Copy the credentials from Option 1 (short-term)"
    echo
    
    read -p "Press Enter when you have the credentials ready..."
    
    read -p "Enter AWS_ACCESS_KEY_ID: " access_key
    read -s -p "Enter AWS_SECRET_ACCESS_KEY: " secret_key
    echo
    read -s -p "Enter AWS_SESSION_TOKEN: " session_token
    echo
    
    # Test credentials
    export AWS_ACCESS_KEY_ID="$access_key"
    export AWS_SECRET_ACCESS_KEY="$secret_key"
    export AWS_SESSION_TOKEN="$session_token"
    export AWS_DEFAULT_REGION="$AWS_REGION"
    
    if aws sts get-caller-identity &> /dev/null; then
        print_success "Credentials validated"
        
        # Set GitHub secrets
        echo "$access_key" | gh secret set AWS_ACCESS_KEY_ID_INDIA
        echo "$secret_key" | gh secret set AWS_SECRET_ACCESS_KEY_INDIA
        echo "$session_token" | gh secret set AWS_SESSION_TOKEN_INDIA
        echo "$AWS_REGION" | gh secret set AWS_REGION_INDIA
        
        print_success "Temporary credentials configured"
        print_warning "These credentials will expire. You'll need to refresh them periodically."
    else
        print_error "Credentials validation failed. Please check and try again."
        exit 1
    fi
}

create_basic_infrastructure() {
    print_status "Creating basic infrastructure..."
    
    # Create a minimal terraform.tfvars for dev
    cd infrastructure/terraform
    
    if [ ! -f "terraform.tfvars.dev" ]; then
        read -p "Enter your dev domain (e.g., dev.yourdomain.com): " domain_name
        read -s -p "Enter database password (min 8 chars): " db_password
        echo
        
        cat > terraform.tfvars.dev << EOF
# Development Environment - Quick Setup
domain_name = "$domain_name"
database_password = "$db_password"
EOF
        
        print_success "Created terraform.tfvars.dev"
    fi
    
    # Use the simplified dev-only configuration
    if [ -f "dev-only.tf" ]; then
        print_status "Using simplified dev-only infrastructure..."
        terraform init
        terraform plan -var-file="terraform.tfvars.dev" -target=aws_db_instance.dev -target=aws_ecr_repository.api_dev
        
        read -p "Deploy basic infrastructure (RDS + ECR)? (y/N): " deploy_confirm
        if [ "$deploy_confirm" = "y" ] || [ "$deploy_confirm" = "Y" ]; then
            terraform apply -var-file="terraform.tfvars.dev" -target=aws_db_instance.dev -target=aws_ecr_repository.api_dev -auto-approve
            print_success "Basic infrastructure deployed"
        fi
    else
        print_warning "dev-only.tf not found. Using full infrastructure configuration."
    fi
    
    cd ../..
}

setup_amplify_apps() {
    print_status "Setting up AWS Amplify applications..."
    
    # Check if we can access AWS
    if ! aws amplify list-apps --region $AWS_REGION &> /dev/null; then
        print_error "Cannot access AWS Amplify. Please check your credentials."
        return 1
    fi
    
    local apps=("web" "admin" "landing")
    local github_repo=$(gh repo view --json owner,name -q '.owner.login + "/" + .name')
    
    for app in "${apps[@]}"; do
        print_status "Creating Amplify app for $app..."
        
        local app_id=$(aws amplify create-app \
            --name "valura-$app-dev" \
            --description "Valura AI $app - Development" \
            --platform WEB \
            --region $AWS_REGION \
            --output text --query 'app.appId' 2>/dev/null || echo "")
        
        if [ -n "$app_id" ]; then
            print_success "Created Amplify app: $app_id"
            echo "$app_id" | gh secret set "AMPLIFY_APP_ID_${app^^}_DEV"
            
            # Create dev branch
            aws amplify create-branch \
                --app-id $app_id \
                --branch-name dev \
                --enable-auto-build \
                --region $AWS_REGION &> /dev/null || echo "Branch creation skipped"
        else
            print_warning "Failed to create Amplify app for $app"
        fi
    done
}

set_basic_secrets() {
    print_status "Setting basic GitHub secrets..."
    
    # Get account ID and ECR registry
    local account_id=$(aws sts get-caller-identity --query Account --output text 2>/dev/null || echo "")
    if [ -n "$account_id" ]; then
        echo "$account_id.dkr.ecr.$AWS_REGION.amazonaws.com" | gh secret set ECR_REGISTRY_INDIA
    fi
    
    # Set basic application secrets (you'll need to update these)
    echo "https://api-dev.yourdomain.com" | gh secret set NEXT_PUBLIC_API_URL_DEV
    
    print_success "Basic secrets configured"
    print_warning "You'll need to set these secrets manually:"
    echo "  - NEXT_PUBLIC_USER_POOL_ID"
    echo "  - NEXT_PUBLIC_USER_POOL_CLIENT_ID"
    echo "  - Database connection string (if not using Terraform output)"
}

test_setup() {
    print_status "Testing the setup..."
    
    # Test AWS access
    if aws sts get-caller-identity &> /dev/null; then
        print_success "AWS access working"
    else
        print_warning "AWS access issues detected"
    fi
    
    # Check GitHub secrets
    local secret_count=$(gh secret list | wc -l)
    print_status "GitHub secrets configured: $secret_count"
    
    print_success "Setup test completed"
}

show_next_steps() {
    print_success "Quick dev environment setup completed!"
    echo
    print_status "Next steps:"
    echo "1. Update your Next.js apps for static export (see docs/DEV_ENVIRONMENT_SETUP.md)"
    echo "2. Set remaining GitHub secrets manually"
    echo "3. Test the pipeline:"
    echo "   git checkout -b dev"
    echo "   git add ."
    echo "   git commit -m 'feat: initial dev setup'"
    echo "   git push origin dev"
    echo
    print_status "Monitor your setup:"
    echo "  - AWS Console: Check Amplify apps and RDS"
    echo "  - GitHub Actions: Watch the workflow run"
    echo "  - AWS Billing: Monitor costs"
    echo
    if [ "$auth_choice" = "2" ]; then
        print_warning "Remember: Your temporary credentials will expire!"
        echo "You'll need to refresh them when they expire."
    fi
}

main() {
    print_header
    
    print_warning "This script will create AWS resources that may incur charges."
    print_status "Estimated cost: $15-30/month for dev environment"
    echo
    
    read -p "Continue with quick setup? (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        print_status "Setup cancelled"
        exit 0
    fi
    
    check_prerequisites
    setup_aws_credentials
    create_basic_infrastructure
    setup_amplify_apps
    set_basic_secrets
    test_setup
    show_next_steps
}

main "$@"
