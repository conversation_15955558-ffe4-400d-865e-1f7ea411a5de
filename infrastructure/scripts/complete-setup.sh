#!/bin/bash

# Valura AI - Complete CI/CD Setup Script
# Comprehensive setup for IAM Identity Center users with dev/stg/prod strategy

set -e

# Colors and formatting
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m'

print_header() { echo -e "${BOLD}${BLUE}$1${NC}"; }
print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
PROJECT_NAME="valura-ai"
DEV_REGION="ap-south-1"
PROD_REGION="me-central-1"
ENVIRONMENT="development"

# Global variables
SETUP_MODE=""
AUTH_METHOD=""
AWS_ACCOUNT_ID=""
DOMAIN_NAME=""
DB_PASSWORD=""

show_banner() {
    echo "=================================================================="
    echo "           Valura AI - Complete CI/CD Setup"
    echo "=================================================================="
    echo "  Multi-region deployment: India (dev) → UAE (stg/prod)"
    echo "  Branch strategy: dev → stg → prod"
    echo "  IAM Identity Center compatible"
    echo "=================================================================="
    echo
}

check_prerequisites() {
    print_header "Checking Prerequisites"
    
    local tools=("aws" "gh" "terraform" "jq")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v $tool &> /dev/null; then
            missing_tools+=($tool)
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        echo "Install with: brew install ${missing_tools[*]}"
        exit 1
    fi
    
    # Check GitHub CLI authentication
    if ! gh auth status &> /dev/null; then
        print_error "Please authenticate with GitHub CLI first:"
        echo "  gh auth login"
        exit 1
    fi
    
    print_success "All prerequisites satisfied"
}

select_setup_mode() {
    print_header "Setup Configuration"
    
    echo "Choose your setup approach:"
    echo "1. Quick Start (Dev environment only, temporary credentials)"
    echo "2. Production Ready (Full OIDC setup, all environments)"
    echo "3. Custom (Choose specific components)"
    echo
    
    while true; do
        read -p "Select option (1-3): " choice
        case $choice in
            1) SETUP_MODE="quick"; break ;;
            2) SETUP_MODE="production"; break ;;
            3) SETUP_MODE="custom"; break ;;
            *) echo "Please enter 1, 2, or 3" ;;
        esac
    done
    
    print_status "Selected: $SETUP_MODE setup mode"
}

configure_authentication() {
    print_header "AWS Authentication Setup"
    
    if [ "$SETUP_MODE" = "quick" ]; then
        AUTH_METHOD="temp_keys"
        print_status "Using temporary access keys for quick setup"
    else
        echo "Choose authentication method:"
        echo "1. OIDC (Recommended for production)"
        echo "2. Temporary Access Keys (Easier setup)"
        echo
        
        while true; do
            read -p "Select option (1-2): " choice
            case $choice in
                1) AUTH_METHOD="oidc"; break ;;
                2) AUTH_METHOD="temp_keys"; break ;;
                *) echo "Please enter 1 or 2" ;;
            esac
        done
    fi
    
    case $AUTH_METHOD in
        "oidc")
            setup_oidc_authentication
            ;;
        "temp_keys")
            setup_temporary_credentials
            ;;
    esac
}

setup_oidc_authentication() {
    print_status "Setting up OIDC authentication..."
    
    echo
    print_warning "OIDC Setup Required in AWS Console:"
    echo "1. Go to IAM Console → Identity providers → Add provider"
    echo "2. Provider URL: https://token.actions.githubusercontent.com"
    echo "3. Audience: sts.amazonaws.com"
    echo "4. Create IAM role: GitHubActionsRole-ValuraDev"
    echo "5. Attach required policies (see documentation)"
    echo
    
    read -p "Have you completed the OIDC setup? (y/N): " oidc_ready
    if [ "$oidc_ready" != "y" ] && [ "$oidc_ready" != "Y" ]; then
        print_error "Please complete OIDC setup first. See docs/GITHUB_OIDC_SETUP.md"
        exit 1
    fi
    
    read -p "Enter your AWS Account ID: " AWS_ACCOUNT_ID
    read -p "Enter IAM role name [GitHubActionsRole-ValuraDev]: " role_name
    role_name=${role_name:-GitHubActionsRole-ValuraDev}
    
    local role_arn="arn:aws:iam::$AWS_ACCOUNT_ID:role/$role_name"
    
    # Set GitHub secrets for OIDC
    echo "$role_arn" | gh secret set AWS_ROLE_ARN_INDIA
    echo "$DEV_REGION" | gh secret set AWS_REGION_INDIA
    
    print_success "OIDC authentication configured"
}

setup_temporary_credentials() {
    print_status "Setting up temporary access keys..."
    
    echo
    print_warning "Get temporary credentials from AWS Console:"
    echo "1. Log into ValuraDev account via IAM Identity Center"
    echo "2. Go to 'Command line or programmatic access'"
    echo "3. Copy credentials from Option 1 (short-term)"
    echo
    
    read -p "Press Enter when ready..."
    
    read -p "AWS_ACCESS_KEY_ID: " access_key
    read -s -p "AWS_SECRET_ACCESS_KEY: " secret_key
    echo
    read -s -p "AWS_SESSION_TOKEN: " session_token
    echo
    
    # Test credentials
    export AWS_ACCESS_KEY_ID="$access_key"
    export AWS_SECRET_ACCESS_KEY="$secret_key"
    export AWS_SESSION_TOKEN="$session_token"
    export AWS_DEFAULT_REGION="$DEV_REGION"
    
    if aws sts get-caller-identity &> /dev/null; then
        AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        print_success "Credentials validated for account: $AWS_ACCOUNT_ID"
        
        # Set GitHub secrets
        echo "$access_key" | gh secret set AWS_ACCESS_KEY_ID_INDIA
        echo "$secret_key" | gh secret set AWS_SECRET_ACCESS_KEY_INDIA
        echo "$session_token" | gh secret set AWS_SESSION_TOKEN_INDIA
        echo "$DEV_REGION" | gh secret set AWS_REGION_INDIA
        
        print_success "GitHub secrets configured"
    else
        print_error "Credential validation failed"
        exit 1
    fi
}

collect_configuration() {
    print_header "Project Configuration"
    
    read -p "Enter your domain name (e.g., yourdomain.com): " DOMAIN_NAME
    read -s -p "Enter database password (min 8 characters): " DB_PASSWORD
    echo
    
    if [ ${#DB_PASSWORD} -lt 8 ]; then
        print_error "Database password must be at least 8 characters"
        exit 1
    fi
    
    print_success "Configuration collected"
}

setup_cost_protection() {
    print_header "Setting Up Cost Protection"
    
    # Create billing alerts
    local thresholds=(25 50 75 100)
    
    for threshold in "${thresholds[@]}"; do
        aws cloudwatch put-metric-alarm \
            --alarm-name "valura-dev-billing-$threshold" \
            --alarm-description "Billing alert for \$$threshold" \
            --metric-name EstimatedCharges \
            --namespace AWS/Billing \
            --statistic Maximum \
            --period 86400 \
            --threshold $threshold \
            --comparison-operator GreaterThanThreshold \
            --dimensions Name=Currency,Value=USD \
            --evaluation-periods 1 \
            --region us-east-1 2>/dev/null || print_warning "Failed to create billing alarm for \$$threshold"
    done
    
    print_success "Cost protection configured"
}

deploy_infrastructure() {
    print_header "Deploying Infrastructure"
    
    cd infrastructure/terraform
    
    # Create terraform.tfvars for dev environment
    cat > terraform.tfvars.dev << EOF
# Development Environment Configuration
environment = "development"
domain_name = "dev.$DOMAIN_NAME"
uae_region = "$PROD_REGION"
india_region = "$DEV_REGION"
database_password = "$DB_PASSWORD"

# Development-specific settings
database_instance_class = "db.t3.micro"
database_allocated_storage = 20
enable_multi_az = false
backup_retention_period = 1
enable_deletion_protection = false
app_runner_cpu = "0.25 vCPU"
app_runner_memory = "0.5 GB"
auto_scaling_max_size = 3
auto_scaling_min_size = 1
cloudfront_price_class = "PriceClass_100"
log_retention_days = 7
EOF
    
    print_status "Created terraform.tfvars.dev"
    
    # Initialize and plan
    terraform init
    
    if [ "$SETUP_MODE" = "quick" ]; then
        # Deploy minimal infrastructure for quick start
        print_status "Deploying minimal infrastructure..."
        terraform plan -var-file="terraform.tfvars.dev" -target=aws_db_instance.main -target=aws_ecr_repository.api
    else
        # Deploy full infrastructure
        print_status "Deploying full infrastructure..."
        terraform plan -var-file="terraform.tfvars.dev"
    fi
    
    read -p "Apply this infrastructure plan? (y/N): " apply_confirm
    if [ "$apply_confirm" = "y" ] || [ "$apply_confirm" = "Y" ]; then
        if [ "$SETUP_MODE" = "quick" ]; then
            terraform apply -var-file="terraform.tfvars.dev" -target=aws_db_instance.main -target=aws_ecr_repository.api -auto-approve
        else
            terraform apply -var-file="terraform.tfvars.dev" -auto-approve
        fi
        print_success "Infrastructure deployed"
    else
        print_warning "Infrastructure deployment skipped"
        cd ../..
        return 1
    fi
    
    # Extract outputs for GitHub secrets
    local db_url=$(terraform output -raw database_connection_string 2>/dev/null || echo "")
    local ecr_url=$(terraform output -raw ecr_repository_url 2>/dev/null || echo "")
    
    if [ -n "$db_url" ]; then
        echo "$db_url" | gh secret set DATABASE_URL_DEV
        print_success "Database URL configured in GitHub secrets"
    fi
    
    if [ -n "$ecr_url" ]; then
        local ecr_registry=$(echo "$ecr_url" | cut -d'/' -f1)
        echo "$ecr_registry" | gh secret set ECR_REGISTRY_INDIA
        print_success "ECR registry configured in GitHub secrets"
    fi
    
    cd ../..
}

setup_amplify_applications() {
    print_header "Setting Up AWS Amplify Applications"
    
    local apps=("web" "admin" "landing")
    local github_repo=$(gh repo view --json owner,name -q '.owner.login + "/" + .name')
    
    for app in "${apps[@]}"; do
        print_status "Creating Amplify app for $app..."
        
        local app_id=$(aws amplify create-app \
            --name "valura-$app-dev" \
            --description "Valura AI $app application - Development" \
            --platform WEB \
            --region $DEV_REGION \
            --output text --query 'app.appId' 2>/dev/null || echo "")
        
        if [ -n "$app_id" ]; then
            print_success "Created Amplify app: $app_id"
            echo "$app_id" | gh secret set "AMPLIFY_APP_ID_${app^^}_DEV"
            
            # Create dev branch
            aws amplify create-branch \
                --app-id $app_id \
                --branch-name dev \
                --enable-auto-build \
                --region $DEV_REGION &> /dev/null || echo "Branch creation skipped"
        else
            print_warning "Failed to create Amplify app for $app"
        fi
    done
}

configure_application_secrets() {
    print_header "Configuring Application Secrets"
    
    # Set basic application secrets
    echo "https://api-dev.$DOMAIN_NAME" | gh secret set NEXT_PUBLIC_API_URL_DEV
    
    print_success "Basic application secrets configured"
    
    print_warning "You'll need to set these secrets manually:"
    echo "  - NEXT_PUBLIC_USER_POOL_ID"
    echo "  - NEXT_PUBLIC_USER_POOL_CLIENT_ID"
    echo "  - NEXT_PUBLIC_PERSONA_TEMPLATE_ID"
    echo "  - NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID"
    echo "  - NEXT_PUBLIC_POSTHOG_KEY"
    echo "  - NEXT_PUBLIC_POSTHOG_HOST"
    echo "  - RESEND_API_KEY"
    echo "  - NEXT_PUBLIC_SUPABASE_URL"
    echo "  - NEXT_PUBLIC_SUPABASE_ANON_KEY"
}

test_deployment() {
    print_header "Testing Deployment"
    
    print_status "Creating test deployment..."
    
    # Check if we're already on dev branch
    current_branch=$(git branch --show-current 2>/dev/null || echo "")
    
    if [ "$current_branch" != "dev" ]; then
        if git show-ref --verify --quiet refs/heads/dev; then
            git checkout dev
        else
            git checkout -b dev
        fi
    fi
    
    # Make a test commit
    echo "# Development Environment - $(date)" >> README_DEV.md
    git add README_DEV.md
    git commit -m "feat: test development environment deployment" || echo "No changes to commit"
    
    print_status "Pushing to dev branch to trigger CI/CD..."
    git push origin dev
    
    print_success "Test deployment initiated"
    print_status "Monitor progress at: https://github.com/$(gh repo view --json owner,name -q '.owner.login + "/" + .name')/actions"
}

show_completion_summary() {
    print_header "Setup Complete!"
    
    echo
    print_success "Development environment successfully configured"
    echo
    print_status "What was set up:"
    echo "  ✅ AWS authentication ($AUTH_METHOD)"
    echo "  ✅ Infrastructure deployment"
    echo "  ✅ AWS Amplify applications"
    echo "  ✅ GitHub secrets configuration"
    echo "  ✅ Cost protection alerts"
    echo "  ✅ Test deployment initiated"
    echo
    print_status "Next steps:"
    echo "  1. Monitor GitHub Actions workflow"
    echo "  2. Check AWS Amplify deployments"
    echo "  3. Set remaining application secrets"
    echo "  4. Configure custom domain (optional)"
    echo "  5. Set up staging/production environments"
    echo
    print_status "Useful links:"
    echo "  - GitHub Actions: https://github.com/$(gh repo view --json owner,name -q '.owner.login + "/" + .name')/actions"
    echo "  - AWS Console: https://console.aws.amazon.com/"
    echo "  - Documentation: docs/COMPLETE_IMPLEMENTATION_GUIDE.md"
    echo
    if [ "$AUTH_METHOD" = "temp_keys" ]; then
        print_warning "Remember: Your temporary credentials will expire!"
        echo "  You'll need to refresh them periodically."
    fi
    
    print_status "Estimated monthly cost: \$27-54 for development environment"
}

main() {
    show_banner
    
    print_warning "This script will create AWS resources that incur charges."
    print_status "Estimated cost: \$27-54/month for development environment"
    echo
    
    read -p "Continue with complete setup? (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        print_status "Setup cancelled"
        exit 0
    fi
    
    check_prerequisites
    select_setup_mode
    configure_authentication
    collect_configuration
    setup_cost_protection
    deploy_infrastructure
    setup_amplify_applications
    configure_application_secrets
    test_deployment
    show_completion_summary
}

main "$@"
