#!/bin/bash

# GitHub Secrets Setup Script for Valura AI
# This script helps you set up GitHub secrets using GitHub CLI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if GitHub CLI is installed
check_gh_cli() {
    if ! command -v gh &> /dev/null; then
        print_error "GitHub CLI is not installed. Please install it first:"
        echo "  brew install gh"
        echo "  or visit: https://cli.github.com/"
        exit 1
    fi
    
    # Check if user is authenticated
    if ! gh auth status &> /dev/null; then
        print_error "Please authenticate with GitHub CLI first:"
        echo "  gh auth login"
        exit 1
    fi
    
    print_success "GitHub CLI is ready"
}

# Function to set a secret
set_secret() {
    local secret_name=$1
    local secret_description=$2
    local is_sensitive=${3:-false}
    
    if [ "$is_sensitive" = true ]; then
        echo -n "Enter $secret_description: "
        read -s secret_value
        echo
    else
        echo -n "Enter $secret_description: "
        read secret_value
    fi
    
    if [ -n "$secret_value" ]; then
        echo "$secret_value" | gh secret set "$secret_name"
        print_success "Set secret: $secret_name"
    else
        print_warning "Skipped empty secret: $secret_name"
    fi
}

# Function to set secrets from Terraform output
set_terraform_secrets() {
    print_status "Setting secrets from Terraform output..."
    
    if [ ! -f "infrastructure/terraform/terraform.tfstate" ]; then
        print_warning "Terraform state file not found. Please run terraform apply first."
        return
    fi
    
    cd infrastructure/terraform
    
    # Extract values from Terraform output
    if terraform output &> /dev/null; then
        print_status "Extracting values from Terraform output..."
        
        # Get S3 bucket names
        WEB_BUCKET=$(terraform output -json s3_buckets | jq -r '.web.bucket_name' 2>/dev/null || echo "")
        ADMIN_BUCKET=$(terraform output -json s3_buckets | jq -r '.admin.bucket_name' 2>/dev/null || echo "")
        LANDING_BUCKET=$(terraform output -json s3_buckets | jq -r '.landing.bucket_name' 2>/dev/null || echo "")
        
        # Get CloudFront distribution IDs
        WEB_CF_ID=$(terraform output -json cloudfront_distributions | jq -r '.web.distribution_id' 2>/dev/null || echo "")
        ADMIN_CF_ID=$(terraform output -json cloudfront_distributions | jq -r '.admin.distribution_id' 2>/dev/null || echo "")
        LANDING_CF_ID=$(terraform output -json cloudfront_distributions | jq -r '.landing.distribution_id' 2>/dev/null || echo "")
        
        # Get ECR repository URL
        ECR_URL=$(terraform output -raw ecr_repository_url 2>/dev/null || echo "")
        
        # Set secrets if values exist
        [ -n "$WEB_BUCKET" ] && echo "$WEB_BUCKET" | gh secret set S3_BUCKET_WEB_PROD
        [ -n "$ADMIN_BUCKET" ] && echo "$ADMIN_BUCKET" | gh secret set S3_BUCKET_ADMIN_PROD
        [ -n "$LANDING_BUCKET" ] && echo "$LANDING_BUCKET" | gh secret set S3_BUCKET_LANDING_PROD
        [ -n "$WEB_CF_ID" ] && echo "$WEB_CF_ID" | gh secret set CLOUDFRONT_DISTRIBUTION_ID_WEB
        [ -n "$ADMIN_CF_ID" ] && echo "$ADMIN_CF_ID" | gh secret set CLOUDFRONT_DISTRIBUTION_ID_ADMIN
        [ -n "$LANDING_CF_ID" ] && echo "$LANDING_CF_ID" | gh secret set CLOUDFRONT_DISTRIBUTION_ID_LANDING
        
        if [ -n "$ECR_URL" ]; then
            ECR_REGISTRY=$(echo "$ECR_URL" | cut -d'/' -f1)
            echo "$ECR_REGISTRY" | gh secret set ECR_REGISTRY_UAE
        fi
        
        print_success "Terraform secrets configured"
    else
        print_warning "Could not read Terraform output"
    fi
    
    cd ../..
}

# Main setup function
main() {
    print_status "Setting up GitHub secrets for Valura AI"
    
    check_gh_cli
    
    echo
    print_status "This script will help you set up GitHub secrets for CI/CD"
    print_warning "Make sure you have the following information ready:"
    echo "  - AWS access keys for UAE and India regions"
    echo "  - Database passwords"
    echo "  - API keys for external services"
    echo "  - Expo token"
    echo
    
    read -p "Continue? (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        print_status "Setup cancelled"
        exit 0
    fi
    
    echo
    print_status "Setting up AWS credentials..."
    
    # AWS Credentials
    set_secret "AWS_ACCESS_KEY_ID_UAE" "AWS Access Key ID for UAE region (me-central-1)" true
    set_secret "AWS_SECRET_ACCESS_KEY_UAE" "AWS Secret Access Key for UAE region" true
    set_secret "AWS_ACCESS_KEY_ID_INDIA" "AWS Access Key ID for India region (ap-south-1)" true
    set_secret "AWS_SECRET_ACCESS_KEY_INDIA" "AWS Secret Access Key for India region" true
    
    echo
    print_status "Setting up application environment variables..."
    
    # Application Environment Variables
    set_secret "NEXT_PUBLIC_API_URL" "Production API URL (e.g., https://api.yourdomain.com)"
    set_secret "NEXT_PUBLIC_USER_POOL_ID" "AWS Cognito User Pool ID"
    set_secret "NEXT_PUBLIC_USER_POOL_CLIENT_ID" "AWS Cognito User Pool Client ID"
    set_secret "NEXT_PUBLIC_PERSONA_TEMPLATE_ID" "Persona Template ID"
    set_secret "NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID" "Persona Environment ID"
    set_secret "NEXT_PUBLIC_POSTHOG_KEY" "PostHog API Key"
    set_secret "NEXT_PUBLIC_POSTHOG_HOST" "PostHog Host (default: https://eu.i.posthog.com)"
    
    echo
    print_status "Setting up database URLs..."
    
    # Database URLs
    set_secret "DATABASE_URL_DEV" "Development Database URL" true
    set_secret "DATABASE_URL_STAGING" "Staging Database URL" true
    set_secret "DATABASE_URL_PROD" "Production Database URL" true
    
    echo
    print_status "Setting up external service keys..."
    
    # External Services
    set_secret "EXPO_TOKEN" "Expo/EAS Token" true
    set_secret "RESEND_API_KEY" "Resend API Key" true
    set_secret "NEXT_PUBLIC_SUPABASE_URL" "Supabase URL"
    set_secret "NEXT_PUBLIC_SUPABASE_ANON_KEY" "Supabase Anonymous Key"
    
    echo
    print_status "Setting up AWS Amplify App IDs (for development)..."
    
    # AWS Amplify App IDs
    set_secret "AMPLIFY_APP_ID_WEB_DEV" "Amplify App ID for Web App (Development)"
    set_secret "AMPLIFY_APP_ID_ADMIN_DEV" "Amplify App ID for Admin Panel (Development)"
    set_secret "AMPLIFY_APP_ID_LANDING_DEV" "Amplify App ID for Landing Page (Development)"
    
    echo
    print_status "Setting up additional configuration..."
    
    # Additional Configuration
    set_secret "ADMIN_PORT" "Admin Panel Port (default: 3002)"
    
    # Set region constants
    echo "me-central-1" | gh secret set AWS_REGION_UAE
    echo "ap-south-1" | gh secret set AWS_REGION_INDIA
    
    echo
    print_status "Attempting to set secrets from Terraform output..."
    set_terraform_secrets
    
    echo
    print_success "GitHub secrets setup completed!"
    print_status "Next steps:"
    echo "  1. Verify all secrets are set correctly in GitHub repository settings"
    echo "  2. Set up GitHub Environments (development, staging, production)"
    echo "  3. Configure environment-specific secrets if needed"
    echo "  4. Test the CI/CD pipeline with a small change"
    
    echo
    print_status "To view all secrets:"
    echo "  gh secret list"
}

# Run main function
main "$@"
