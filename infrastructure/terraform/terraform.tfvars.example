# Terraform Variables Example
# Copy this file to terraform.tfvars and update with your values

# Environment Configuration
environment = "production"  # or "staging", "development"

# Domain Configuration
domain_name = "valura.ae"  # Replace with your actual domain

# AWS Regions
uae_region   = "me-central-1"  # UAE
india_region = "ap-south-1"   # Mumbai

# Network Configuration
vpc_cidr = "10.0.0.0/16"

# Database Configuration
database_password = "your-secure-database-password-here"  # Use a strong password
database_username = "valura_admin"

# Environment-specific overrides (optional)
# These will override the defaults in variables.tf for specific environments

# Production overrides
# database_instance_class = "db.t3.medium"
# database_allocated_storage = 100
# enable_multi_az = true
# backup_retention_period = 30
# enable_deletion_protection = true
# app_runner_cpu = "1 vCPU"
# app_runner_memory = "2 GB"
# auto_scaling_max_size = 10
# auto_scaling_min_size = 2
# cloudfront_price_class = "PriceClass_All"

# Staging overrides
# database_instance_class = "db.t3.small"
# database_allocated_storage = 50
# enable_multi_az = false
# backup_retention_period = 7
# enable_deletion_protection = false
# app_runner_cpu = "0.5 vCPU"
# app_runner_memory = "1 GB"
# auto_scaling_max_size = 5
# auto_scaling_min_size = 1
# cloudfront_price_class = "PriceClass_100"

# Development overrides
# database_instance_class = "db.t3.micro"
# database_allocated_storage = 20
# enable_multi_az = false
# backup_retention_period = 1
# enable_deletion_protection = false
# app_runner_cpu = "0.25 vCPU"
# app_runner_memory = "0.5 GB"
# auto_scaling_max_size = 3
# auto_scaling_min_size = 1
# cloudfront_price_class = "PriceClass_100"
