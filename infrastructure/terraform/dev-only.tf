# Development Environment Only - Simplified Configuration
# Use this file for initial dev environment setup

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Single provider for dev environment in India
provider "aws" {
  region = "ap-south-1"
  
  default_tags {
    tags = {
      Project     = "Valura AI"
      Environment = "development"
      ManagedBy   = "Terraform"
      Account     = "ValuraDev"
    }
  }
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_availability_zones" "available" {
  state = "available"
}

# Local values
locals {
  account_id = data.aws_caller_identity.current.account_id
  environment = "development"
  
  common_tags = {
    Project     = "Valura AI"
    Environment = "development"
    ManagedBy   = "Terraform"
    Account     = "ValuraDev"
  }
}

# Variables for dev environment
variable "domain_name" {
  description = "Domain name for dev environment (e.g., dev.yourdomain.com)"
  type        = string
  default     = "dev.valura.local"  # Change this to your actual dev domain
}

variable "database_password" {
  description = "Password for RDS PostgreSQL database"
  type        = string
  sensitive   = true
}

# VPC for development
resource "aws_vpc" "dev" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-vpc"
  })
}

# Internet Gateway
resource "aws_internet_gateway" "dev" {
  vpc_id = aws_vpc.dev.id
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-igw"
  })
}

# Public Subnets (for RDS and App Runner)
resource "aws_subnet" "public" {
  count                   = 2
  vpc_id                  = aws_vpc.dev.id
  cidr_block              = cidrsubnet("10.0.0.0/16", 8, count.index)
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-public-subnet-${count.index + 1}"
  })
}

# Route Table for Public Subnets
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.dev.id
  
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.dev.id
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-public-rt"
  })
}

# Route Table Associations
resource "aws_route_table_association" "public" {
  count          = 2
  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}

# Security Group for RDS
resource "aws_security_group" "rds" {
  name        = "valura-dev-rds-sg"
  description = "Security group for RDS PostgreSQL"
  vpc_id      = aws_vpc.dev.id
  
  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.app_runner.id]
  }
  
  # Allow connections from your IP for development (optional)
  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # Restrict this to your IP for security
    description = "Development access"
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-rds-sg"
  })
}

# Security Group for App Runner
resource "aws_security_group" "app_runner" {
  name        = "valura-dev-apprunner-sg"
  description = "Security group for App Runner service"
  vpc_id      = aws_vpc.dev.id
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-apprunner-sg"
  })
}

# RDS Subnet Group
resource "aws_db_subnet_group" "dev" {
  name       = "valura-dev-db-subnet-group"
  subnet_ids = aws_subnet.public[*].id
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-db-subnet-group"
  })
}

# RDS PostgreSQL Instance (Development)
resource "aws_db_instance" "dev" {
  identifier = "valura-dev-db"
  
  # Engine configuration
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.micro"  # Free tier eligible
  
  # Storage configuration
  allocated_storage     = 20
  max_allocated_storage = 100
  storage_type          = "gp2"
  storage_encrypted     = true
  
  # Database configuration
  db_name  = "valura_dev_db"
  username = "valura_admin"
  password = var.database_password
  
  # Network configuration
  db_subnet_group_name   = aws_db_subnet_group.dev.name
  vpc_security_group_ids = [aws_security_group.rds.id]
  publicly_accessible    = true  # For development access
  
  # Backup configuration (minimal for dev)
  backup_retention_period = 1
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  # Development settings
  multi_az               = false
  deletion_protection    = false
  skip_final_snapshot    = true
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-db"
  })
}

# ECR Repository for API
resource "aws_ecr_repository" "api_dev" {
  name                 = "valura-api-dev"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-api-dev-ecr"
  })
}

# ECR Lifecycle Policy
resource "aws_ecr_lifecycle_policy" "api_dev" {
  repository = aws_ecr_repository.api_dev.name
  
  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 5 images for dev"
        selection = {
          tagStatus     = "any"
          countType     = "imageCountMoreThan"
          countNumber   = 5
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# App Runner VPC Connector
resource "aws_apprunner_vpc_connector" "dev" {
  vpc_connector_name = "valura-dev-vpc-connector"
  subnets           = aws_subnet.public[*].id
  security_groups   = [aws_security_group.app_runner.id]
  
  tags = merge(local.common_tags, {
    Name = "valura-dev-vpc-connector"
  })
}

# Outputs
output "database_endpoint" {
  description = "RDS PostgreSQL endpoint"
  value       = aws_db_instance.dev.endpoint
  sensitive   = true
}

output "database_connection_string" {
  description = "Database connection string for applications"
  value       = "postgresql://${aws_db_instance.dev.username}:${var.database_password}@${aws_db_instance.dev.endpoint}:${aws_db_instance.dev.port}/${aws_db_instance.dev.db_name}"
  sensitive   = true
}

output "ecr_repository_url" {
  description = "ECR repository URL for API"
  value       = aws_ecr_repository.api_dev.repository_url
}

output "vpc_connector_arn" {
  description = "App Runner VPC connector ARN"
  value       = aws_apprunner_vpc_connector.dev.arn
}

output "aws_region" {
  description = "AWS region"
  value       = "ap-south-1"
}

output "environment" {
  description = "Environment name"
  value       = "development"
}

# Instructions for next steps
output "next_steps" {
  description = "Next steps after infrastructure deployment"
  value = [
    "1. Create AWS Amplify apps for web, admin, and landing applications",
    "2. Configure GitHub secrets with the database connection string",
    "3. Set up App Runner service using the ECR repository",
    "4. Test the CI/CD pipeline by pushing to dev branch",
    "5. Monitor AWS costs and adjust resources as needed"
  ]
}
