# Outputs for Valura AI Infrastructure

# Domain and DNS
output "domain_name" {
  description = "Main domain name"
  value       = var.domain_name
}

output "route53_zone_id" {
  description = "Route53 hosted zone ID"
  value       = aws_route53_zone.main.zone_id
}

output "route53_name_servers" {
  description = "Route53 name servers"
  value       = aws_route53_zone.main.name_servers
}

# SSL Certificate
output "certificate_arn" {
  description = "ACM certificate ARN"
  value       = aws_acm_certificate_validation.main.certificate_arn
}

# S3 Buckets
output "s3_buckets" {
  description = "S3 bucket names for applications"
  value = {
    for app_name, app_config in local.apps : app_name => {
      bucket_name = aws_s3_bucket.apps[app_name].id
      bucket_arn  = aws_s3_bucket.apps[app_name].arn
    }
  }
}

# CloudFront Distributions
output "cloudfront_distributions" {
  description = "CloudFront distribution information"
  value = {
    for app_name, app_config in local.apps : app_name => {
      distribution_id   = aws_cloudfront_distribution.apps[app_name].id
      distribution_arn  = aws_cloudfront_distribution.apps[app_name].arn
      domain_name       = aws_cloudfront_distribution.apps[app_name].domain_name
      hosted_zone_id    = aws_cloudfront_distribution.apps[app_name].hosted_zone_id
      url              = app_config.subdomain == "" ? "https://${var.domain_name}" : "https://${app_config.subdomain}.${var.domain_name}"
    }
  }
}

# VPC Information
output "vpc_id" {
  description = "VPC ID"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "VPC CIDR block"
  value       = aws_vpc.main.cidr_block
}

output "public_subnet_ids" {
  description = "Public subnet IDs"
  value       = aws_subnet.public[*].id
}

output "private_subnet_ids" {
  description = "Private subnet IDs"
  value       = aws_subnet.private[*].id
}

# Database Information
output "database_endpoint" {
  description = "RDS PostgreSQL endpoint"
  value       = aws_db_instance.main.endpoint
  sensitive   = true
}

output "database_port" {
  description = "RDS PostgreSQL port"
  value       = aws_db_instance.main.port
}

output "database_name" {
  description = "Database name"
  value       = aws_db_instance.main.db_name
}

output "database_username" {
  description = "Database username"
  value       = aws_db_instance.main.username
  sensitive   = true
}

# ECR Repository
output "ecr_repository_url" {
  description = "ECR repository URL"
  value       = aws_ecr_repository.api.repository_url
}

output "ecr_repository_arn" {
  description = "ECR repository ARN"
  value       = aws_ecr_repository.api.arn
}

# App Runner
output "vpc_connector_arn" {
  description = "App Runner VPC connector ARN"
  value       = aws_apprunner_vpc_connector.main.arn
}

output "auto_scaling_configuration_arn" {
  description = "App Runner auto scaling configuration ARN"
  value       = aws_apprunner_auto_scaling_configuration_version.main.arn
}

# Security Groups
output "database_security_group_id" {
  description = "Database security group ID"
  value       = aws_security_group.database.id
}

output "app_runner_security_group_id" {
  description = "App Runner security group ID"
  value       = aws_security_group.app_runner.id
}

# Environment Configuration
output "environment" {
  description = "Current environment"
  value       = var.environment
}

output "aws_regions" {
  description = "AWS regions being used"
  value = {
    uae   = var.uae_region
    india = var.india_region
  }
}

# GitHub Actions Secrets (for easy copy-paste)
output "github_secrets" {
  description = "GitHub Actions secrets to configure"
  value = {
    # AWS Credentials
    AWS_ACCESS_KEY_ID_UAE     = "Configure with UAE region IAM user access key"
    AWS_SECRET_ACCESS_KEY_UAE = "Configure with UAE region IAM user secret key"
    AWS_ACCESS_KEY_ID_INDIA   = "Configure with India region IAM user access key"
    AWS_SECRET_ACCESS_KEY_INDIA = "Configure with India region IAM user secret key"
    AWS_REGION_UAE            = var.uae_region
    AWS_REGION_INDIA          = var.india_region
    
    # S3 Buckets
    S3_BUCKET_WEB_PROD        = aws_s3_bucket.apps["web"].id
    S3_BUCKET_ADMIN_PROD      = aws_s3_bucket.apps["admin"].id
    S3_BUCKET_LANDING_PROD    = aws_s3_bucket.apps["landing"].id
    
    # CloudFront Distribution IDs
    CLOUDFRONT_DISTRIBUTION_ID_WEB     = aws_cloudfront_distribution.apps["web"].id
    CLOUDFRONT_DISTRIBUTION_ID_ADMIN   = aws_cloudfront_distribution.apps["admin"].id
    CLOUDFRONT_DISTRIBUTION_ID_LANDING = aws_cloudfront_distribution.apps["landing"].id
    
    # ECR
    ECR_REGISTRY_UAE = "${local.account_id}.dkr.ecr.${var.uae_region}.amazonaws.com"
    ECR_REGISTRY_INDIA = "${local.account_id}.dkr.ecr.${var.india_region}.amazonaws.com"
    
    # Database
    DATABASE_URL_PROD = "postgresql://${aws_db_instance.main.username}:${var.database_password}@${aws_db_instance.main.endpoint}/${aws_db_instance.main.db_name}"
    
    # App Runner
    VPC_CONNECTOR_ARN = aws_apprunner_vpc_connector.main.arn
    AUTO_SCALING_CONFIG_ARN = aws_apprunner_auto_scaling_configuration_version.main.arn
  }
  sensitive = true
}

# Cost Estimation
output "estimated_monthly_costs" {
  description = "Estimated monthly costs (USD)"
  value = {
    cloudfront = "~$1-5 per distribution"
    s3_storage = "~$1-3 per bucket"
    rds = var.environment == "production" ? "~$25-50" : "~$10-20"
    app_runner = var.environment == "production" ? "~$30-100" : "~$10-30"
    nat_gateway = "~$45 (2 NAT gateways)"
    route53 = "~$0.50 per hosted zone"
    data_transfer = "~$5-20 depending on usage"
    total_estimate = var.environment == "production" ? "~$100-250/month" : "~$50-100/month"
  }
}

# Deployment URLs
output "application_urls" {
  description = "Application URLs after deployment"
  value = {
    landing_page = "https://${var.domain_name}"
    web_app      = "https://app.${var.domain_name}"
    admin_panel  = "https://admin.${var.domain_name}"
    api_endpoint = "Will be available after App Runner service deployment"
  }
}

# Next Steps
output "next_steps" {
  description = "Next steps after infrastructure deployment"
  value = [
    "1. Configure GitHub Actions secrets using the 'github_secrets' output",
    "2. Update DNS nameservers to point to Route53 nameservers",
    "3. Deploy App Runner service using the CI/CD pipeline",
    "4. Configure application environment variables",
    "5. Test all applications and verify SSL certificates"
  ]
}
