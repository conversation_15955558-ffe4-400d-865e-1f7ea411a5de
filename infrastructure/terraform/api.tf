# API Infrastructure - VPC, <PERSON><PERSON>, <PERSON><PERSON>, ECR

# VPC Configuration
resource "aws_vpc" "main" {
  provider             = aws.uae
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = merge(local.common_tags, {
    Name = "valura-vpc-${var.environment}"
  })
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  provider = aws.uae
  vpc_id   = aws_vpc.main.id
  
  tags = merge(local.common_tags, {
    Name = "valura-igw-${var.environment}"
  })
}

# Public Subnets
resource "aws_subnet" "public" {
  provider                = aws.uae
  count                   = 2
  vpc_id                  = aws_vpc.main.id
  cidr_block              = cidrsubnet(var.vpc_cidr, 8, count.index)
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true
  
  tags = merge(local.common_tags, {
    Name = "valura-public-subnet-${count.index + 1}-${var.environment}"
    Type = "Public"
  })
}

# Private Subnets
resource "aws_subnet" "private" {
  provider          = aws.uae
  count             = 2
  vpc_id            = aws_vpc.main.id
  cidr_block        = cidrsubnet(var.vpc_cidr, 8, count.index + 2)
  availability_zone = data.aws_availability_zones.available.names[count.index]
  
  tags = merge(local.common_tags, {
    Name = "valura-private-subnet-${count.index + 1}-${var.environment}"
    Type = "Private"
  })
}

# Elastic IPs for NAT Gateways
resource "aws_eip" "nat" {
  provider = aws.uae
  count    = 2
  domain   = "vpc"
  
  depends_on = [aws_internet_gateway.main]
  
  tags = merge(local.common_tags, {
    Name = "valura-nat-eip-${count.index + 1}-${var.environment}"
  })
}

# NAT Gateways
resource "aws_nat_gateway" "main" {
  provider      = aws.uae
  count         = 2
  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = aws_subnet.public[count.index].id
  
  tags = merge(local.common_tags, {
    Name = "valura-nat-${count.index + 1}-${var.environment}"
  })
  
  depends_on = [aws_internet_gateway.main]
}

# Route Tables
resource "aws_route_table" "public" {
  provider = aws.uae
  vpc_id   = aws_vpc.main.id
  
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main.id
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-public-rt-${var.environment}"
  })
}

resource "aws_route_table" "private" {
  provider = aws.uae
  count    = 2
  vpc_id   = aws_vpc.main.id
  
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.main[count.index].id
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-private-rt-${count.index + 1}-${var.environment}"
  })
}

# Route Table Associations
resource "aws_route_table_association" "public" {
  provider       = aws.uae
  count          = 2
  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}

resource "aws_route_table_association" "private" {
  provider       = aws.uae
  count          = 2
  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index].id
}

# Security Groups
resource "aws_security_group" "database" {
  provider    = aws.uae
  name        = "valura-db-sg-${var.environment}"
  description = "Security group for RDS PostgreSQL database"
  vpc_id      = aws_vpc.main.id
  
  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.app_runner.id]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-db-sg-${var.environment}"
  })
}

resource "aws_security_group" "app_runner" {
  provider    = aws.uae
  name        = "valura-apprunner-sg-${var.environment}"
  description = "Security group for App Runner service"
  vpc_id      = aws_vpc.main.id
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-apprunner-sg-${var.environment}"
  })
}

# RDS Subnet Group
resource "aws_db_subnet_group" "main" {
  provider   = aws.uae
  name       = "valura-db-subnet-group-${var.environment}"
  subnet_ids = aws_subnet.private[*].id
  
  tags = merge(local.common_tags, {
    Name = "valura-db-subnet-group-${var.environment}"
  })
}

# RDS PostgreSQL Database
resource "aws_db_instance" "main" {
  provider = aws.uae
  
  identifier = "valura-db-${var.environment}"
  
  # Engine configuration
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = local.config.database_instance_class
  
  # Storage configuration
  allocated_storage     = local.config.database_allocated_storage
  max_allocated_storage = local.config.database_allocated_storage * 2
  storage_type          = "gp2"
  storage_encrypted     = true
  
  # Database configuration
  db_name  = "valura_db"
  username = var.database_username
  password = var.database_password
  
  # Network configuration
  db_subnet_group_name   = aws_db_subnet_group.main.name
  vpc_security_group_ids = [aws_security_group.database.id]
  publicly_accessible    = false
  
  # Backup configuration
  backup_retention_period = local.config.backup_retention_period
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  # High availability
  multi_az = local.config.enable_multi_az
  
  # Monitoring
  monitoring_interval = 60
  monitoring_role_arn = aws_iam_role.rds_monitoring.arn
  
  # Security
  deletion_protection = local.config.enable_deletion_protection
  
  # Performance Insights
  performance_insights_enabled = true
  performance_insights_retention_period = 7
  
  tags = merge(local.common_tags, {
    Name = "valura-db-${var.environment}"
  })
}

# IAM Role for RDS Monitoring
resource "aws_iam_role" "rds_monitoring" {
  provider = aws.uae
  name     = "valura-rds-monitoring-role-${var.environment}"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })
  
  tags = local.common_tags
}

resource "aws_iam_role_policy_attachment" "rds_monitoring" {
  provider   = aws.uae
  role       = aws_iam_role.rds_monitoring.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

# ECR Repository
resource "aws_ecr_repository" "api" {
  provider = aws.uae
  name     = "valura-api-${var.environment}"
  
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = true
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-api-ecr-${var.environment}"
  })
}

# ECR Lifecycle Policy
resource "aws_ecr_lifecycle_policy" "api" {
  provider   = aws.uae
  repository = aws_ecr_repository.api.name
  
  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 10 images"
        selection = {
          tagStatus     = "any"
          countType     = "imageCountMoreThan"
          countNumber   = 10
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# App Runner VPC Connector
resource "aws_apprunner_vpc_connector" "main" {
  provider               = aws.uae
  vpc_connector_name     = "valura-vpc-connector-${var.environment}"
  subnets                = aws_subnet.private[*].id
  security_groups        = [aws_security_group.app_runner.id]
  
  tags = merge(local.common_tags, {
    Name = "valura-vpc-connector-${var.environment}"
  })
}

# App Runner Auto Scaling Configuration
resource "aws_apprunner_auto_scaling_configuration_version" "main" {
  provider                       = aws.uae
  auto_scaling_configuration_name = "valura-autoscaling-${var.environment}"
  max_concurrency                = local.config.auto_scaling_max_concurrency
  max_size                       = local.config.auto_scaling_max_size
  min_size                       = local.config.auto_scaling_min_size
  
  tags = merge(local.common_tags, {
    Name = "valura-autoscaling-${var.environment}"
  })
}
