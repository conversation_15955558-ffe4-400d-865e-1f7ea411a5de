# Variables for Valura AI Infrastructure

variable "environment" {
  description = "Environment name (production, staging, development)"
  type        = string
  default     = "production"
  
  validation {
    condition     = contains(["production", "staging", "development"], var.environment)
    error_message = "Environment must be one of: production, staging, development."
  }
}

variable "domain_name" {
  description = "Domain name for the application (e.g., valura.ae)"
  type        = string
}

variable "uae_region" {
  description = "AWS region for UAE deployment"
  type        = string
  default     = "me-central-1"
}

variable "india_region" {
  description = "AWS region for India deployment"
  type        = string
  default     = "ap-south-1"
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "database_password" {
  description = "Password for RDS PostgreSQL database"
  type        = string
  sensitive   = true
}

variable "database_username" {
  description = "Username for RDS PostgreSQL database"
  type        = string
  default     = "valura_admin"
}

variable "app_runner_cpu" {
  description = "CPU configuration for App Runner"
  type        = string
  default     = "1 vCPU"
  
  validation {
    condition     = contains(["0.25 vCPU", "0.5 vCPU", "1 vCPU", "2 vCPU"], var.app_runner_cpu)
    error_message = "CPU must be one of: 0.25 vCPU, 0.5 vCPU, 1 vCPU, 2 vCPU."
  }
}

variable "app_runner_memory" {
  description = "Memory configuration for App Runner"
  type        = string
  default     = "2 GB"
  
  validation {
    condition     = contains(["0.5 GB", "1 GB", "2 GB", "3 GB", "4 GB"], var.app_runner_memory)
    error_message = "Memory must be one of: 0.5 GB, 1 GB, 2 GB, 3 GB, 4 GB."
  }
}

variable "database_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t3.micro"
}

variable "database_allocated_storage" {
  description = "RDS allocated storage in GB"
  type        = number
  default     = 20
}

variable "enable_multi_az" {
  description = "Enable Multi-AZ for RDS"
  type        = bool
  default     = false
}

variable "backup_retention_period" {
  description = "RDS backup retention period in days"
  type        = number
  default     = 7
}

variable "enable_deletion_protection" {
  description = "Enable deletion protection for RDS"
  type        = bool
  default     = true
}

variable "cloudfront_price_class" {
  description = "CloudFront price class"
  type        = string
  default     = "PriceClass_100"
  
  validation {
    condition     = contains(["PriceClass_All", "PriceClass_200", "PriceClass_100"], var.cloudfront_price_class)
    error_message = "Price class must be one of: PriceClass_All, PriceClass_200, PriceClass_100."
  }
}

variable "auto_scaling_max_size" {
  description = "Maximum number of App Runner instances"
  type        = number
  default     = 10
}

variable "auto_scaling_min_size" {
  description = "Minimum number of App Runner instances"
  type        = number
  default     = 1
}

variable "auto_scaling_max_concurrency" {
  description = "Maximum concurrent requests per App Runner instance"
  type        = number
  default     = 100
}

variable "enable_container_insights" {
  description = "Enable CloudWatch Container Insights"
  type        = bool
  default     = true
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 14
}

# Environment-specific configurations
locals {
  environment_config = {
    production = {
      database_instance_class     = "db.t3.medium"
      database_allocated_storage  = 100
      enable_multi_az            = true
      backup_retention_period    = 30
      enable_deletion_protection = true
      app_runner_cpu            = "1 vCPU"
      app_runner_memory         = "2 GB"
      auto_scaling_max_size     = 10
      auto_scaling_min_size     = 2
      cloudfront_price_class    = "PriceClass_All"
      log_retention_days        = 30
    }
    staging = {
      database_instance_class     = "db.t3.small"
      database_allocated_storage  = 50
      enable_multi_az            = false
      backup_retention_period    = 7
      enable_deletion_protection = false
      app_runner_cpu            = "0.5 vCPU"
      app_runner_memory         = "1 GB"
      auto_scaling_max_size     = 5
      auto_scaling_min_size     = 1
      cloudfront_price_class    = "PriceClass_100"
      log_retention_days        = 14
    }
    development = {
      database_instance_class     = "db.t3.micro"
      database_allocated_storage  = 20
      enable_multi_az            = false
      backup_retention_period    = 1
      enable_deletion_protection = false
      app_runner_cpu            = "0.25 vCPU"
      app_runner_memory         = "0.5 GB"
      auto_scaling_max_size     = 3
      auto_scaling_min_size     = 1
      cloudfront_price_class    = "PriceClass_100"
      log_retention_days        = 7
    }
  }
  
  # Current environment configuration
  config = local.environment_config[var.environment]
}
