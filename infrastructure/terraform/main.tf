# Valura AI Infrastructure - Main Configuration
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  # Backend configuration - uncomment and configure for remote state
  # backend "s3" {
  #   bucket = "valura-terraform-state"
  #   key    = "infrastructure/terraform.tfstate"
  #   region = "me-south-1"
  # }
}

# Provider configurations for multi-region deployment
provider "aws" {
  alias  = "uae"
  region = var.uae_region
  
  default_tags {
    tags = {
      Project     = "Valura AI"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

provider "aws" {
  alias  = "india"
  region = var.india_region
  
  default_tags {
    tags = {
      Project     = "Valura AI"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Provider for CloudFront (must be us-east-1)
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
  
  default_tags {
    tags = {
      Project     = "Valura AI"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_availability_zones" "available" {
  provider = aws.uae
  state    = "available"
}

# Local values
locals {
  account_id = data.aws_caller_identity.current.account_id
  
  # Common tags
  common_tags = {
    Project     = "Valura AI"
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
  
  # Application configurations
  apps = {
    web = {
      name        = "web"
      subdomain   = "app"
      bucket_name = "valura-web-${var.environment}-${local.account_id}"
    }
    admin = {
      name        = "admin"
      subdomain   = "admin"
      bucket_name = "valura-admin-${var.environment}-${local.account_id}"
    }
    landing = {
      name        = "landing"
      subdomain   = ""
      bucket_name = "valura-landing-${var.environment}-${local.account_id}"
    }
  }
}

# SSL Certificate (must be in us-east-1 for CloudFront)
resource "aws_acm_certificate" "main" {
  provider          = aws.us_east_1
  domain_name       = var.domain_name
  validation_method = "DNS"
  
  subject_alternative_names = [
    "*.${var.domain_name}"
  ]
  
  lifecycle {
    create_before_destroy = true
  }
  
  tags = merge(local.common_tags, {
    Name = "valura-cert-${var.environment}"
  })
}

# Route53 Hosted Zone
resource "aws_route53_zone" "main" {
  provider = aws.uae
  name     = var.domain_name
  
  tags = merge(local.common_tags, {
    Name = "valura-zone-${var.environment}"
  })
}

# Certificate validation
resource "aws_route53_record" "cert_validation" {
  provider = aws.uae
  for_each = {
    for dvo in aws_acm_certificate.main.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }
  
  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = aws_route53_zone.main.zone_id
}

resource "aws_acm_certificate_validation" "main" {
  provider                = aws.us_east_1
  certificate_arn         = aws_acm_certificate.main.arn
  validation_record_fqdns = [for record in aws_route53_record.cert_validation : record.fqdn]
}

# S3 Buckets for static hosting
resource "aws_s3_bucket" "apps" {
  provider = aws.uae
  for_each = local.apps
  
  bucket = each.value.bucket_name
  
  tags = merge(local.common_tags, {
    Name        = each.value.bucket_name
    Application = each.value.name
  })
}

resource "aws_s3_bucket_versioning" "apps" {
  provider = aws.uae
  for_each = local.apps
  
  bucket = aws_s3_bucket.apps[each.key].id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_encryption" "apps" {
  provider = aws.uae
  for_each = local.apps
  
  bucket = aws_s3_bucket.apps[each.key].id
  
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }
}

resource "aws_s3_bucket_public_access_block" "apps" {
  provider = aws.uae
  for_each = local.apps
  
  bucket = aws_s3_bucket.apps[each.key].id
  
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# CloudFront Origin Access Control
resource "aws_cloudfront_origin_access_control" "apps" {
  for_each = local.apps
  
  name                              = "valura-${each.value.name}-oac-${var.environment}"
  description                       = "OAC for ${each.value.name} app"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

# CloudFront Distributions
resource "aws_cloudfront_distribution" "apps" {
  for_each = local.apps
  
  origin {
    domain_name              = aws_s3_bucket.apps[each.key].bucket_regional_domain_name
    origin_access_control_id = aws_cloudfront_origin_access_control.apps[each.key].id
    origin_id                = "S3-${each.value.bucket_name}"
  }
  
  enabled             = true
  is_ipv6_enabled     = true
  comment             = "Valura ${each.value.name} - ${var.environment}"
  default_root_object = "index.html"
  
  # Aliases
  aliases = each.value.subdomain == "" ? [
    var.domain_name,
    "www.${var.domain_name}"
  ] : ["${each.value.subdomain}.${var.domain_name}"]
  
  default_cache_behavior {
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${each.value.bucket_name}"
    
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    
    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
    compress               = true
  }
  
  # Custom error responses for SPA
  custom_error_response {
    error_code         = 403
    response_code      = 200
    response_page_path = "/index.html"
  }
  
  custom_error_response {
    error_code         = 404
    response_code      = 200
    response_page_path = "/index.html"
  }
  
  price_class = var.environment == "production" ? "PriceClass_All" : "PriceClass_100"
  
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  
  viewer_certificate {
    acm_certificate_arn      = aws_acm_certificate_validation.main.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }
  
  tags = merge(local.common_tags, {
    Name        = "valura-${each.value.name}-cf-${var.environment}"
    Application = each.value.name
  })
}

# S3 Bucket Policies for CloudFront
resource "aws_s3_bucket_policy" "apps" {
  provider = aws.uae
  for_each = local.apps
  
  bucket = aws_s3_bucket.apps[each.key].id
  
  policy = jsonencode({
    Statement = [
      {
        Sid    = "AllowCloudFrontServicePrincipal"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${aws_s3_bucket.apps[each.key].arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = aws_cloudfront_distribution.apps[each.key].arn
          }
        }
      }
    ]
  })
}

# Route53 Records
resource "aws_route53_record" "apps" {
  provider = aws.uae
  for_each = local.apps
  
  zone_id = aws_route53_zone.main.zone_id
  name    = each.value.subdomain == "" ? var.domain_name : "${each.value.subdomain}.${var.domain_name}"
  type    = "A"
  
  alias {
    name                   = aws_cloudfront_distribution.apps[each.key].domain_name
    zone_id                = aws_cloudfront_distribution.apps[each.key].hosted_zone_id
    evaluate_target_health = false
  }
}

# WWW redirect for landing page
resource "aws_route53_record" "www_redirect" {
  provider = aws.uae
  count    = var.environment == "production" ? 1 : 0
  
  zone_id = aws_route53_zone.main.zone_id
  name    = "www.${var.domain_name}"
  type    = "A"
  
  alias {
    name                   = aws_cloudfront_distribution.apps["landing"].domain_name
    zone_id                = aws_cloudfront_distribution.apps["landing"].hosted_zone_id
    evaluate_target_health = false
  }
}
