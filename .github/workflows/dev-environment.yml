name: Dev Environment CI/CD

on:
  push:
    branches: [dev]
  pull_request:
    branches: [dev]

env:
  NODE_VERSION: '18.x'
  PNPM_VERSION: '8.x'
  AWS_REGION: 'ap-south-1'

jobs:
  # Detect changes in monorepo
  detect-changes:
    runs-on: ubuntu-latest
    permissions:
      id-token: write  # Required for OIDC
      contents: read
    outputs:
      web: ${{ steps.changes.outputs.web }}
      admin: ${{ steps.changes.outputs.admin }}
      api: ${{ steps.changes.outputs.api }}
      landing: ${{ steps.changes.outputs.landing }}
      packages: ${{ steps.changes.outputs.packages }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            web:
              - 'apps/web/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            admin:
              - 'apps/admin/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            api:
              - 'apps/api/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            landing:
              - 'apps/landing/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            packages:
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'

  # Build and test shared packages
  packages:
    needs: detect-changes
    if: needs.detect-changes.outputs.packages == 'true'
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*"
      
      - name: Test packages
        run: pnpm run test --filter="./packages/*" || echo "No tests found"
      
      - name: Lint packages
        run: pnpm run lint --filter="./packages/*" || echo "No lint configured"

  # Web App Development Deployment
  web-app-dev:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.web == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment: development
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*" || echo "No packages to build"
      
      - name: Lint web app
        run: pnpm run lint --filter=web || echo "No lint configured"
      
      - name: Test web app
        run: pnpm run test --filter=web || echo "No tests found"
        env:
          CI: true
      
      - name: Build web app
        run: pnpm run build --filter=web
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL_DEV }}
          NEXT_PUBLIC_USER_POOL_ID: ${{ secrets.NEXT_PUBLIC_USER_POOL_ID }}
          NEXT_PUBLIC_USER_POOL_CLIENT_ID: ${{ secrets.NEXT_PUBLIC_USER_POOL_CLIENT_ID }}
          NEXT_PUBLIC_PERSONA_TEMPLATE_ID: ${{ secrets.NEXT_PUBLIC_PERSONA_TEMPLATE_ID }}
          NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID: ${{ secrets.NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID }}
          NEXT_PUBLIC_POSTHOG_KEY: ${{ secrets.NEXT_PUBLIC_POSTHOG_KEY }}
          NEXT_PUBLIC_POSTHOG_HOST: ${{ secrets.NEXT_PUBLIC_POSTHOG_HOST }}
      
      - name: Configure AWS credentials (OIDC)
        if: ${{ secrets.AWS_ROLE_ARN_INDIA }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN_INDIA }}
          aws-region: ${{ env.AWS_REGION }}
          role-session-name: GitHubActions-WebApp-Dev

      - name: Configure AWS credentials (Access Keys)
        if: ${{ !secrets.AWS_ROLE_ARN_INDIA && secrets.AWS_ACCESS_KEY_ID_INDIA }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_INDIA }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_INDIA }}
          aws-session-token: ${{ secrets.AWS_SESSION_TOKEN_INDIA }}  # For temporary credentials
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Deploy to AWS Amplify (Development)
        run: |
          # Install Amplify CLI
          npm install -g @aws-amplify/cli
          
          # Deploy to Amplify
          amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_WEB_DEV }} --yes || echo "Amplify deployment failed - check app ID"
        working-directory: apps/web

  # Admin Panel Development Deployment
  admin-panel-dev:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.admin == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment: development
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*" || echo "No packages to build"
      
      - name: Lint admin panel
        run: pnpm run lint --filter=@repo/admin || echo "No lint configured"
      
      - name: Test admin panel
        run: pnpm run test --filter=@repo/admin || echo "No tests found"
        env:
          CI: true
      
      - name: Build admin panel
        run: pnpm run build --filter=@repo/admin
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL_DEV }}
          PORT: ${{ secrets.ADMIN_PORT || '3002' }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_INDIA }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_INDIA }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Deploy to AWS Amplify (Development)
        run: |
          npm install -g @aws-amplify/cli
          amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_ADMIN_DEV }} --yes || echo "Amplify deployment failed - check app ID"
        working-directory: apps/admin

  # Landing Page Development Deployment
  landing-page-dev:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.landing == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment: development
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*" || echo "No packages to build"
      
      - name: Lint landing page
        run: pnpm run lint --filter=@repo/landing || echo "No lint configured"
      
      - name: Build landing page
        run: pnpm run build --filter=@repo/landing
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_INDIA }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_INDIA }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Deploy to AWS Amplify (Development)
        run: |
          npm install -g @aws-amplify/cli
          amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_LANDING_DEV }} --yes || echo "Amplify deployment failed - check app ID"
        working-directory: apps/landing

  # API Server Development Deployment
  api-server-dev:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.api == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment: development
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*" || echo "No packages to build"
      
      - name: Lint API server
        run: pnpm run lint --filter=@repo/api || echo "No lint configured"
      
      - name: Test API server
        run: pnpm run test --filter=@repo/api || echo "No tests found"
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          JWT_SECRET: test-secret
          NODE_ENV: test
      
      - name: Build API server
        run: pnpm run build --filter=@repo/api
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_INDIA }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_INDIA }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Build and push Docker image
        run: |
          # Login to ECR
          aws ecr get-login-password --region ${{ env.AWS_REGION }} | docker login --username AWS --password-stdin ${{ secrets.ECR_REGISTRY_INDIA }}
          
          # Build image
          docker build -t valura-api-dev:${{ github.sha }} -f apps/api/Dockerfile .
          
          # Tag and push
          docker tag valura-api-dev:${{ github.sha }} ${{ secrets.ECR_REGISTRY_INDIA }}/valura-api-dev:${{ github.sha }}
          docker tag valura-api-dev:${{ github.sha }} ${{ secrets.ECR_REGISTRY_INDIA }}/valura-api-dev:latest
          docker push ${{ secrets.ECR_REGISTRY_INDIA }}/valura-api-dev:${{ github.sha }} || echo "ECR push failed - check repository exists"
          docker push ${{ secrets.ECR_REGISTRY_INDIA }}/valura-api-dev:latest || echo "ECR push failed - check repository exists"
      
      - name: Deploy to App Runner (Development)
        run: |
          # Deploy to App Runner (if service exists)
          if [ -n "${{ secrets.APP_RUNNER_SERVICE_ARN_DEV }}" ]; then
            aws apprunner start-deployment --service-arn ${{ secrets.APP_RUNNER_SERVICE_ARN_DEV }} || echo "App Runner deployment failed - check service exists"
          else
            echo "App Runner service ARN not configured - skipping deployment"
          fi
