name: CI/CD Pipeline - UAE Production

on:
  push:
    branches: [prod, stg, dev]
  pull_request:
    branches: [prod, stg, dev]

env:
  NODE_VERSION: '18.x'
  PYTHON_VERSION: '3.11'
  PNPM_VERSION: '8.x'
  # UAE Production and Staging use me-central-1 (UAE)
  UAE_AWS_REGION: 'me-central-1'
  # India Development uses ap-south-1 (Mumbai)
  INDIA_AWS_REGION: 'ap-south-1'

jobs:
  # Detect changes in monorepo
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      web: ${{ steps.changes.outputs.web }}
      admin: ${{ steps.changes.outputs.admin }}
      api: ${{ steps.changes.outputs.api }}
      mobile: ${{ steps.changes.outputs.mobile }}
      landing: ${{ steps.changes.outputs.landing }}
      microservice: ${{ steps.changes.outputs.microservice }}
      packages: ${{ steps.changes.outputs.packages }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            web:
              - 'apps/web/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            admin:
              - 'apps/admin/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            api:
              - 'apps/api/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            mobile:
              - 'apps/mobile/**'
              - 'package.json'
            landing:
              - 'apps/landing/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            microservice:
              - 'apps/microservice/**'
            packages:
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'

  # Build and test shared packages
  packages:
    needs: detect-changes
    if: needs.detect-changes.outputs.packages == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*"
      
      - name: Test packages
        run: pnpm run test --filter="./packages/*"
      
      - name: Lint packages
        run: pnpm run lint --filter="./packages/*"

  # Web App CI/CD with UAE/India deployment
  web-app:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.web == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref == 'refs/heads/prod' && 'production' || github.ref == 'refs/heads/stg' && 'staging' || 'development' }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*"
      
      - name: Lint web app
        run: pnpm run lint --filter=web
      
      - name: Test web app
        run: pnpm run test --filter=web
        env:
          CI: true
      
      - name: Build web app
        run: pnpm run build --filter=web
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
          NEXT_PUBLIC_USER_POOL_ID: ${{ secrets.NEXT_PUBLIC_USER_POOL_ID }}
          NEXT_PUBLIC_USER_POOL_CLIENT_ID: ${{ secrets.NEXT_PUBLIC_USER_POOL_CLIENT_ID }}
          NEXT_PUBLIC_PERSONA_TEMPLATE_ID: ${{ secrets.NEXT_PUBLIC_PERSONA_TEMPLATE_ID }}
          NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID: ${{ secrets.NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID }}
          NEXT_PUBLIC_POSTHOG_KEY: ${{ secrets.NEXT_PUBLIC_POSTHOG_KEY }}
          NEXT_PUBLIC_POSTHOG_HOST: ${{ secrets.NEXT_PUBLIC_POSTHOG_HOST }}
      
      # Development: Deploy to Amplify in India
      - name: Deploy to AWS Amplify (Development - India)
        if: github.ref == 'refs/heads/dev'
        run: |
          npm install -g @aws-amplify/cli
          
          # Configure AWS for India region
          export AWS_REGION=${{ env.INDIA_AWS_REGION }}
          aws configure set region ${{ env.INDIA_AWS_REGION }}
          
          amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_WEB_DEV }} --yes
        working-directory: apps/web
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_INDIA }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_INDIA }}
      
      # Production: Deploy to S3 + CloudFront in UAE
      - name: Deploy to S3 + CloudFront (Production - UAE)
        if: github.ref == 'refs/heads/prod'
        run: |
          # Configure AWS for UAE region
          export AWS_REGION=${{ env.UAE_AWS_REGION }}
          aws configure set region ${{ env.UAE_AWS_REGION }}

          # Sync to S3
          aws s3 sync apps/web/out s3://${{ secrets.S3_BUCKET_WEB_PROD }} --delete --cache-control "public, max-age=31536000, immutable" --exclude "*.html"
          aws s3 sync apps/web/out s3://${{ secrets.S3_BUCKET_WEB_PROD }} --delete --cache-control "public, max-age=0, must-revalidate" --include "*.html"

          # Invalidate CloudFront
          aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID_WEB }} --paths "/*"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_UAE }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_UAE }}
      
      # Staging: Deploy to S3 + CloudFront in UAE
      - name: Deploy to S3 + CloudFront (Staging - UAE)
        if: github.ref == 'refs/heads/stg'
        run: |
          export AWS_REGION=${{ env.UAE_AWS_REGION }}
          aws configure set region ${{ env.UAE_AWS_REGION }}
          
          aws s3 sync apps/web/out s3://${{ secrets.S3_BUCKET_WEB_STAGING }} --delete --cache-control "public, max-age=31536000, immutable" --exclude "*.html"
          aws s3 sync apps/web/out s3://${{ secrets.S3_BUCKET_WEB_STAGING }} --delete --cache-control "public, max-age=0, must-revalidate" --include "*.html"
          
          aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID_WEB_STAGING }} --paths "/*"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_UAE }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_UAE }}

  # Admin Panel CI/CD with similar UAE/India deployment
  admin-panel:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.admin == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment: 
      name: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*"
      
      - name: Lint admin panel
        run: pnpm run lint --filter=@repo/admin
      
      - name: Test admin panel
        run: pnpm run test --filter=@repo/admin
        env:
          CI: true
      
      - name: Build admin panel
        run: pnpm run build --filter=@repo/admin
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
          PORT: ${{ secrets.ADMIN_PORT }}
      
      # Development: Deploy to Amplify in India
      - name: Deploy to AWS Amplify (Development - India)
        if: github.ref == 'refs/heads/develop'
        run: |
          npm install -g @aws-amplify/cli
          export AWS_REGION=${{ env.INDIA_AWS_REGION }}
          aws configure set region ${{ env.INDIA_AWS_REGION }}
          amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_ADMIN_DEV }} --yes
        working-directory: apps/admin
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_INDIA }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_INDIA }}
      
      # Production: Deploy to S3 + CloudFront in UAE/Bahrain
      - name: Deploy to S3 + CloudFront (Production - UAE)
        if: github.ref == 'refs/heads/main'
        run: |
          export AWS_REGION=${{ env.UAE_AWS_REGION }}
          aws configure set region ${{ env.UAE_AWS_REGION }}
          
          aws s3 sync apps/admin/out s3://${{ secrets.S3_BUCKET_ADMIN_PROD }} --delete --cache-control "public, max-age=31536000, immutable" --exclude "*.html"
          aws s3 sync apps/admin/out s3://${{ secrets.S3_BUCKET_ADMIN_PROD }} --delete --cache-control "public, max-age=0, must-revalidate" --include "*.html"
          
          aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID_ADMIN }} --paths "/*"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_UAE }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_UAE }}

  # Mobile App CI/CD (EAS + AWS backend)
  mobile-app:
    needs: detect-changes
    if: needs.detect-changes.outputs.mobile == 'true'
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'

      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: yarn install --frozen-lockfile
        working-directory: apps/mobile

      - name: Lint mobile app
        run: yarn lint
        working-directory: apps/mobile

      - name: Create EAS update (Development)
        if: github.ref == 'refs/heads/develop'
        run: |
          cd apps/mobile
          # Configure for India backend
          export EXPO_PUBLIC_API_URL="${{ secrets.API_URL_DEV_INDIA }}"
          export EXPO_PUBLIC_AWS_REGION="${{ env.INDIA_AWS_REGION }}"
          eas update --branch development --message "Development update: ${{ github.event.head_commit.message }}"

      - name: Create EAS update (Staging)
        if: github.ref == 'refs/heads/staging'
        run: |
          cd apps/mobile
          # Configure for UAE backend
          export EXPO_PUBLIC_API_URL="${{ secrets.API_URL_STAGING_UAE }}"
          export EXPO_PUBLIC_AWS_REGION="${{ env.UAE_AWS_REGION }}"
          eas update --branch staging --message "Staging update: ${{ github.event.head_commit.message }}"

      - name: Build and submit to stores (Production)
        if: github.ref == 'refs/heads/main'
        run: |
          cd apps/mobile
          # Configure for UAE backend
          export EXPO_PUBLIC_API_URL="${{ secrets.API_URL_PROD_UAE }}"
          export EXPO_PUBLIC_AWS_REGION="${{ env.UAE_AWS_REGION }}"

          # Build for both platforms
          eas build --platform all --non-interactive

          # Submit to app stores (optional - can be manual)
          # eas submit --platform ios --non-interactive
          # eas submit --platform android --non-interactive
