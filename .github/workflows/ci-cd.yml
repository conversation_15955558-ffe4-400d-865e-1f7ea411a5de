name: CI/CD Pipeline

on:
  push:
    branches: [main, develop, staging]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18.x'
  PYTHON_VERSION: '3.11'
  PNPM_VERSION: '8.x'

jobs:
  # Detect changes in monorepo
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      web: ${{ steps.changes.outputs.web }}
      admin: ${{ steps.changes.outputs.admin }}
      api: ${{ steps.changes.outputs.api }}
      mobile: ${{ steps.changes.outputs.mobile }}
      landing: ${{ steps.changes.outputs.landing }}
      microservice: ${{ steps.changes.outputs.microservice }}
      packages: ${{ steps.changes.outputs.packages }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            web:
              - 'apps/web/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            admin:
              - 'apps/admin/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            api:
              - 'apps/api/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            mobile:
              - 'apps/mobile/**'
              - 'package.json'
            landing:
              - 'apps/landing/**'
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'
            microservice:
              - 'apps/microservice/**'
            packages:
              - 'packages/**'
              - 'package.json'
              - 'pnpm-lock.yaml'

  # Build and test shared packages
  packages:
    needs: detect-changes
    if: needs.detect-changes.outputs.packages == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*"
      
      - name: Test packages
        run: pnpm run test --filter="./packages/*"
      
      - name: Lint packages
        run: pnpm run lint --filter="./packages/*"

  # Web App CI/CD
  web-app:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.web == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment: 
      name: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*"
      
      - name: Lint web app
        run: pnpm run lint --filter=web
      
      - name: Test web app
        run: pnpm run test --filter=web
        env:
          CI: true
      
      - name: Build web app
        run: pnpm run build --filter=web
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
          NEXT_PUBLIC_USER_POOL_ID: ${{ secrets.NEXT_PUBLIC_USER_POOL_ID }}
          NEXT_PUBLIC_USER_POOL_CLIENT_ID: ${{ secrets.NEXT_PUBLIC_USER_POOL_CLIENT_ID }}
          NEXT_PUBLIC_PERSONA_TEMPLATE_ID: ${{ secrets.NEXT_PUBLIC_PERSONA_TEMPLATE_ID }}
          NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID: ${{ secrets.NEXT_PUBLIC_PERSONA_ENVIRONMENT_ID }}
          NEXT_PUBLIC_POSTHOG_KEY: ${{ secrets.NEXT_PUBLIC_POSTHOG_KEY }}
          NEXT_PUBLIC_POSTHOG_HOST: ${{ secrets.NEXT_PUBLIC_POSTHOG_HOST }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Deploy to AWS Amplify
        run: |
          # Install Amplify CLI
          npm install -g @aws-amplify/cli
          
          # Deploy based on branch
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_WEB_PROD }} --yes
          elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_WEB_STAGING }} --yes
          else
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_WEB_DEV }} --yes
          fi
        working-directory: apps/web

  # Admin Panel CI/CD
  admin-panel:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.admin == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment: 
      name: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*"
      
      - name: Lint admin panel
        run: pnpm run lint --filter=@repo/admin
      
      - name: Test admin panel
        run: pnpm run test --filter=@repo/admin
        env:
          CI: true
      
      - name: Build admin panel
        run: pnpm run build --filter=@repo/admin
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
          PORT: ${{ secrets.ADMIN_PORT }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Deploy to AWS Amplify
        run: |
          npm install -g @aws-amplify/cli
          
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_ADMIN_PROD }} --yes
          elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_ADMIN_STAGING }} --yes
          else
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_ADMIN_DEV }} --yes
          fi
        working-directory: apps/admin

  # Landing Page CI/CD
  landing-page:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.landing == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment: 
      name: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build packages
        run: pnpm run build --filter="./packages/*"
      
      - name: Lint landing page
        run: pnpm run lint --filter=@repo/landing
      
      - name: Build landing page
        run: pnpm run build --filter=@repo/landing
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Deploy to AWS Amplify
        run: |
          npm install -g @aws-amplify/cli
          
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_LANDING_PROD }} --yes
          elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_LANDING_STAGING }} --yes
          else
            amplify publish --appId ${{ secrets.AMPLIFY_APP_ID_LANDING_DEV }} --yes
          fi
        working-directory: apps/landing

  # API Server CI/CD
  api-server:
    needs: [detect-changes, packages]
    if: always() && (needs.detect-changes.outputs.api == 'true' || needs.detect-changes.outputs.packages == 'true')
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build packages
        run: pnpm run build --filter="./packages/*"

      - name: Lint API server
        run: pnpm run lint --filter=@repo/api

      - name: Test API server
        run: pnpm run test --filter=@repo/api
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          JWT_SECRET: test-secret
          NODE_ENV: test

      - name: Build API server
        run: pnpm run build --filter=@repo/api

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Build and push Docker image
        run: |
          # Login to ECR
          aws ecr get-login-password --region ${{ secrets.AWS_REGION }} | docker login --username AWS --password-stdin ${{ secrets.ECR_REGISTRY }}

          # Build image
          docker build -t valura-api:${{ github.sha }} -f apps/api/Dockerfile .

          # Tag and push
          docker tag valura-api:${{ github.sha }} ${{ secrets.ECR_REGISTRY }}/valura-api:${{ github.sha }}
          docker tag valura-api:${{ github.sha }} ${{ secrets.ECR_REGISTRY }}/valura-api:latest
          docker push ${{ secrets.ECR_REGISTRY }}/valura-api:${{ github.sha }}
          docker push ${{ secrets.ECR_REGISTRY }}/valura-api:latest

      - name: Deploy to AWS App Runner
        run: |
          # Update App Runner service
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            SERVICE_ARN="${{ secrets.APP_RUNNER_SERVICE_ARN_PROD }}"
          elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
            SERVICE_ARN="${{ secrets.APP_RUNNER_SERVICE_ARN_STAGING }}"
          else
            SERVICE_ARN="${{ secrets.APP_RUNNER_SERVICE_ARN_DEV }}"
          fi

          aws apprunner start-deployment --service-arn $SERVICE_ARN

      - name: Run database migrations
        run: |
          # Install dependencies and run migrations
          cd apps/api
          pnpm install

          # Set environment-specific database URL
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            export DATABASE_URL="${{ secrets.DATABASE_URL_PROD }}"
          elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
            export DATABASE_URL="${{ secrets.DATABASE_URL_STAGING }}"
          else
            export DATABASE_URL="${{ secrets.DATABASE_URL_DEV }}"
          fi

          # Run Prisma migrations
          npx prisma migrate deploy

          # Run TimescaleDB migrations
          pnpm run timescale:migrate:up

  # Mobile App CI/CD
  mobile-app:
    needs: detect-changes
    if: needs.detect-changes.outputs.mobile == 'true'
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'

      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: yarn install --frozen-lockfile
        working-directory: apps/mobile

      - name: Lint mobile app
        run: yarn lint
        working-directory: apps/mobile

      - name: Create EAS update (Development)
        if: github.ref == 'refs/heads/develop'
        run: |
          cd apps/mobile
          eas update --branch development --message "Development update: ${{ github.event.head_commit.message }}"

      - name: Create EAS update (Staging)
        if: github.ref == 'refs/heads/staging'
        run: |
          cd apps/mobile
          eas update --branch staging --message "Staging update: ${{ github.event.head_commit.message }}"

      - name: Build and submit to stores (Production)
        if: github.ref == 'refs/heads/main'
        run: |
          cd apps/mobile
          # Build for both platforms
          eas build --platform all --non-interactive

          # Submit to app stores (optional - can be manual)
          # eas submit --platform ios --non-interactive
          # eas submit --platform android --non-interactive
