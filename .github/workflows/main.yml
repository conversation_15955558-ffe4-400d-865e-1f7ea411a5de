# Legacy mobile workflow - will be replaced by comprehensive CI/CD
name: Legacy Mobile EAS Update

on:
  push:
    branches:
      - feature/uae-pass
    paths:
      - 'apps/mobile/**'

jobs:
  update:
    concurrency:
      group: update-eas-${{ github.ref }}
      cancel-in-progress: true
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install

      - name: Make scripts executable
        run: chmod +x ./github_action_file.sh

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
          cache: true

      - name: Verify EAS authentication
        run: eas whoami

      - name: Check project info
        run: eas project:info

      - name: Prebuild & generate gradlew
        run: ./github_action_file.sh

      - name: Create EAS update
        run: eas update --auto --non-interactive
